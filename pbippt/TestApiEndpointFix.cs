using System;
using System.Threading.Tasks;
using PBIppt.ApiClient;
using PBIppt.Models;

namespace PBIppt
{
    /// <summary>
    /// 测试API端点修复
    /// </summary>
    public class TestApiEndpointFix
    {
        private readonly BatteryImageApiClient _apiClient;

        public TestApiEndpointFix()
        {
            _apiClient = new BatteryImageApiClient("http://localhost:82");
        }

        /// <summary>
        /// 测试修复后的API端点
        /// </summary>
        public async Task<string> TestFixedApiEndpoint()
        {
            var results = new System.Text.StringBuilder();
            results.AppendLine("=== 测试API端点修复 ===");

            try
            {
                // 1. 登录
                results.AppendLine("步骤1: 登录");
                var loginResult = await _apiClient.LoginAsync("071716", "Lpg';lkj");
                if (loginResult.Success)
                {
                    results.AppendLine("✓ 登录成功");
                }
                else
                {
                    results.AppendLine($"✗ 登录失败: {loginResult.ErrorMessage}");
                    return results.ToString();
                }

                // 2. 测试修复后的API端点
                results.AppendLine("步骤2: 测试修复后的API端点");
                var testProgress = new TestProgress 
                { 
                    Id = 0,
                    SampleNo = "1907295047497265154", // 使用已知存在的安全测试ID
                    SampleType = "电池"
                };

                var response = await _apiClient.GetPictureByTestProgressAsync(testProgress, "all");
                
                if (response != null && response.Header != null && response.Header.Count > 0)
                {
                    results.AppendLine("✓ API端点修复成功");
                    results.AppendLine($"  - 表头行数: {response.Header.Count}");
                    results.AppendLine($"  - 数据行数: {response.BodyDataList?.Count ?? 0}");
                    results.AppendLine($"  - 样品数量: {response.AllSampleNoList?.Count ?? 0}");
                    results.AppendLine($"  - 图片类型数量: {response.AllPicTypeList?.Count ?? 0}");
                }
                else
                {
                    results.AppendLine("✗ API端点仍有问题，返回数据为空");
                }

                // 3. 测试转换方法
                results.AppendLine("步骤3: 测试响应转换");
                var rawResponse = await _apiClient.GetPictureBySafetyTestIdsAsync("1907295047497265154", "all");
                if (rawResponse != null && rawResponse.Success)
                {
                    results.AppendLine("✓ 安全测试API调用成功");
                    results.AppendLine($"  - 响应消息: {rawResponse.Message}");
                }
                else
                {
                    results.AppendLine("✗ 安全测试API调用失败");
                }

            }
            catch (Exception ex)
            {
                results.AppendLine($"✗ 测试过程中发生异常: {ex.Message}");
                results.AppendLine($"  异常类型: {ex.GetType().Name}");
                if (ex.InnerException != null)
                {
                    results.AppendLine($"  内部异常: {ex.InnerException.Message}");
                }
            }

            return results.ToString();
        }

        /// <summary>
        /// 测试表格创建修复
        /// </summary>
        public string TestTableCreationFix()
        {
            var results = new System.Text.StringBuilder();
            results.AppendLine("=== 测试表格创建修复 ===");

            try
            {
                // 模拟表格创建测试
                results.AppendLine("步骤1: 检查PowerPoint应用程序");
                var app = Globals.ThisAddIn.Application;
                if (app != null && app.Presentations.Count > 0)
                {
                    results.AppendLine("✓ PowerPoint应用程序可用");
                    
                    var presentation = app.ActivePresentation;
                    if (presentation != null && presentation.Slides.Count > 0)
                    {
                        results.AppendLine("✓ 活动演示文稿可用");
                        results.AppendLine("✓ 表格创建修复已应用（添加了Shape ID验证）");
                    }
                    else
                    {
                        results.AppendLine("⚠ 没有活动演示文稿或幻灯片");
                    }
                }
                else
                {
                    results.AppendLine("⚠ PowerPoint应用程序不可用");
                }
            }
            catch (Exception ex)
            {
                results.AppendLine($"✗ 表格创建测试失败: {ex.Message}");
            }

            return results.ToString();
        }
    }
}
