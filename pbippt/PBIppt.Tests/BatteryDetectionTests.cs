using System;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using PBIppt.ApiClient;
using PBIppt.ImageProcessing;

namespace PBIppt.Tests
{
    /// <summary>
    /// 电芯检测功能测试
    /// </summary>
    [TestClass]
    public class BatteryDetectionTests
    {
        /// <summary>
        /// 测试电芯检测API客户端连接
        /// </summary>
        [TestMethod]
        public async Task TestBatteryDetectionApiConnection()
        {
            // Arrange
            var apiClient = new BatteryDetectionApiClient();

            // Act
            var result = await apiClient.TestConnectionAsync();

            // Assert
            // 注意：这个测试可能会失败，因为需要实际的API服务器
            // 在实际环境中运行时应该成功
            Console.WriteLine($"API连接测试结果: {result}");
        }

        /// <summary>
        /// 测试坐标转换逻辑
        /// </summary>
        [TestMethod]
        public void TestCoordinateConversion()
        {
            // 这个测试验证坐标转换的基本逻辑
            // 实际的转换在PowerPointCropHelper中进行

            // Arrange
            int apiLeft = 100, apiTop = 200, apiRight = 300, apiBottom = 400;
            float originalWidth = 1000, originalHeight = 800;
            float currentWidth = 500, currentHeight = 400;

            // Act - 模拟转换逻辑
            float leftRatio = (float)apiLeft / originalWidth;
            float topRatio = (float)apiTop / originalHeight;
            float rightRatio = (float)apiRight / originalWidth;
            float bottomRatio = (float)apiBottom / originalHeight;

            float pptLeft = leftRatio * currentWidth;
            float pptTop = topRatio * currentHeight;
            float pptRight = rightRatio * currentWidth;
            float pptBottom = bottomRatio * currentHeight;

            // Assert
            Assert.AreEqual(50f, pptLeft, 0.1f, "左边坐标转换错误");
            Assert.AreEqual(100f, pptTop, 0.1f, "上边坐标转换错误");
            Assert.AreEqual(150f, pptRight, 0.1f, "右边坐标转换错误");
            Assert.AreEqual(200f, pptBottom, 0.1f, "下边坐标转换错误");

            Console.WriteLine($"坐标转换测试通过:");
            Console.WriteLine($"API坐标: ({apiLeft}, {apiTop}, {apiRight}, {apiBottom})");
            Console.WriteLine($"PPT坐标: ({pptLeft:F1}, {pptTop:F1}, {pptRight:F1}, {pptBottom:F1})");
        }

        /// <summary>
        /// 测试电芯检测处理器创建
        /// </summary>
        [TestMethod]
        public void TestBatteryDetectionProcessorCreation()
        {
            // Arrange & Act
            var processor = new BatteryDetectionProcessor();

            // Assert
            Assert.IsNotNull(processor, "电芯检测处理器创建失败");
            
            var apiInfo = processor.GetApiClientInfo();
            Assert.IsNotNull(apiInfo, "API客户端信息获取失败");
            
            Console.WriteLine($"电芯检测处理器创建成功");
            Console.WriteLine($"API信息: {apiInfo}");
        }

        /// <summary>
        /// 测试模拟检测响应解析
        /// </summary>
        [TestMethod]
        public void TestDetectionResponseParsing()
        {
            // Arrange
            var mockResponse = @"{
                ""results"": [
                    {
                        ""image_id"": ""test_image_001"",
                        ""CropLeft"": 1590,
                        ""CropTop"": 2696,
                        ""CropRight"": 110,
                        ""CropBottom"": 875
                    }
                ]
            }";

            // Act - 模拟解析逻辑
            try
            {
                var jsonResponse = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(mockResponse);
                
                // Assert
                Assert.IsNotNull(jsonResponse.results, "解析结果为空");
                
                var firstResult = jsonResponse.results[0];
                Assert.AreEqual("test_image_001", firstResult.image_id.ToString(), "图片ID解析错误");
                Assert.AreEqual(1590, (int)firstResult.CropLeft, "CropLeft解析错误");
                Assert.AreEqual(2696, (int)firstResult.CropTop, "CropTop解析错误");
                Assert.AreEqual(110, (int)firstResult.CropRight, "CropRight解析错误");
                Assert.AreEqual(875, (int)firstResult.CropBottom, "CropBottom解析错误");

                Console.WriteLine("检测响应解析测试通过");
                Console.WriteLine($"解析结果: ImageId={firstResult.image_id}, 裁剪区域=({firstResult.CropLeft}, {firstResult.CropTop}, {firstResult.CropRight}, {firstResult.CropBottom})");
            }
            catch (Exception ex)
            {
                Assert.Fail($"解析检测响应失败: {ex.Message}");
            }
        }
    }
}
