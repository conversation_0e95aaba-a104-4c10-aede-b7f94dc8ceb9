# 测试项目

此项目包含PowerPoint电池图片表格插件的单元测试和集成测试。

## 测试目录结构

```
PBIppt.Tests/
├── ApiClientTests/       # API客户端测试
│   ├── BatteryApiClientTests.cs
│   └── BatteryImageApiClientTests.cs
├── UITests/              # UI组件测试
│   ├── LoginFormTests.cs
│   ├── QueryFormTests.cs
│   └── TableGeneratorTests.cs
└── IntegrationTests/     # 集成测试
    ├── TableGenerationWorkflowTests.cs
    └── BatteryDetectionWorkflowTests.cs
```

## 运行测试

使用Visual Studio测试资源管理器运行测试，或使用以下命令行：

```
dotnet test PBIppt.Tests
```

## 测试覆盖率

- API客户端测试：测试API调用、数据解析和错误处理
- UI测试：测试表单验证、事件处理和UI逻辑
- 集成测试：测试完整工作流程，包括用户登录、查询参数和表格生成 