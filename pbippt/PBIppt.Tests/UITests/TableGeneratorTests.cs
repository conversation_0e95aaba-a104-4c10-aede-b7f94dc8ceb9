using System;
using System.Collections.Generic;
using System.Drawing;
using Microsoft.Office.Interop.PowerPoint;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using PBIppt.Models;
using PBIppt.UI;
using PowerPoint = Microsoft.Office.Interop.PowerPoint;
using Office = Microsoft.Office.Core;

namespace PBIppt.Tests.UITests
{
    [TestClass]
    public class TableGeneratorTests
    {
        private Mock<PowerPoint.Slide> _mockSlide;
        private Mock<PowerPoint.Shapes> _mockShapes;
        private Mock<PowerPoint.Shape> _mockTableShape;
        private Mock<PowerPoint.Table> _mockTable;
        private TableGenerator _tableGenerator;

        [TestInitialize]
        public void Initialize()
        {
            // 创建模拟对象
            _mockSlide = new Mock<PowerPoint.Slide>();
            _mockShapes = new Mock<PowerPoint.Shapes>();
            _mockTableShape = new Mock<PowerPoint.Shape>();
            _mockTable = new Mock<PowerPoint.Table>();

            // 设置模拟对象的行为
            _mockSlide.Setup(s => s.Shapes).Returns(_mockShapes.Object);
            _mockShapes.Setup(s => s.AddTable(It.IsAny<int>(), It.IsAny<int>(), 
                It.IsAny<float>(), It.IsAny<float>(), It.IsAny<float>(), It.IsAny<float>()))
                .Returns(_mockTableShape.Object);
            _mockTableShape.Setup(s => s.Table).Returns(_mockTable.Object);

            // 创建要测试的对象
            _tableGenerator = new TableGenerator(_mockSlide.Object);
        }

        [TestMethod]
        public void GenerateBatteryImageTable_ValidData_CreatesTable()
        {
            // 准备
            var responseData = new BatteryImageResponse
            {
                Header = new List<List<string>>
                {
                    new List<string> { "电池编号", "1号", "2号" },
                    new List<string> { "图片类型", "处理前", "处理后" }
                },
                BodyDataList = new List<Dictionary<string, object>>
                {
                    new Dictionary<string, object>
                    {
                        { "电池编号", "样品1" },
                        { "1号_处理前", new byte[] { 1, 2, 3 } },
                        { "1号_处理后", new byte[] { 4, 5, 6 } }
                    }
                }
            };

            // 设置更多模拟对象的行为
            SetupTableMocks(2, 3, 1);

            // 执行
            var result = _tableGenerator.GenerateBatteryImageTable(responseData);

            // 验证
            Assert.IsNotNull(result);
            _mockShapes.Verify(s => s.AddTable(3, 3, It.IsAny<float>(), It.IsAny<float>(), 
                It.IsAny<float>(), It.IsAny<float>()), Times.Once);
        }

        private void SetupTableMocks(int headerRows, int columns, int bodyRows)
        {
            // 设置表格行列数
            _mockTable.Setup(t => t.Rows).Returns(CreateMockTableRows(headerRows + bodyRows).Object);
            _mockTable.Setup(t => t.Columns).Returns(CreateMockTableColumns(columns).Object);
            
            // 设置单元格
            for (int row = 1; row <= headerRows + bodyRows; row++)
            {
                for (int col = 1; col <= columns; col++)
                {
                    var mockCell = new Mock<PowerPoint.Cell>();
                    var mockShape = new Mock<PowerPoint.Shape>();
                    var mockTextFrame = new Mock<PowerPoint.TextFrame>();
                    var mockTextRange = new Mock<PowerPoint.TextRange>();
                    
                    mockCell.Setup(c => c.Shape).Returns(mockShape.Object);
                    mockShape.Setup(s => s.TextFrame).Returns(mockTextFrame.Object);
                    mockTextFrame.Setup(tf => tf.TextRange).Returns(mockTextRange.Object);
                    
                    _mockTable.Setup(t => t.Cell(row, col)).Returns(mockCell.Object);
                }
            }
            
            // 设置表格边框
            _mockTable.Setup(t => t.Borders).Returns(new Mock<PowerPoint.Borders>().Object);
        }
        
        private Mock<PowerPoint.Rows> CreateMockTableRows(int count)
        {
            var mockRows = new Mock<PowerPoint.Rows>();
            mockRows.Setup(r => r.Count).Returns(count);
            
            for (int i = 1; i <= count; i++)
            {
                var mockRow = new Mock<PowerPoint.Row>();
                mockRows.Setup(r => r[i]).Returns(mockRow.Object);
            }
            
            return mockRows;
        }
        
        private Mock<PowerPoint.Columns> CreateMockTableColumns(int count)
        {
            var mockColumns = new Mock<PowerPoint.Columns>();
            mockColumns.Setup(c => c.Count).Returns(count);
            
            for (int i = 1; i <= count; i++)
            {
                var mockColumn = new Mock<PowerPoint.Column>();
                mockColumns.Setup(c => c[i]).Returns(mockColumn.Object);
            }
            
            return mockColumns;
        }
    }
} 