using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using PBIppt.ApiClient;
using PBIppt.Models;
using PBIppt.UI;
using PowerPoint = Microsoft.Office.Interop.PowerPoint;

namespace PBIppt.Tests.IntegrationTests
{
    [TestClass]
    public class TableGenerationWorkflowTests
    {
        private Mock<IBatteryImageApiClient> _mockApiClient;
        private Mock<PowerPoint.Slide> _mockSlide;
        private Mock<PowerPoint.Shapes> _mockShapes;
        private Mock<PowerPoint.Shape> _mockTableShape;
        private Mock<PowerPoint.Table> _mockTable;

        [TestInitialize]
        public void Initialize()
        {
            // 创建模拟对象
            _mockApiClient = new Mock<IBatteryImageApiClient>();
            _mockSlide = new Mock<PowerPoint.Slide>();
            _mockShapes = new Mock<PowerPoint.Shapes>();
            _mockTableShape = new Mock<PowerPoint.Shape>();
            _mockTable = new Mock<PowerPoint.Table>();

            // 设置模拟对象的行为
            _mockSlide.Setup(s => s.Shapes).Returns(_mockShapes.Object);
            _mockShapes.Setup(s => s.AddTable(It.IsAny<int>(), It.IsAny<int>(), 
                It.IsAny<float>(), It.IsAny<float>(), It.IsAny<float>(), It.IsAny<float>()))
                .Returns(_mockTableShape.Object);
            _mockTableShape.Setup(s => s.Table).Returns(_mockTable.Object);
        }

        [TestMethod]
        public async Task CompleteTableGenerationWorkflow_Success()
        {
            // 准备
            // 1. 模拟登录成功
            _mockApiClient.Setup(a => a.LoginAsync(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(new LoginResponse
                {
                    TokenInfo = new Newtonsoft.Json.Linq.JObject
                    {
                        ["token"] = "test_token",
                        ["expireIn"] = 3600,
                        ["userInfo"] = new Newtonsoft.Json.Linq.JObject
                        {
                            ["id"] = 1,
                            ["account"] = "test",
                            ["name"] = "测试用户",
                            ["adminType"] = 1
                        }
                    }
                });

            // 2. 模拟获取存储天数列表
            _mockApiClient.Setup(a => a.GetAvailableTotalDaysAsync(It.IsAny<long>()))
                .ReturnsAsync(new List<string> { "10", "20", "30" });

            // 3. 模拟获取样品编号列表
            _mockApiClient.Setup(a => a.GetSampleNoListAsync(It.IsAny<long>()))
                .ReturnsAsync(new List<string> { "样品1", "样品2", "样品3" });

            // 4. 模拟获取图片类型列表
            _mockApiClient.Setup(a => a.GetPictureTypesBySampleTypeAsync(It.IsAny<string>()))
                .ReturnsAsync(new List<string> { "正面", "侧面", "顶面" });

            // 5. 模拟获取图片数据
            _mockApiClient.Setup(a => a.GetPictureByTestProgressAsync(It.IsAny<TestProgress>(), It.IsAny<string>()))
                .ReturnsAsync(new BatteryImageResponse
                {
                    Header = new List<List<string>>
                    {
                        new List<string> { "电池编号", "1号", "2号" },
                        new List<string> { "图片类型", "处理前", "处理后" }
                    },
                    BodyDataList = new List<Dictionary<string, object>>
                    {
                        new Dictionary<string, object>
                        {
                            { "电池编号", "样品1" },
                            { "1号_处理前", new byte[] { 1, 2, 3 } },
                            { "1号_处理后", new byte[] { 4, 5, 6 } }
                        }
                    },
                    AllPicTotalDayList = new List<string> { "10", "20", "30" },
                    AllSampleNoList = new List<string> { "样品1", "样品2", "样品3" },
                    AllPicTypeList = new List<string> { "正面", "侧面", "顶面" }
                });

            // 设置表格模拟对象
            SetupTableMocks(2, 3, 1);

            // 执行
            // 1. 登录
            var loginResult = await _mockApiClient.Object.LoginAsync("test", "password");
            Assert.IsNotNull(loginResult);

            // 2. 获取测试进度参数
            var testProgress = new TestProgress { Id = 1 };
            var totalDays = await _mockApiClient.Object.GetAvailableTotalDaysAsync(1);
            var sampleNos = await _mockApiClient.Object.GetSampleNoListAsync(1);
            var pictureTypes = await _mockApiClient.Object.GetPictureTypesBySampleTypeAsync("电池");

            // 3. 获取图片数据
            var response = await _mockApiClient.Object.GetPictureByTestProgressAsync(testProgress, "all");
            Assert.IsNotNull(response);

            // 4. 生成表格
            var tableGenerator = new TableGenerator(_mockSlide.Object);
            var table = tableGenerator.GenerateBatteryImageTable(response);
            Assert.IsNotNull(table);

            // 验证
            _mockApiClient.Verify(a => a.LoginAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
            _mockApiClient.Verify(a => a.GetPictureByTestProgressAsync(It.IsAny<TestProgress>(), It.IsAny<string>()), Times.Once);
            _mockShapes.Verify(s => s.AddTable(It.IsAny<int>(), It.IsAny<int>(), 
                It.IsAny<float>(), It.IsAny<float>(), It.IsAny<float>(), It.IsAny<float>()), Times.Once);
        }

        private void SetupTableMocks(int headerRows, int columns, int bodyRows)
        {
            // 设置表格行列数
            var mockRows = new Mock<PowerPoint.Rows>();
            mockRows.Setup(r => r.Count).Returns(headerRows + bodyRows);
            _mockTable.Setup(t => t.Rows).Returns(mockRows.Object);
            
            var mockColumns = new Mock<PowerPoint.Columns>();
            mockColumns.Setup(c => c.Count).Returns(columns);
            _mockTable.Setup(t => t.Columns).Returns(mockColumns.Object);
            
            // 设置单元格
            for (int row = 1; row <= headerRows + bodyRows; row++)
            {
                for (int col = 1; col <= columns; col++)
                {
                    var mockCell = new Mock<PowerPoint.Cell>();
                    var mockShape = new Mock<PowerPoint.Shape>();
                    var mockTextFrame = new Mock<PowerPoint.TextFrame>();
                    var mockTextRange = new Mock<PowerPoint.TextRange>();
                    
                    mockCell.Setup(c => c.Shape).Returns(mockShape.Object);
                    mockShape.Setup(s => s.TextFrame).Returns(mockTextFrame.Object);
                    mockTextFrame.Setup(tf => tf.TextRange).Returns(mockTextRange.Object);
                    
                    _mockTable.Setup(t => t.Cell(row, col)).Returns(mockCell.Object);
                }
            }
            
            // 设置表格边框
            _mockTable.Setup(t => t.Borders).Returns(new Mock<PowerPoint.Borders>().Object);
        }
    }
} 