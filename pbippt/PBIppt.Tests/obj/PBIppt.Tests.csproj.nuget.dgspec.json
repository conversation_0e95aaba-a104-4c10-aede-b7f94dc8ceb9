{"format": 1, "restore": {"D:\\gitlocal\\pbi\\pbippt\\PBIppt.Tests\\PBIppt.Tests.csproj": {}}, "projects": {"D:\\gitlocal\\pbi\\pbippt\\PBIppt.Tests\\PBIppt.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\gitlocal\\pbi\\pbippt\\PBIppt.Tests\\PBIppt.Tests.csproj", "projectName": "PBIppt.Tests", "projectPath": "D:\\gitlocal\\pbi\\pbippt\\PBIppt.Tests\\PBIppt.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\gitlocal\\pbi\\pbippt\\PBIppt.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"D:\\gitlocal\\pbi\\pbippt\\PBIppt\\PBIppt.csproj": {"projectPath": "D:\\gitlocal\\pbi\\pbippt\\PBIppt\\PBIppt.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"MSTest.TestAdapter": {"target": "Package", "version": "[3.0.2, )"}, "MSTest.TestFramework": {"target": "Package", "version": "[3.0.2, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.5.0, )"}, "Moq": {"target": "Package", "version": "[4.18.4, )"}, "coverlet.collector": {"target": "Package", "version": "[3.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "D:\\gitlocal\\pbi\\pbippt\\PBIppt\\PBIppt.csproj": {"restore": {"projectUniqueName": "D:\\gitlocal\\pbi\\pbippt\\PBIppt\\PBIppt.csproj", "projectName": "PBIppt", "projectPath": "D:\\gitlocal\\pbi\\pbippt\\PBIppt\\PBIppt.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "projectStyle": "PackagesConfig", "UsingMicrosoftNETSdk": false, "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low"}, "packagesConfigPath": "D:\\gitlocal\\pbi\\pbippt\\PBIppt\\packages.config"}, "frameworks": {"net472": {}}}}}