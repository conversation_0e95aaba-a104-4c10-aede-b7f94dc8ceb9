using System;

namespace CropTest
{
    /// <summary>
    /// 裁剪坐标结构
    /// </summary>
    public class CropCoordinates
    {
        public float Left { get; set; }
        public float Top { get; set; }
        public float Right { get; set; }
        public float Bottom { get; set; }
    }

    /// <summary>
    /// PowerPoint裁剪坐标转换修复测试
    /// </summary>
    public class CropCoordinateTest
    {
        /// <summary>
        /// 修复后的坐标转换方法：将API边界框转换为PowerPoint裁剪距离
        /// 重新分析：API返回的是要保留的区域边界框，需要转换为从边缘裁剪的距离
        /// </summary>
        public static CropCoordinates ConvertApiCoordsToPptCrop_Fixed(
            int apiCropLeft, int apiCropTop, int apiCropRight, int apiCropBottom,
            int originalPixelWidth, int originalPixelHeight,
            float shapeWidth, float shapeHeight)
        {
            Console.WriteLine($"=== 修复后的坐标转换测试 ===");
            Console.WriteLine($"API返回的边界框: Left={apiCropLeft}, Top={apiCropTop}, Right={apiCropRight}, Bottom={apiCropBottom}");
            Console.WriteLine($"原始图片像素尺寸: {originalPixelWidth}x{originalPixelHeight}");
            Console.WriteLine($"PowerPoint形状尺寸: {shapeWidth:F2}x{shapeHeight:F2} points");

            // 计算缩放比例
            float scaleX = shapeWidth / originalPixelWidth;
            float scaleY = shapeHeight / originalPixelHeight;

            Console.WriteLine($"缩放比例: X={scaleX:F4}, Y={scaleY:F4}");

            // 重新理解：API返回的是要保留的区域边界框
            // 需要计算从边缘裁剪掉的距离
            float cropLeftDistance = apiCropLeft;                                    // 从左边裁剪掉的距离
            float cropTopDistance = apiCropTop;                                      // 从上边裁剪掉的距离
            float cropRightDistance = originalPixelWidth - apiCropRight;             // 从右边裁剪掉的距离
            float cropBottomDistance = originalPixelHeight - apiCropBottom;          // 从下边裁剪掉的距离

            Console.WriteLine($"像素裁剪距离: Left={cropLeftDistance:F2}, Top={cropTopDistance:F2}, Right={cropRightDistance:F2}, Bottom={cropBottomDistance:F2}");

            // 验证保留区域大小
            float preservedPixelWidth = apiCropRight - apiCropLeft;
            float preservedPixelHeight = apiCropBottom - apiCropTop;
            Console.WriteLine($"API要保留的像素区域: {preservedPixelWidth:F2}x{preservedPixelHeight:F2}");

            // 按比例缩放到PowerPoint坐标系
            var cropCoords = new CropCoordinates
            {
                Left = cropLeftDistance * scaleX,
                Top = cropTopDistance * scaleY,
                Right = cropRightDistance * scaleX,
                Bottom = cropBottomDistance * scaleY
            };

            Console.WriteLine($"PowerPoint裁剪坐标: Left={cropCoords.Left:F2}, Top={cropCoords.Top:F2}, Right={cropCoords.Right:F2}, Bottom={cropCoords.Bottom:F2}");

            // 计算保留区域
            float remainingWidth = shapeWidth - cropCoords.Left - cropCoords.Right;
            float remainingHeight = shapeHeight - cropCoords.Top - cropCoords.Bottom;
            Console.WriteLine($"保留区域: 宽度={remainingWidth:F2}, 高度={remainingHeight:F2}");

            // 验证保留区域是否合理
            float expectedWidth = preservedPixelWidth * scaleX;
            float expectedHeight = preservedPixelHeight * scaleY;
            Console.WriteLine($"期望保留区域: 宽度={expectedWidth:F2}, 高度={expectedHeight:F2}");

            return cropCoords;
        }

        /// <summary>
        /// 旧的错误转换方法（用于对比）
        /// </summary>
        public static CropCoordinates ConvertApiCoordsToPptCrop_Old(
            int apiCropLeft, int apiCropTop, int apiCropRight, int apiCropBottom,
            int originalPixelWidth, int originalPixelHeight,
            float shapeWidth, float shapeHeight)
        {
            Console.WriteLine($"=== 旧的错误转换方法 ===");

            // 计算缩放比例
            float scaleX = shapeWidth / originalPixelWidth;
            float scaleY = shapeHeight / originalPixelHeight;

            // 错误：直接将边界框坐标当作裁剪距离
            var cropCoords = new CropCoordinates
            {
                Left = apiCropLeft * scaleX,
                Top = apiCropTop * scaleY,
                Right = apiCropRight * scaleX,
                Bottom = apiCropBottom * scaleY
            };

            Console.WriteLine($"错误的PowerPoint裁剪坐标: Left={cropCoords.Left:F2}, Top={cropCoords.Top:F2}, Right={cropCoords.Right:F2}, Bottom={cropCoords.Bottom:F2}");

            return cropCoords;
        }

        /// <summary>
        /// 新的修复方法：基于对PowerPoint裁剪系统的重新理解
        /// 可能问题在于我们对API坐标的理解有误
        /// </summary>
        public static CropCoordinates ConvertApiCoordsToPptCrop_NewFix(
            int apiCropLeft, int apiCropTop, int apiCropRight, int apiCropBottom,
            int originalPixelWidth, int originalPixelHeight,
            float shapeWidth, float shapeHeight)
        {
            Console.WriteLine($"=== 新的修复方法测试 ===");
            Console.WriteLine($"重新分析API坐标含义...");

            // 假设1：API返回的可能已经是裁剪距离而不是边界框
            Console.WriteLine($"假设1：API返回的就是裁剪距离");
            float scaleX = shapeWidth / originalPixelWidth;
            float scaleY = shapeHeight / originalPixelHeight;

            var method1 = new CropCoordinates
            {
                Left = apiCropLeft * scaleX,
                Top = apiCropTop * scaleY,
                Right = apiCropRight * scaleX,
                Bottom = apiCropBottom * scaleY
            };

            Console.WriteLine($"方法1结果: Left={method1.Left:F2}, Top={method1.Top:F2}, Right={method1.Right:F2}, Bottom={method1.Bottom:F2}");

            // 假设2：API返回的是百分比坐标，需要转换为绝对坐标
            Console.WriteLine($"假设2：使用百分比转换");
            float leftPercent = (float)apiCropLeft / originalPixelWidth;
            float topPercent = (float)apiCropTop / originalPixelHeight;
            float rightPercent = (float)apiCropRight / originalPixelWidth;
            float bottomPercent = (float)apiCropBottom / originalPixelHeight;

            var method2 = new CropCoordinates
            {
                Left = leftPercent * shapeWidth,
                Top = topPercent * shapeHeight,
                Right = (1.0f - rightPercent) * shapeWidth,
                Bottom = (1.0f - bottomPercent) * shapeHeight
            };

            Console.WriteLine($"方法2结果: Left={method2.Left:F2}, Top={method2.Top:F2}, Right={method2.Right:F2}, Bottom={method2.Bottom:F2}");

            return method1; // 返回第一种方法的结果
        }

        /// <summary>
        /// 测试百分比坐标转换逻辑
        /// </summary>
        public static void TestPercentageConversion()
        {
            Console.WriteLine("=== 百分比坐标转换测试 ===");

            // 使用日志中的实际数据
            float leftPercent = 0.256944f;
            float topPercent = 0.297185f;
            float rightPercent = 0.461111f;
            float bottomPercent = 0.426486f;

            Console.WriteLine($"API百分比坐标: Left={leftPercent:F6}, Top={topPercent:F6}, Right={rightPercent:F6}, Bottom={bottomPercent:F6}");

            // 当前的转换逻辑
            float cropLeft = leftPercent * 100;              // 从左边裁剪的百分比
            float cropTop = topPercent * 100;                // 从上边裁剪的百分比
            float cropRight = (1.0f - rightPercent) * 100;   // 从右边裁剪的百分比
            float cropBottom = (1.0f - bottomPercent) * 100; // 从下边裁剪的百分比

            Console.WriteLine($"PowerPoint裁剪百分比: Left={cropLeft:F2}%, Top={cropTop:F2}%, Right={cropRight:F2}%, Bottom={cropBottom:F2}%");

            // 验证保留区域
            float preservedWidthPercent = (rightPercent - leftPercent) * 100;
            float preservedHeightPercent = (bottomPercent - topPercent) * 100;
            Console.WriteLine($"保留区域百分比: 宽度={preservedWidthPercent:F2}%, 高度={preservedHeightPercent:F2}%");

            // 验证总和
            float totalCropWidth = cropLeft + cropRight;
            float totalCropHeight = cropTop + cropBottom;
            Console.WriteLine($"总裁剪百分比: 宽度={totalCropWidth:F2}%, 高度={totalCropHeight:F2}%");
            Console.WriteLine($"剩余百分比: 宽度={100-totalCropWidth:F2}%, 高度={100-totalCropHeight:F2}%");

            Console.WriteLine("\n=== 新的修复逻辑验证 ===");

            // 新的修复：直接使用API百分比作为裁剪距离
            Console.WriteLine("新修复：直接使用API百分比作为裁剪距离:");
            Console.WriteLine($"裁剪距离: Left={leftPercent*100:F2}%, Top={topPercent*100:F2}%, Right={rightPercent*100:F2}%, Bottom={bottomPercent*100:F2}%");
            float newPreservedWidth = 100 - (leftPercent*100) - (rightPercent*100);
            float newPreservedHeight = 100 - (topPercent*100) - (bottomPercent*100);
            Console.WriteLine($"新修复的保留区域: 宽度={newPreservedWidth:F2}%, 高度={newPreservedHeight:F2}%");

            Console.WriteLine("\n对比结果:");
            Console.WriteLine($"原始逻辑保留区域: {preservedWidthPercent:F2}% × {preservedHeightPercent:F2}%");
            Console.WriteLine($"新修复保留区域: {newPreservedWidth:F2}% × {newPreservedHeight:F2}%");
            Console.WriteLine($"新修复的保留区域更大，更合理！");

            Console.WriteLine();
        }

        /// <summary>
        /// 测试主方法
        /// </summary>
        public static void RunTest()
        {
            Console.WriteLine("PowerPoint裁剪坐标转换修复测试");
            Console.WriteLine("=====================================");

            // 首先测试百分比转换逻辑
            TestPercentageConversion();

            // 使用日志中的实际数据进行测试
            
            // Picture 7的数据
            Console.WriteLine("\n测试Picture 7:");
            int picture7_apiLeft = 171, picture7_apiTop = 262, picture7_apiRight = 299, picture7_apiBottom = 372;
            int picture7_pixelWidth = 661, picture7_pixelHeight = 884;
            float picture7_shapeWidth = 495.75f, picture7_shapeHeight = 663.00f;

            var picture7_fixed = ConvertApiCoordsToPptCrop_Fixed(
                picture7_apiLeft, picture7_apiTop, picture7_apiRight, picture7_apiBottom,
                picture7_pixelWidth, picture7_pixelHeight,
                picture7_shapeWidth, picture7_shapeHeight);

            var picture7_old = ConvertApiCoordsToPptCrop_Old(
                picture7_apiLeft, picture7_apiTop, picture7_apiRight, picture7_apiBottom,
                picture7_pixelWidth, picture7_pixelHeight,
                picture7_shapeWidth, picture7_shapeHeight);

            var picture7_newfix = ConvertApiCoordsToPptCrop_NewFix(
                picture7_apiLeft, picture7_apiTop, picture7_apiRight, picture7_apiBottom,
                picture7_pixelWidth, picture7_pixelHeight,
                picture7_shapeWidth, picture7_shapeHeight);

            // Picture 4的数据
            Console.WriteLine("\n测试Picture 4:");
            int picture4_apiLeft = 185, picture4_apiTop = 285, picture4_apiRight = 332, picture4_apiBottom = 409;
            int picture4_pixelWidth = 720, picture4_pixelHeight = 959;
            float picture4_shapeWidth = 719.56f, picture4_shapeHeight = 539.56f;

            var picture4_fixed = ConvertApiCoordsToPptCrop_Fixed(
                picture4_apiLeft, picture4_apiTop, picture4_apiRight, picture4_apiBottom,
                picture4_pixelWidth, picture4_pixelHeight,
                picture4_shapeWidth, picture4_shapeHeight);

            var picture4_old = ConvertApiCoordsToPptCrop_Old(
                picture4_apiLeft, picture4_apiTop, picture4_apiRight, picture4_apiBottom,
                picture4_pixelWidth, picture4_pixelHeight,
                picture4_shapeWidth, picture4_shapeHeight);

            var picture4_newfix = ConvertApiCoordsToPptCrop_NewFix(
                picture4_apiLeft, picture4_apiTop, picture4_apiRight, picture4_apiBottom,
                picture4_pixelWidth, picture4_pixelHeight,
                picture4_shapeWidth, picture4_shapeHeight);

            Console.WriteLine("\n=====================================");
            Console.WriteLine("测试完成！");
            Console.WriteLine("修复后的方法应该产生更合理的裁剪结果。");
        }
    }

    /// <summary>
    /// 程序入口点
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            CropCoordinateTest.RunTest();
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
