using System;
using System.Configuration;

namespace PBIppt.Models
{
    /// <summary>
    /// 环境配置管理器 - 根据编译配置自动选择不同环境的设置
    /// </summary>
    public static class EnvironmentConfig
    {
        /// <summary>
        /// 当前环境类型
        /// </summary>
        public enum EnvironmentType
        {
            Debug,
            Release
        }

        /// <summary>
        /// 获取当前环境类型
        /// </summary>
        public static EnvironmentType CurrentEnvironment
        {
            get
            {
#if DEBUG
                return EnvironmentType.Debug;
#else
                return EnvironmentType.Release;
#endif
            }
        }

        /// <summary>
        /// 获取当前环境名称
        /// </summary>
        public static string EnvironmentName => CurrentEnvironment.ToString();

        /// <summary>
        /// 获取API服务器基础URL
        /// </summary>
        public static string ApiBaseUrl
        {
            get
            {
                // 优先从配置文件读取
                string configValue = ConfigurationManager.AppSettings["ApiBaseUrl"];
                if (!string.IsNullOrEmpty(configValue))
                {
                    return configValue;
                }

                // 根据环境返回默认值
                switch (CurrentEnvironment)
                {
                    case EnvironmentType.Debug:
                        return "http://localhost:82"; // 测试环境
                    case EnvironmentType.Release:
                        return "http://************:88"; // 生产环境
                    default:
                        return "http://localhost:82";
                }
            }
        }

        /// <summary>
        /// 环境配置信息
        /// </summary>
        public class RoleConfig
        {
            public long PptUserRoleId { get; set; }
            public long BatteryDetectionRoleId { get; set; }
            public long AdminRoleId { get; set; }
            public string ApiBaseUrl { get; set; }
            public string Environment { get; set; }
        }

        /// <summary>
        /// Debug环境配置
        /// </summary>
        private static readonly RoleConfig DebugConfig = new RoleConfig
        {
            PptUserRoleId = 1234567890L,        // 测试环境PPT用户角色ID
            BatteryDetectionRoleId = 1234567890L, // 测试环境电芯检测角色ID
            AdminRoleId = 1111111111L,           // 测试环境管理员角色ID
            ApiBaseUrl = "http://localhost:82",
            Environment = "Debug"
        };

        /// <summary>
        /// Release环境配置
        /// </summary>
        private static readonly RoleConfig ReleaseConfig = new RoleConfig
        {
            PptUserRoleId = 9876543210L,        // 生产环境PPT用户角色ID
            BatteryDetectionRoleId = 9876543210L, // 生产环境电芯检测角色ID
            AdminRoleId = 2222222222L,           // 生产环境管理员角色ID
            ApiBaseUrl = "http://************:88",
            Environment = "Release"
        };

        /// <summary>
        /// 获取当前环境的配置
        /// </summary>
        public static RoleConfig GetCurrentConfig()
        {
            switch (CurrentEnvironment)
            {
                case EnvironmentType.Debug:
                    return DebugConfig;
                case EnvironmentType.Release:
                    return ReleaseConfig;
                default:
                    return DebugConfig;
            }
        }

        /// <summary>
        /// 获取配置值（优先使用配置文件，然后使用环境默认值）
        /// </summary>
        /// <param name="configKey">配置键名</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public static long GetRoleId(string configKey, long defaultValue)
        {
            string configValue = ConfigurationManager.AppSettings[configKey];
            if (!string.IsNullOrEmpty(configValue) && long.TryParse(configValue, out long roleId))
            {
                return roleId;
            }
            return defaultValue;
        }

        /// <summary>
        /// 获取详细的环境配置信息
        /// </summary>
        /// <returns>配置信息字符串</returns>
        public static string GetDetailedConfigInfo()
        {
            var currentConfig = GetCurrentConfig();
            var info = new System.Text.StringBuilder();
            
            info.AppendLine("=== PPT插件环境配置信息 ===");
            info.AppendLine($"当前环境: {EnvironmentName}");
            info.AppendLine($"API服务器: {ApiBaseUrl}");
            info.AppendLine();
            
            info.AppendLine("角色ID配置:");
            info.AppendLine($"PPT用户角色ID: {RoleConstants.PPT_USER_ROLE_ID}");
            info.AppendLine($"电芯检测角色ID: {RoleConstants.BATTERY_DETECTION_ROLE_ID}");
            info.AppendLine($"管理员角色ID: {RoleConstants.ADMIN_ROLE_ID}");
            info.AppendLine();
            
            info.AppendLine("环境默认值:");
            info.AppendLine($"PPT用户角色ID: {currentConfig.PptUserRoleId}");
            info.AppendLine($"电芯检测角色ID: {currentConfig.BatteryDetectionRoleId}");
            info.AppendLine($"管理员角色ID: {currentConfig.AdminRoleId}");
            info.AppendLine();
            
            // 检查配置文件覆盖情况
            info.AppendLine("配置文件覆盖情况:");
            CheckConfigOverride(info, "PptUserRoleId", currentConfig.PptUserRoleId);
            CheckConfigOverride(info, "BatteryDetectionRoleId", currentConfig.BatteryDetectionRoleId);
            CheckConfigOverride(info, "AdminRoleId", currentConfig.AdminRoleId);
            CheckConfigOverride(info, "ApiBaseUrl", currentConfig.ApiBaseUrl);
            
            return info.ToString();
        }

        /// <summary>
        /// 检查配置文件是否覆盖了默认值
        /// </summary>
        private static void CheckConfigOverride(System.Text.StringBuilder info, string key, object defaultValue)
        {
            string configValue = ConfigurationManager.AppSettings[key];
            if (string.IsNullOrEmpty(configValue))
            {
                info.AppendLine($"  {key}: 使用环境默认值 ({defaultValue})");
            }
            else
            {
                info.AppendLine($"  {key}: 使用配置文件值 ({configValue})");
            }
        }

        /// <summary>
        /// 验证当前配置是否有效
        /// </summary>
        /// <returns>验证结果</returns>
        public static (bool IsValid, string ErrorMessage) ValidateConfig()
        {
            try
            {
                var pptRoleId = RoleConstants.PPT_USER_ROLE_ID;
                var batteryRoleId = RoleConstants.BATTERY_DETECTION_ROLE_ID;
                var adminRoleId = RoleConstants.ADMIN_ROLE_ID;
                var apiUrl = ApiBaseUrl;

                if (pptRoleId <= 0)
                    return (false, "PPT用户角色ID无效");
                
                if (batteryRoleId <= 0)
                    return (false, "电芯检测角色ID无效");
                
                if (adminRoleId <= 0)
                    return (false, "管理员角色ID无效");
                
                if (string.IsNullOrEmpty(apiUrl))
                    return (false, "API服务器地址无效");

                return (true, "配置验证通过");
            }
            catch (Exception ex)
            {
                return (false, $"配置验证失败: {ex.Message}");
            }
        }
    }
}
