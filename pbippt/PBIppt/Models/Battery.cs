using System;
using System.Drawing;

namespace PBIppt.Models
{
    /// <summary>
    /// Represents a battery detected in an image
    /// </summary>
    public class Battery
    {
        /// <summary>
        /// Unique identifier for the battery
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// The confidence score of the detection (0-1)
        /// </summary>
        public double Confidence { get; set; }

        /// <summary>
        /// The bounding box of the battery in the image
        /// </summary>
        public Rectangle BoundingBox { get; set; }

        /// <summary>
        /// The type or model of the battery
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Additional information about the battery
        /// </summary>
        public string Description { get; set; }
    }
} 