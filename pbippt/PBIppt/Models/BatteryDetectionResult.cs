using System;
using System.Collections.Generic;

namespace PBIppt.Models
{
    /// <summary>
    /// Result of a battery detection operation
    /// </summary>
    public class BatteryDetectionResult
    {
        /// <summary>
        /// Whether the detection was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Error message if the detection failed
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// List of batteries detected in the image
        /// </summary>
        public List<Battery> Batteries { get; set; } = new List<Battery>();

        /// <summary>
        /// The time taken to process the image
        /// </summary>
        public TimeSpan ProcessingTime { get; set; }
    }
} 