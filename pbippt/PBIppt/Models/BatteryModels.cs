using System;
using System.Collections.Generic;

namespace PBIppt.Models
{

    
    /// <summary>
    /// 电池检测结果
    /// </summary>
    public class BatteryDetectionResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 检测到的电池列表
        /// </summary>
        public List<BatteryInfo> Batteries { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
    }
    
    /// <summary>
    /// 电池信息
    /// </summary>
    public class BatteryInfo
    {
        /// <summary>
        /// 电池ID
        /// </summary>
        public string Id { get; set; }
        
        /// <summary>
        /// 类型
        /// </summary>
        public string Type { get; set; }
        
        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; }
        
        /// <summary>
        /// 位置信息
        /// </summary>
        public BatteryLocation Location { get; set; }
    }
    
    /// <summary>
    /// 电池位置信息
    /// </summary>
    public class BatteryLocation
    {
        /// <summary>
        /// X坐标
        /// </summary>
        public double X { get; set; }
        
        /// <summary>
        /// Y坐标
        /// </summary>
        public double Y { get; set; }
        
        /// <summary>
        /// 宽度
        /// </summary>
        public double Width { get; set; }
        
        /// <summary>
        /// 高度
        /// </summary>
        public double Height { get; set; }
    }
} 