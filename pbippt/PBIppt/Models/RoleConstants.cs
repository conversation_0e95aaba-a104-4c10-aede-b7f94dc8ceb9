using System;
using System.Configuration;

namespace PBIppt.Models
{
    /// <summary>
    /// 角色权限常量定义
    /// </summary>
    public static class RoleConstants
    {
        /// <summary>
        /// PPT插件用户角色ID - 可以使用PPT插件的基础角色
        /// 这个值可以通过配置文件配置，或者从数据库中查询获取
        /// </summary>
        public static long PPT_USER_ROLE_ID
        {
            get
            {
                // 优先从配置文件读取
                string configValue = ConfigurationManager.AppSettings["PptUserRoleId"];
                if (!string.IsNullOrEmpty(configValue) && long.TryParse(configValue, out long roleId))
                {
                    return roleId;
                }
                
                // 默认角色ID - 请根据实际数据库中的角色ID进行修改
                // 可以通过查询 sys_role 表获取具体的角色ID
                return 1234567890L; // 示例ID，请替换为实际的角色ID
            }
        }

        /// <summary>
        /// 电芯检测功能专用角色ID - 如果需要更细粒度的权限控制
        /// </summary>
        public static long BATTERY_DETECTION_ROLE_ID
        {
            get
            {
                string configValue = ConfigurationManager.AppSettings["BatteryDetectionRoleId"];
                if (!string.IsNullOrEmpty(configValue) && long.TryParse(configValue, out long roleId))
                {
                    return roleId;
                }
                
                // 如果没有配置专门的电芯检测角色，使用PPT用户角色
                return PPT_USER_ROLE_ID;
            }
        }

        /// <summary>
        /// 管理员角色ID - 拥有所有权限
        /// </summary>
        public static long ADMIN_ROLE_ID
        {
            get
            {
                string configValue = ConfigurationManager.AppSettings["AdminRoleId"];
                if (!string.IsNullOrEmpty(configValue) && long.TryParse(configValue, out long roleId))
                {
                    return roleId;
                }
                
                return 9876543210L; // 示例ID，请替换为实际的管理员角色ID
            }
        }

        /// <summary>
        /// PPT插件用户角色编码 - 通过角色编码进行验证的方式
        /// </summary>
        public const string PPT_USER_ROLE_CODE = "PPT_USER";

        /// <summary>
        /// 电芯检测功能角色编码
        /// </summary>
        public const string BATTERY_DETECTION_ROLE_CODE = "BATTERY_DETECTION";

        /// <summary>
        /// 管理员角色编码
        /// </summary>
        public const string ADMIN_ROLE_CODE = "ADMIN";

        /// <summary>
        /// 检查是否为管理员角色
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>true表示是管理员角色</returns>
        public static bool IsAdminRole(long roleId)
        {
            return roleId == ADMIN_ROLE_ID;
        }

        /// <summary>
        /// 检查是否为管理员角色
        /// </summary>
        /// <param name="roleCode">角色编码</param>
        /// <returns>true表示是管理员角色</returns>
        public static bool IsAdminRole(string roleCode)
        {
            return string.Equals(roleCode, ADMIN_ROLE_CODE, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 获取角色显示名称
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>角色显示名称</returns>
        public static string GetRoleDisplayName(long roleId)
        {
            if (roleId == PPT_USER_ROLE_ID)
                return "PPT插件用户";
            else if (roleId == BATTERY_DETECTION_ROLE_ID)
                return "电芯检测用户";
            else if (roleId == ADMIN_ROLE_ID)
                return "系统管理员";
            else
                return $"角色ID: {roleId}";
        }
    }
}
