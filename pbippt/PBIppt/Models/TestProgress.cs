using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace PBIppt.Models
{
    /// <summary>
    /// 测试进度模型
    /// </summary>
    public class TestProgress
    {
        /// <summary>
        /// 进度ID
        /// </summary>
        [JsonProperty("id")]
        public long Id { get; set; }
        
        /// <summary>
        /// 样品编号，JSON格式的字符串
        /// </summary>
        [JsonProperty("sampleCodes")]
        public string SampleCodes { get; set; }
        
        /// <summary>
        /// 单个样品编号（兼容适配器）
        /// </summary>
        [JsonProperty("sampleNo")]
        public string SampleNo { get; set; }
        
        /// <summary>
        /// 样品类型
        /// </summary>
        [JsonProperty("sampleType")]
        public string SampleType { get; set; }
        
        /// <summary>
        /// 测试名称
        /// </summary>
        [JsonProperty("testName")]
        public string TestName { get; set; }
        
        /// <summary>
        /// 测试编码
        /// </summary>
        [JsonProperty("testCode")]
        public string TestCode { get; set; }
        
        /// <summary>
        /// 图片类型
        /// </summary>
        [JsonProperty("pictureType")]
        public string PictureType { get; set; }
    }
} 