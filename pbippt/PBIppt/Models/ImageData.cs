using System;

namespace PBIppt.Models
{
    /// <summary>
    /// 图片数据模型
    /// </summary>
    public class ImageData
    {
        /// <summary>
        /// 图片ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 图片名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 图片字节数据
        /// </summary>
        public byte[] Data { get; set; }

        /// <summary>
        /// 图片类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 图片大小（字节）
        /// </summary>
        public long Size => Data?.Length ?? 0;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }
}
