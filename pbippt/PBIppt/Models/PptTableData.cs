using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PBIppt.Models
{
    /// <summary>
    /// PPT表格数据模型
    /// </summary>
    public class PptTableData
    {
        /// <summary>
        /// 委托单号
        /// </summary>
        public string DelegateNumber { get; set; }
        
        /// <summary>
        /// 测试项目ID
        /// </summary>
        public string TestItemId { get; set; }
        
        /// <summary>
        /// 测试项目名称
        /// </summary>
        public string TestItemName { get; set; }
        
        /// <summary>
        /// 照片部位列表
        /// </summary>
        public List<string> PhotoPositions { get; set; } = new List<string>();
        
        /// <summary>
        /// 测试阶段列表
        /// </summary>
        public List<string> TestStages { get; set; } = new List<string>();
        
        /// <summary>
        /// 电芯行数据列表
        /// </summary>
        public List<BatteryRowData> BatteryRows { get; set; } = new List<BatteryRowData>();
    }
    
    /// <summary>
    /// 电芯行数据模型
    /// </summary>
    public class BatteryRowData
    {
        /// <summary>
        /// 电芯编号
        /// </summary>
        public string CellNumber { get; set; }
        
        /// <summary>
        /// SOC（默认为空）
        /// </summary>
        public string Soc { get; set; } = "";
        
        /// <summary>
        /// 测试结果（默认为空）
        /// </summary>
        public string TestResult { get; set; } = "";
        
        /// <summary>
        /// 测试前照片（部位 -> 图片URL）
        /// </summary>
        public Dictionary<string, string> BeforeTestPhotos { get; set; } = new Dictionary<string, string>();
        
        /// <summary>
        /// 测试后照片（部位 -> 图片URL）
        /// </summary>
        public Dictionary<string, string> AfterTestPhotos { get; set; } = new Dictionary<string, string>();
    }
}
