using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;

namespace PBIppt.Models
{
    /// <summary>
    /// API响应数据通用模型
    /// </summary>
    public class ResponseData
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// 响应代码
        /// </summary>
        public int Code { get; set; }
        
        /// <summary>
        /// 响应数据
        /// </summary>
        public object Data { get; set; }
    }

    /// <summary>
    /// 登录请求模型
    /// </summary>
    public class LoginRequest
    {
        /// <summary>
        /// 账号
        /// </summary>
        public string Account { get; set; }
        
        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }
    }

    /// <summary>
    /// 登录响应模型
    /// </summary>
    public class LoginResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 访问令牌
        /// </summary>
        public string AccessToken { get; set; }
        
        /// <summary>
        /// 令牌类型
        /// </summary>
        public string TokenType { get; set; }
        
        /// <summary>
        /// 过期时间（秒）
        /// </summary>
        public int ExpiresIn { get; set; }
        
        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 用户角色列表
        /// </summary>
        public List<UserRole> Roles { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 获取带前缀的完整令牌
        /// </summary>
        public string GetFullToken()
        {
            if (string.IsNullOrEmpty(AccessToken) || string.IsNullOrEmpty(TokenType))
            {
                return null;
            }
            
            return $"{TokenType} {AccessToken}";
        }
    }

    /// <summary>
    /// 用户信息模型
    /// </summary>
    public class UserInfo
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 账号
        /// </summary>
        public string Account { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 用户名（兼容性属性，返回Account）
        /// </summary>
        public string Username => Account;

        /// <summary>
        /// 管理员类型
        /// </summary>
        public int AdminType { get; set; }

        /// <summary>
        /// 用户角色列表
        /// </summary>
        public List<UserRole> Roles { get; set; }
    }

    /// <summary>
    /// 用户角色模型
    /// </summary>
    public class UserRole
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 角色名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 角色编码
        /// </summary>
        public string Code { get; set; }
    }

    /// <summary>
    /// 认证信息管理
    /// </summary>
    public static class AuthManager
    {
        private static LoginResponse _currentAuth;
        private static DateTime _expirationTime;

        /// <summary>
        /// 当前认证信息
        /// </summary>
        public static LoginResponse CurrentAuth => _currentAuth;

        /// <summary>
        /// 是否已登录
        /// </summary>
        public static bool IsLoggedIn => _currentAuth != null && _currentAuth.Success && DateTime.Now < _expirationTime;

        /// <summary>
        /// 设置当前认证信息
        /// </summary>
        public static void SetCurrentAuth(LoginResponse auth)
        {
            // 添加调试信息
            if (auth?.Roles != null)
            {
                System.Diagnostics.Debug.WriteLine($"AuthManager.SetCurrentAuth: 设置角色信息，角色数量: {auth.Roles.Count}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("AuthManager.SetCurrentAuth: 角色信息为空");
            }

            _currentAuth = auth;

            if (auth != null && auth.Success && auth.ExpiresIn > 0)
            {
                _expirationTime = DateTime.Now.AddSeconds(auth.ExpiresIn);
            }
        }

        /// <summary>
        /// 清除认证信息
        /// </summary>
        public static void ClearAuth()
        {
            _currentAuth = null;
        }

        /// <summary>
        /// 获取认证头
        /// </summary>
        public static string GetAuthorizationHeader()
        {
            return IsLoggedIn ? _currentAuth.GetFullToken() : null;
        }

        /// <summary>
        /// 检查当前用户是否具有指定的角色ID（使用登录时缓存的角色信息）
        /// </summary>
        /// <param name="requiredRoleId">需要的角色ID</param>
        /// <returns>true表示用户具有该角色，false表示没有</returns>
        public static bool HasRole(long requiredRoleId)
        {


            if (!IsLoggedIn || _currentAuth?.Roles == null)
            {
                System.Diagnostics.Debug.WriteLine("AuthManager.HasRole: 返回false - 未登录或角色为空");
                return false;
            }

            bool hasRole = _currentAuth.Roles.Any(role => role.Id == requiredRoleId);
            System.Diagnostics.Debug.WriteLine($"AuthManager.HasRole: 角色检查结果: {hasRole}");
            return hasRole;
        }

        /// <summary>
        /// 检查当前用户是否具有指定的角色编码（使用登录时缓存的角色信息）
        /// </summary>
        /// <param name="requiredRoleCode">需要的角色编码</param>
        /// <returns>true表示用户具有该角色，false表示没有</returns>
        public static bool HasRole(string requiredRoleCode)
        {
            if (!IsLoggedIn || _currentAuth?.Roles == null || string.IsNullOrEmpty(requiredRoleCode))
            {
                return false;
            }

            return _currentAuth.Roles.Any(role =>
                string.Equals(role.Code, requiredRoleCode, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取当前用户的所有角色ID（使用登录时缓存的角色信息）
        /// </summary>
        /// <returns>角色ID列表</returns>
        public static List<long> GetUserRoleIds()
        {
            if (!IsLoggedIn || _currentAuth?.Roles == null)
            {
                return new List<long>();
            }

            return _currentAuth.Roles.Select(role => role.Id).ToList();
        }

        /// <summary>
        /// 获取当前用户的所有角色信息（使用登录时缓存的角色信息）
        /// </summary>
        /// <returns>角色列表</returns>
        public static List<UserRole> GetUserRoles()
        {
            if (!IsLoggedIn || _currentAuth?.Roles == null)
            {
                return new List<UserRole>();
            }

            return _currentAuth.Roles.ToList();
        }

        /// <summary>
        /// 刷新用户角色信息（当需要更新角色信息时调用）
        /// </summary>
        /// <returns>是否刷新成功</returns>
        public static async Task<bool> RefreshUserRolesAsync()
        {
            if (!IsLoggedIn)
            {
                return false;
            }

            try
            {
                var authService = new PBIppt.Services.AuthService();
                var userInfo = await authService.GetCurrentUserAsync();
                if (userInfo?.Roles != null && _currentAuth != null)
                {
                    _currentAuth.Roles = userInfo.Roles;
                    System.Diagnostics.Debug.WriteLine($"成功刷新用户角色信息，角色数量: {userInfo.Roles.Count}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"刷新用户角色信息失败: {ex.Message}");
                return false;
            }
        }
    }

    /// <summary>
    /// 登录验证帮助类 - 为所有功能提供统一的登录验证
    /// </summary>
    public static class LoginHelper
    {
        /// <summary>
        /// 检查用户是否已登录，如果未登录则显示登录窗口
        /// </summary>
        /// <param name="functionName">功能名称，用于显示提示信息</param>
        /// <returns>true表示已登录或登录成功，false表示未登录或登录失败</returns>
        public static bool EnsureUserLoggedIn(string functionName = "此功能")
        {
            try
            {
                // 只使用AuthManager检查登录状态，不再额外验证token
                bool needLogin = !AuthManager.IsLoggedIn;

                if (needLogin)
                {
                    System.Diagnostics.Debug.WriteLine($"用户未登录，功能: {functionName}");

                    var result = System.Windows.Forms.MessageBox.Show(
                        $"您尚未登录或登录已过期，需要先登录才能使用{functionName}。是否现在登录？",
                        "需要登录",
                        System.Windows.Forms.MessageBoxButtons.YesNo,
                        System.Windows.Forms.MessageBoxIcon.Question);

                    if (result == System.Windows.Forms.DialogResult.Yes)
                    {
                        var loginForm = new PBIppt.UI.LoginForm();
                        return loginForm.ShowDialog() == System.Windows.Forms.DialogResult.OK;
                    }
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查登录状态时出错: {ex.Message}");
                System.Windows.Forms.MessageBox.Show(
                    $"检查登录状态时出错: {ex.Message}",
                    "错误",
                    System.Windows.Forms.MessageBoxButtons.OK,
                    System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 同步版本的登录检查（用于不支持async的场景）
        /// </summary>
        /// <param name="functionName">功能名称</param>
        /// <returns>true表示已登录，false表示未登录</returns>
        public static bool IsUserLoggedIn(string functionName = "此功能")
        {
            try
            {
                bool isLoggedIn = AuthManager.IsLoggedIn;

                if (!isLoggedIn)
                {
                    System.Diagnostics.Debug.WriteLine($"用户未登录，功能: {functionName}");

                    System.Windows.Forms.MessageBox.Show(
                        $"您尚未登录，需要先登录才能使用{functionName}。请点击'登录/切换用户'按钮进行登录。",
                        "需要登录",
                        System.Windows.Forms.MessageBoxButtons.OK,
                        System.Windows.Forms.MessageBoxIcon.Information);
                }

                return isLoggedIn;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查登录状态时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查用户是否已登录且具有指定角色权限（使用登录时缓存的角色信息）
        /// </summary>
        /// <param name="requiredRoleId">需要的角色ID</param>
        /// <param name="functionName">功能名称，用于显示提示信息</param>
        /// <param name="showErrorDialog">是否显示错误对话框</param>
        /// <returns>true表示已登录且有权限，false表示未登录或无权限</returns>
        public static async Task<bool> EnsureUserHasRoleAsync(
            long requiredRoleId,
            string functionName = "此功能",
            bool showErrorDialog = true)
        {
            return await EnsureUserHasRoleInternalAsync(requiredRoleId, null, functionName, showErrorDialog);
        }

        /// <summary>
        /// 检查用户是否已登录且具有指定角色权限（通过角色编码，使用登录时缓存的角色信息）
        /// </summary>
        /// <param name="requiredRoleCode">需要的角色编码</param>
        /// <param name="functionName">功能名称，用于显示提示信息</param>
        /// <param name="showErrorDialog">是否显示错误对话框</param>
        /// <returns>true表示已登录且有权限，false表示未登录或无权限</returns>
        public static async Task<bool> EnsureUserHasRoleAsync(
            string requiredRoleCode,
            string functionName = "此功能",
            bool showErrorDialog = true)
        {
            return await EnsureUserHasRoleInternalAsync(null, requiredRoleCode, functionName, showErrorDialog);
        }

        /// <summary>
        /// 内部权限验证方法，统一处理角色ID和角色编码验证
        /// </summary>
        /// <param name="requiredRoleId">需要的角色ID（可为null）</param>
        /// <param name="requiredRoleCode">需要的角色编码（可为null）</param>
        /// <param name="functionName">功能名称，用于显示提示信息</param>
        /// <param name="showErrorDialog">是否显示错误对话框</param>
        /// <returns>true表示已登录且有权限，false表示未登录或无权限</returns>
        private static Task<bool> EnsureUserHasRoleInternalAsync(
            long? requiredRoleId,
            string requiredRoleCode,
            string functionName,
            bool showErrorDialog)
        {
            try
            {
                // 首先检查是否已登录
                bool isLoggedIn = EnsureUserLoggedIn(functionName);
                if (!isLoggedIn)
                {
                    return Task.FromResult(false);
                }

                // 使用缓存的角色信息检查权限
                bool hasRole = false;
                string roleIdentifier = "";

                System.Diagnostics.Debug.WriteLine($"权限验证开始 - 登录状态: {AuthManager.IsLoggedIn}");
                System.Diagnostics.Debug.WriteLine($"当前用户角色数量: {AuthManager.GetUserRoles().Count}");

                if (requiredRoleId.HasValue)
                {
                    hasRole = AuthManager.HasRole(requiredRoleId.Value);
                    roleIdentifier = $"角色ID: {requiredRoleId.Value}";
                    System.Diagnostics.Debug.WriteLine($"检查角色ID: {requiredRoleId.Value}, 结果: {hasRole}");
                }
                else if (!string.IsNullOrEmpty(requiredRoleCode))
                {
                    hasRole = AuthManager.HasRole(requiredRoleCode);
                    roleIdentifier = $"角色编码: {requiredRoleCode}";
                    System.Diagnostics.Debug.WriteLine($"检查角色编码: {requiredRoleCode}, 结果: {hasRole}");
                }

                if (!hasRole)
                {
                    System.Diagnostics.Debug.WriteLine($"用户没有所需角色权限，{roleIdentifier}, 功能: {functionName}");

                    if (showErrorDialog)
                    {
                        System.Windows.Forms.MessageBox.Show(
                            "您没有该功能权限，如有疑问请联系蔡嘉伟",
                            "权限不足",
                            System.Windows.Forms.MessageBoxButtons.OK,
                            System.Windows.Forms.MessageBoxIcon.Warning);
                    }
                    return Task.FromResult(false);
                }

                System.Diagnostics.Debug.WriteLine($"用户权限验证通过，{roleIdentifier}, 功能: {functionName}");
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查用户权限时出错: {ex.Message}");
                if (showErrorDialog)
                {
                    System.Windows.Forms.MessageBox.Show(
                        $"检查用户权限时出错: {ex.Message}",
                        "错误",
                        System.Windows.Forms.MessageBoxButtons.OK,
                        System.Windows.Forms.MessageBoxIcon.Error);
                }
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// 检查用户是否已登录且具有指定角色权限，如果权限验证失败则尝试刷新角色信息后重新验证
        /// </summary>
        /// <param name="requiredRoleId">需要的角色ID</param>
        /// <param name="functionName">功能名称，用于显示提示信息</param>
        /// <returns>true表示已登录且有权限，false表示未登录或无权限</returns>
        public static async Task<bool> EnsureUserHasRoleWithRefreshAsync(
            long requiredRoleId,
            string functionName = "此功能")
        {
            // 首先尝试常规验证（不显示错误对话框）
            bool hasPermission = await EnsureUserHasRoleAsync(requiredRoleId, functionName, false);

            if (!hasPermission && AuthManager.IsLoggedIn)
            {
                System.Diagnostics.Debug.WriteLine("权限验证失败，尝试刷新角色信息后重新验证...");

                // 刷新角色信息
                bool refreshSuccess = await AuthManager.RefreshUserRolesAsync();

                if (refreshSuccess)
                {
                    // 重新验证权限
                    bool hasRoleAfterRefresh = AuthManager.HasRole(requiredRoleId);
                    if (hasRoleAfterRefresh)
                    {
                        System.Diagnostics.Debug.WriteLine($"刷新角色信息后权限验证通过，角色ID: {requiredRoleId}, 功能: {functionName}");
                        return true;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("刷新角色信息后仍然没有权限");
                    }
                }

                // 如果刷新失败或刷新后仍然没有权限，显示错误对话框
                System.Windows.Forms.MessageBox.Show(
                    "您没有该功能权限，如有疑问请联系蔡嘉伟",
                    "权限不足",
                    System.Windows.Forms.MessageBoxButtons.OK,
                    System.Windows.Forms.MessageBoxIcon.Warning);
            }

            return hasPermission;
        }
    }
}