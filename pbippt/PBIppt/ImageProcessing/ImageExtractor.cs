using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using Microsoft.Office.Interop.PowerPoint;
using PBIppt.Utils;
using PowerPoint = Microsoft.Office.Interop.PowerPoint;
using Office = Microsoft.Office.Core;
using PBIppt.UI;
using System.Diagnostics;

namespace PBIppt.ImageProcessing
{
    /// <summary>
    /// Extracts images from PowerPoint shapes for processing
    /// </summary>
    public class ImageExtractor
    {
        /// <summary>
        /// Extract image from a PowerPoint shape with enhanced handling for different image sources
        /// </summary>
        public static Image ExtractImageFromShape(PowerPoint.Shape shape)
        {
            if (shape.Type != Office.MsoShapeType.msoPicture)
            {
                throw new ArgumentException("Shape is not a picture");
            }

            try
            {
                Debug.WriteLine("开始提取PowerPoint图片，尝试多种方法");

                // 方法1: 尝试获取原始未压缩图片（新增）
                try
                {
                    var image = ExtractOriginalImageData(shape);
                    if (image != null)
                    {
                        Debug.WriteLine($"方法1成功：原始图片数据，图片尺寸: {image.Width}x{image.Height}");
                        return image;
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"方法1失败：原始图片数据提取失败: {ex.Message}");
                }

                // 方法2: 尝试高质量导出（改进）
                try
                {
                    var image = ExtractImageByHighQualityExport(shape);
                    if (image != null)
                    {
                        Debug.WriteLine($"方法2成功：高质量导出方式，图片尺寸: {image.Width}x{image.Height}");
                        return image;
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"方法2失败：高质量导出方式失败: {ex.Message}");
                }

                // 方法3: 标准导出为文件然后读取
                try
                {
                    var image = ExtractImageByExport(shape);
                    if (image != null)
                    {
                        Debug.WriteLine($"方法3成功：标准导出文件方式，图片尺寸: {image.Width}x{image.Height}");
                        return image;
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"方法3失败：标准导出文件方式失败: {ex.Message}");
                }

                // 方法4: 传统剪贴板方法（作为最后备用）
                try
                {
                    var image = ExtractImageByClipboard(shape);
                    if (image != null)
                    {
                        Debug.WriteLine($"方法4成功：剪贴板方式，图片尺寸: {image.Width}x{image.Height}");
                        return image;
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"方法4失败：剪贴板方式失败: {ex.Message}");
                }

                throw new Exception("所有图片提取方法都失败");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex);
                throw new Exception($"Failed to extract image from shape: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 尝试获取原始未压缩的图片数据（简化版本）
        /// </summary>
        private static Image ExtractOriginalImageData(PowerPoint.Shape shape)
        {
            try
            {
                Debug.WriteLine("尝试获取原始未压缩图片数据");

                // 尝试通过链接图片访问原始文件
                try
                {
                    // 检查是否为链接图片
                    var linkFormat = shape.LinkFormat;
                    if (linkFormat != null && !string.IsNullOrEmpty(linkFormat.SourceFullName))
                    {
                        string sourcePath = linkFormat.SourceFullName;
                        Debug.WriteLine($"发现链接图片，源路径: {sourcePath}");

                        if (File.Exists(sourcePath))
                        {
                            Debug.WriteLine("直接从源文件读取原始图片");
                            // 创建图片副本，避免文件锁定
                            using (var originalImage = Image.FromFile(sourcePath))
                            {
                                return new Bitmap(originalImage);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"链接图片访问失败: {ex.Message}");
                }

                // 如果不是链接图片，直接返回null，让其他方法处理
                Debug.WriteLine("非链接图片，使用其他提取方法");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"原始图片数据提取失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 通过高质量导出方式提取图片（简化版本）
        /// </summary>
        private static Image ExtractImageByHighQualityExport(PowerPoint.Shape shape)
        {
            string tempFilePath = null;
            try
            {
                // 创建临时文件路径，使用PNG格式保持最高质量
                tempFilePath = Path.Combine(Path.GetTempPath(), $"ppt_hq_image_{Guid.NewGuid()}.png");

                Debug.WriteLine($"尝试高质量导出图片到临时文件: {tempFilePath}");

                // 使用PNG格式导出以保持最高质量
                shape.Export(tempFilePath, PpShapeFormat.ppShapeFormatPNG);

                // 检查文件是否存在
                if (!File.Exists(tempFilePath))
                {
                    throw new Exception("高质量导出的图片文件不存在");
                }

                // 从文件读取图片
                var imageBytes = File.ReadAllBytes(tempFilePath);
                Debug.WriteLine($"成功读取高质量导出的图片文件，大小: {imageBytes.Length} bytes");

                using (var memoryStream = new MemoryStream(imageBytes))
                {
                    // 创建图片副本，避免文件锁定
                    var originalImage = Image.FromStream(memoryStream);
                    var imageCopy = new Bitmap(originalImage);
                    originalImage.Dispose();

                    return imageCopy;
                }
            }
            finally
            {
                // 清理临时文件
                if (!string.IsNullOrEmpty(tempFilePath) && File.Exists(tempFilePath))
                {
                    try
                    {
                        File.Delete(tempFilePath);
                        Debug.WriteLine("已清理高质量导出的临时图片文件");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"清理临时文件失败: {ex.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// 通过导出文件的方式提取图片（标准方法）
        /// </summary>
        private static Image ExtractImageByExport(PowerPoint.Shape shape)
        {
            string tempFilePath = null;
            try
            {
                // 创建临时文件路径
                tempFilePath = Path.Combine(Path.GetTempPath(), $"ppt_image_{Guid.NewGuid()}.png");

                Debug.WriteLine($"尝试导出图片到临时文件: {tempFilePath}");

                // 导出图片到文件
                shape.Export(tempFilePath, PpShapeFormat.ppShapeFormatPNG);

                // 检查文件是否存在
                if (!File.Exists(tempFilePath))
                {
                    throw new Exception("导出的图片文件不存在");
                }

                // 从文件读取图片
                var imageBytes = File.ReadAllBytes(tempFilePath);
                Debug.WriteLine($"成功读取导出的图片文件，大小: {imageBytes.Length} bytes");

                using (var memoryStream = new MemoryStream(imageBytes))
                {
                    // 创建图片副本，避免文件锁定
                    var originalImage = Image.FromStream(memoryStream);
                    var imageCopy = new Bitmap(originalImage);
                    originalImage.Dispose();

                    return imageCopy;
                }
            }
            finally
            {
                // 清理临时文件
                if (!string.IsNullOrEmpty(tempFilePath) && File.Exists(tempFilePath))
                {
                    try
                    {
                        File.Delete(tempFilePath);
                        Debug.WriteLine("已清理临时图片文件");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"清理临时文件失败: {ex.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// 通过剪贴板方式提取图片（传统方法，作为备用）
        /// </summary>
        private static Image ExtractImageByClipboard(PowerPoint.Shape shape)
        {
            // 保存当前剪贴板内容
            object clipboardBackup = null;
            try
            {
                if (Clipboard.ContainsImage())
                {
                    clipboardBackup = Clipboard.GetImage();
                }
                else if (Clipboard.ContainsText())
                {
                    clipboardBackup = Clipboard.GetText();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"备份剪贴板内容失败: {ex.Message}");
            }

            try
            {
                // 复制形状到剪贴板
                shape.Copy();

                // 从剪贴板获取图片
                if (Clipboard.ContainsImage())
                {
                    var image = Clipboard.GetImage();
                    if (image != null)
                    {
                        // 创建图片副本
                        var imageCopy = new Bitmap(image);
                        image.Dispose();
                        return imageCopy;
                    }
                }

                throw new Exception("剪贴板中没有图片数据");
            }
            finally
            {
                // 恢复剪贴板内容
                try
                {
                    if (clipboardBackup != null)
                    {
                        if (clipboardBackup is Image backupImage)
                        {
                            Clipboard.SetImage(backupImage);
                        }
                        else if (clipboardBackup is string backupText)
                        {
                            Clipboard.SetText(backupText);
                        }
                    }
                    else
                    {
                        Clipboard.Clear();
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"恢复剪贴板内容失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 检测PowerPoint图片压缩状态并提供诊断信息
        /// </summary>
        public static string DiagnoseImageCompression(PowerPoint.Shape shape)
        {
            if (shape.Type != Office.MsoShapeType.msoPicture)
            {
                return "选中的形状不是图片";
            }

            try
            {
                var diagnostics = new List<string>();
                diagnostics.Add("=== PowerPoint图片压缩诊断 ===");

                // 检查图片基本信息
                diagnostics.Add($"形状名称: {shape.Name}");
                diagnostics.Add($"显示尺寸: {shape.Width:F2} x {shape.Height:F2} points");

                // 检查图片格式信息（简化版本）
                try
                {
                    // 检查是否为链接图片
                    if (shape.LinkFormat != null)
                    {
                        diagnostics.Add("链接状态: 链接图片");
                        try
                        {
                            if (!string.IsNullOrEmpty(shape.LinkFormat.SourceFullName))
                            {
                                diagnostics.Add($"源文件: {shape.LinkFormat.SourceFullName}");
                                diagnostics.Add($"源文件存在: {File.Exists(shape.LinkFormat.SourceFullName)}");
                            }
                        }
                        catch (Exception ex)
                        {
                            diagnostics.Add($"链接信息访问失败: {ex.Message}");
                        }
                    }
                    else
                    {
                        diagnostics.Add("链接状态: 嵌入图片");
                    }
                }
                catch (Exception ex)
                {
                    diagnostics.Add($"图片信息访问失败: {ex.Message}");
                }

                // 测试不同提取方法的结果
                diagnostics.Add("\n=== 提取方法测试 ===");

                // 测试方法1: 原始数据
                try
                {
                    var originalImage = ExtractOriginalImageData(shape);
                    if (originalImage != null)
                    {
                        diagnostics.Add($"✓ 原始数据方法: {originalImage.Width}x{originalImage.Height}, 格式: {originalImage.PixelFormat}");
                        originalImage.Dispose();
                    }
                    else
                    {
                        diagnostics.Add("✗ 原始数据方法: 失败");
                    }
                }
                catch (Exception ex)
                {
                    diagnostics.Add($"✗ 原始数据方法: 异常 - {ex.Message}");
                }

                // 测试方法2: 高质量导出
                try
                {
                    var hqImage = ExtractImageByHighQualityExport(shape);
                    if (hqImage != null)
                    {
                        diagnostics.Add($"✓ 高质量导出: {hqImage.Width}x{hqImage.Height}, 格式: {hqImage.PixelFormat}");
                        hqImage.Dispose();
                    }
                    else
                    {
                        diagnostics.Add("✗ 高质量导出: 失败");
                    }
                }
                catch (Exception ex)
                {
                    diagnostics.Add($"✗ 高质量导出: 异常 - {ex.Message}");
                }

                // 测试方法3: 标准导出
                try
                {
                    var standardImage = ExtractImageByExport(shape);
                    if (standardImage != null)
                    {
                        diagnostics.Add($"✓ 标准导出: {standardImage.Width}x{standardImage.Height}, 格式: {standardImage.PixelFormat}");
                        standardImage.Dispose();
                    }
                    else
                    {
                        diagnostics.Add("✗ 标准导出: 失败");
                    }
                }
                catch (Exception ex)
                {
                    diagnostics.Add($"✗ 标准导出: 异常 - {ex.Message}");
                }

                // 测试方法4: 剪贴板
                try
                {
                    var clipboardImage = ExtractImageByClipboard(shape);
                    if (clipboardImage != null)
                    {
                        diagnostics.Add($"✓ 剪贴板方法: {clipboardImage.Width}x{clipboardImage.Height}, 格式: {clipboardImage.PixelFormat}");
                        clipboardImage.Dispose();
                    }
                    else
                    {
                        diagnostics.Add("✗ 剪贴板方法: 失败");
                    }
                }
                catch (Exception ex)
                {
                    diagnostics.Add($"✗ 剪贴板方法: 异常 - {ex.Message}");
                }

                diagnostics.Add("\n=== 建议 ===");
                diagnostics.Add("如果插入的JPG图片检测不准确，可能是因为:");
                diagnostics.Add("1. PowerPoint自动压缩了图片质量");
                diagnostics.Add("2. 图片在插入时被重新采样");
                diagnostics.Add("3. 建议使用截图粘贴或PNG格式的高质量图片");

                return string.Join("\n", diagnostics);
            }
            catch (Exception ex)
            {
                return $"诊断失败: {ex.Message}";
            }
        }
    }
}