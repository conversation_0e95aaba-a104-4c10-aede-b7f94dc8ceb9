 using System;
using System.Drawing;
using System.Drawing.Imaging;
using Microsoft.Office.Interop.PowerPoint;
using PBIppt.Models;
using PBIppt.Utils;
using PBIppt.UI;
using PowerPoint = Microsoft.Office.Interop.PowerPoint;

namespace PBIppt.ImageProcessing
{
    /// <summary>
    /// Handles cropping images to focus on batteries
    /// </summary>
    public class ImageCropper
    {
        /// <summary>
        /// Crop a shape to focus on the detected battery
        /// </summary>
        public static void CropShapeToBattery(PowerPoint.Shape shape, Battery battery, float padding = 0.1f)
        {
            try
            {
                if (shape.Type != Microsoft.Office.Core.MsoShapeType.msoPicture)
                {
                    throw new ArgumentException("Shape is not a picture");
                }
                
                // Get the original shape dimensions
                float originalWidth = shape.Width;
                float originalHeight = shape.Height;
                
                // Get the image dimensions
                Image image = ImageExtractor.ExtractImageFromShape(shape);
                float imageWidth = image.Width;
                float imageHeight = image.Height;
                
                // Calculate scale factors between shape and image
                float scaleX = originalWidth / imageWidth;
                float scaleY = originalHeight / imageHeight;
                
                // Add padding to the bounding box
                Rectangle box = battery.BoundingBox;
                int paddingX = (int)(box.Width * padding);
                int paddingY = (int)(box.Height * padding);
                
                // Create padded box, ensuring it doesn't exceed image bounds
                int x = Math.Max(0, box.X - paddingX);
                int y = Math.Max(0, box.Y - paddingY);
                int width = Math.Min(image.Width - x, box.Width + (paddingX * 2));
                int height = Math.Min(image.Height - y, box.Height + (paddingY * 2));
                
                // Convert to percentage of original image (required by PowerPoint API)
                float cropLeft = (float)x / imageWidth;
                float cropTop = (float)y / imageHeight;
                float cropRight = (float)(imageWidth - (x + width)) / imageWidth;
                float cropBottom = (float)(imageHeight - (y + height)) / imageHeight;
                
                // Apply cropping to the shape
                shape.PictureFormat.CropLeft = cropLeft * originalWidth;
                shape.PictureFormat.CropTop = cropTop * originalHeight;
                shape.PictureFormat.CropRight = cropRight * originalWidth;
                shape.PictureFormat.CropBottom = cropBottom * originalHeight;
                
                // Dispose the image
                image.Dispose();
            }
            catch (Exception ex)
            {
                Logger.Exception(ex);
                throw new Exception($"Failed to crop shape: {ex.Message}", ex);
            }
        }
    }
} 