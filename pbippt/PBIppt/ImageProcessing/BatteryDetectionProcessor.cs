using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Diagnostics;
using Microsoft.Office.Interop.PowerPoint;
using PBIppt.ApiClient;
using PBIppt.Models;
using PBIppt.Utils;
using PowerPoint = Microsoft.Office.Interop.PowerPoint;
using Office = Microsoft.Office.Core;

namespace PBIppt.ImageProcessing
{
    /// <summary>
    /// 电芯检测处理器
    /// 负责从PowerPoint图片提取数据，调用检测API，并应用结果
    /// </summary>
    public class BatteryDetectionProcessor
    {
        private readonly IBatteryDetectionApiClient _apiClient;

        /// <summary>
        /// 创建电芯检测处理器
        /// </summary>
        /// <param name="apiClient">电芯检测API客户端</param>
        public BatteryDetectionProcessor(IBatteryDetectionApiClient apiClient = null)
        {
            _apiClient = apiClient ?? new BatteryDetectionApiClient();
            Debug.WriteLine("BatteryDetectionProcessor初始化完成");
        }

        /// <summary>
        /// 确保图片Scale为标准值 - 关键修复
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        private static void EnsureStandardScale(PowerPoint.Shape shape)
        {
            try
            {
                // 重置Scale为100%标准值
                shape.ScaleHeight(1.0f, Microsoft.Office.Core.MsoTriState.msoTrue);
                shape.ScaleWidth(1.0f, Microsoft.Office.Core.MsoTriState.msoTrue);
            }
            catch (Exception)
            {
                // Scale标准化失败时继续执行，不影响主要功能
            }
        }

        /// <summary>
        /// 处理选中的PowerPoint图片进行电芯检测
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="enterCropMode">检测完成后是否进入裁剪模式（默认false，只展示结果）</param>
        /// <returns>处理结果</returns>
        public async Task<BatteryDetectionResult> ProcessShapeAsync(PowerPoint.Shape shape, bool enterCropMode = false)
        {
            // 使用COM对象包装器管理Shape的生命周期
            using (var shapeWrapper = new ComObjectWrapper<PowerPoint.Shape>(shape))
            {
                try
                {
                    // 验证形状类型
                    if (shapeWrapper.Object.Type != Office.MsoShapeType.msoPicture)
                    {
                        return new BatteryDetectionResult
                        {
                            Success = false,
                            ErrorMessage = "选中的对象不是图片，请选择一张图片后重试"
                        };
                    }

                    // 提前获取形状名称，避免COM对象释放后访问
                    string shapeName;
                    try
                    {
                        shapeName = shapeWrapper.Object.Name ?? "UnknownShape";
                    }
                    catch
                    {
                        shapeName = "UnknownShape";
                    }

                    // 关键修复：确保Scale为标准值
                    EnsureStandardScale(shapeWrapper.Object);

                    // 从PowerPoint形状提取图片数据
                var (imageData, actualWidth, actualHeight) = ExtractImageDataFromShapeWithDimensions(shapeWrapper.Object);
                if (imageData == null || imageData.Length == 0)
                {
                    return new BatteryDetectionResult
                    {
                        Success = false,
                        ErrorMessage = "无法从选中的图片中提取数据"
                    };
                }

                // 生成图片ID（使用形状名称和时间戳）
                var imageId = $"{shapeName}_{DateTime.Now:yyyyMMdd_HHmmss}";

                // 调用电芯检测API
                var apiResponse = await _apiClient.DetectSingleImageAsync(imageData, $"{imageId}.jpg", imageId);

                if (!apiResponse.Success)
                {
                    return new BatteryDetectionResult
                    {
                        Success = false,
                        ErrorMessage = $"电芯检测失败: {apiResponse.ErrorMessage}"
                    };
                }

                if (apiResponse.Results == null || apiResponse.Results.Count == 0)
                {
                    return new BatteryDetectionResult
                    {
                        Success = false,
                        ErrorMessage = "检测API没有返回任何结果"
                    };
                }

                // 获取第一个检测结果
                var detectionResult = apiResponse.Results[0];
                
                if (!detectionResult.Success)
                {
                    return new BatteryDetectionResult
                    {
                        Success = false,
                        ErrorMessage = detectionResult.ErrorMessage ?? "检测失败"
                    };
                }

                // 应用检测结果到PowerPoint裁剪，使用实际的图片尺寸
                PowerPointCropHelper.ApplyDetectionResultToCrop(shape, detectionResult, actualWidth, actualHeight);

                return new BatteryDetectionResult
                {
                    Success = true,
                    ProcessingTime = TimeSpan.FromSeconds(1), // 这里可以记录实际处理时间
                    Batteries = new List<Battery>
                    {
                        new Battery
                        {
                            BoundingBox = new Rectangle(
                                detectionResult.CropLeft,
                                detectionResult.CropTop,
                                detectionResult.CropRight - detectionResult.CropLeft,
                                detectionResult.CropBottom - detectionResult.CropTop
                            )
                        }
                    }
                    };
                }
                catch (Exception ex)
                {
                    Logger.Exception(ex);

                    return new BatteryDetectionResult
                    {
                        Success = false,
                        ErrorMessage = $"处理过程中发生错误: {ex.Message}"
                    };
                }
            } // using shapeWrapper
        }

        /// <summary>
        /// 批量处理多个PowerPoint图片形状进行电芯检测
        /// </summary>
        /// <param name="shapes">PowerPoint图片形状列表</param>
        /// <param name="enterCropMode">检测完成后是否进入裁剪模式</param>
        /// <returns>批量处理结果</returns>
        public async Task<BatteryDetectionBatchResult> ProcessMultipleShapesAsync(List<PowerPoint.Shape> shapes, bool enterCropMode = false)
        {
            var result = new BatteryDetectionBatchResult();

            try
            {
                // 提取所有图片数据
                var imageDataList = new List<BatteryDetectionImageData>();
                var shapeImageMap = new Dictionary<string, PowerPoint.Shape>(); // 用于映射图片ID到形状

                for (int i = 0; i < shapes.Count; i++)
                {
                    var shape = shapes[i];

                    try
                    {
                        // 验证形状类型
                        if (shape.Type != Office.MsoShapeType.msoPicture)
                        {
                            continue;
                        }

                        // 提取图片数据和尺寸
                        var (imageData, actualWidth, actualHeight) = ExtractImageDataFromShapeWithDimensions(shape);
                        if (imageData == null || imageData.Length == 0)
                        {
                            result.FailureCount++;
                            result.FailureMessages.Add($"图片 {shape.Name}: 无法提取图片数据");
                            continue;
                        }

                        // 预处理图片：将像素-1保持横纵比（解决加密系统问题）
                        var processedImageData = PreprocessImageForEncryptionIssues(imageData);
                        if (processedImageData != null && processedImageData.Length > 0)
                        {
                            // 更新图片数据和尺寸
                            imageData = processedImageData;
                            var (newWidth, newHeight) = GetImageDimensionsFromData(imageData);
                            Debug.WriteLine($"预处理后图片尺寸: {newWidth}x{newHeight}");
                            actualWidth = newWidth;
                            actualHeight = newHeight;
                        }
                        else
                        {
                            Debug.WriteLine("预处理失败，使用原始图片数据");
                        }

                        // 创建图片数据对象
                        var imageDataObj = new BatteryDetectionImageData
                        {
                            ImageId = shape.Name,
                            FileName = $"{shape.Name}_{DateTime.Now:yyyyMMdd_HHmmss}.jpg",
                            ImageData = imageData
                        };

                        imageDataList.Add(imageDataObj);
                        shapeImageMap[shape.Name] = shape;

                        Debug.WriteLine($"成功提取图片数据: {shape.Name}, 大小: {imageData.Length} bytes, 实际尺寸: {actualWidth}x{actualHeight}");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"提取图片数据失败: {shape.Name}, {ex.Message}");
                        Logger.Exception(ex);
                        result.FailureCount++;
                        result.FailureMessages.Add($"图片 {shape.Name}: {ex.Message}");
                    }
                }

                if (imageDataList.Count == 0)
                {
                    result.ErrorMessage = "没有成功提取到任何图片数据";
                    return result;
                }

                Debug.WriteLine($"成功提取 {imageDataList.Count} 张图片，开始批量检测");

                // 调用批量检测API
                var apiResponse = await _apiClient.DetectMultipleImagesAsync(imageDataList);

                if (!apiResponse.Success)
                {
                    result.ErrorMessage = $"批量检测API调用失败: {apiResponse.ErrorMessage}";
                    return result;
                }

                Debug.WriteLine($"批量检测API调用成功，返回 {apiResponse.Results.Count} 个结果");

                // 处理检测结果并应用到PowerPoint
                foreach (var detectionResult in apiResponse.Results)
                {
                    try
                    {
                        if (detectionResult.Success && shapeImageMap.ContainsKey(detectionResult.ImageId))
                        {
                            var shape = shapeImageMap[detectionResult.ImageId];
                            var imageData = imageDataList.FirstOrDefault(img => img.ImageId == detectionResult.ImageId);

                            if (imageData != null)
                            {
                                // 关键修复：使用与API一致的图片尺寸
                                var (actualWidth, actualHeight) = GetImageDimensionsFromData(imageData.ImageData);

                                // 应用检测结果到PowerPoint裁剪
                                PowerPointCropHelper.ApplyDetectionResultToCrop(shape, detectionResult, actualWidth, actualHeight);

                                result.SuccessCount++;
                            }
                        }
                        else
                        {
                            result.FailureCount++;
                            result.FailureMessages.Add($"图片 {detectionResult.ImageId}: 检测失败或找不到对应形状");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"应用检测结果失败: {detectionResult.ImageId}, {ex.Message}");
                        Logger.Exception(ex);
                        result.FailureCount++;
                        result.FailureMessages.Add($"图片 {detectionResult.ImageId}: {ex.Message}");
                    }
                }

                result.Success = result.SuccessCount > 0;
                Debug.WriteLine($"批量处理完成 - 成功: {result.SuccessCount}, 失败: {result.FailureCount}");

                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"批量处理过程中发生错误: {ex.Message}");
                Logger.Exception(ex);
                result.ErrorMessage = $"批量处理失败: {ex.Message}";
                return result;
            }
        }

        /// <summary>
        /// 从图片数据获取尺寸信息
        /// </summary>
        /// <param name="imageData">图片字节数组</param>
        /// <returns>图片宽度和高度</returns>
        private (int width, int height) GetImageDimensionsFromData(byte[] imageData)
        {
            try
            {
                using (var memoryStream = new MemoryStream(imageData))
                using (var image = Image.FromStream(memoryStream))
                {
                    return (image.Width, image.Height);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取图片尺寸失败: {ex.Message}");
                return (0, 0);
            }
        }

        /// <summary>
        /// 从PowerPoint形状中提取图片数据
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <returns>图片的字节数组</returns>
        private byte[] ExtractImageDataFromShape(PowerPoint.Shape shape)
        {
            try
            {
                Debug.WriteLine("开始从PowerPoint形状提取图片数据");

                // 使用现有的ImageExtractor提取图片
                var image = ImageExtractor.ExtractImageFromShape(shape);
                
                if (image == null)
                {
                    Debug.WriteLine("ImageExtractor返回null");
                    return null;
                }

                Debug.WriteLine($"提取到图片，尺寸: {image.Width}x{image.Height}, 格式: {image.PixelFormat}");

                // 将图片转换为字节数组（JPEG格式）
                using (var memoryStream = new MemoryStream())
                {
                    // 保存为JPEG格式，质量设置为90%
                    var jpegEncoder = GetJpegEncoder();
                    var encoderParams = new EncoderParameters(1);
                    encoderParams.Param[0] = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, 90L);

                    image.Save(memoryStream, jpegEncoder, encoderParams);
                    
                    var imageData = memoryStream.ToArray();
                    Debug.WriteLine($"图片转换完成，JPEG数据大小: {imageData.Length} bytes");

                    // 释放图片资源
                    image.Dispose();

                    return imageData;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"提取图片数据失败: {ex.Message}");
                Logger.Exception(ex);
                return null;
            }
        }

        /// <summary>
        /// 获取JPEG编码器
        /// </summary>
        /// <returns>JPEG图像编码器</returns>
        private ImageCodecInfo GetJpegEncoder()
        {
            var codecs = ImageCodecInfo.GetImageEncoders();
            foreach (var codec in codecs)
            {
                if (codec.FormatID == ImageFormat.Jpeg.Guid)
                {
                    return codec;
                }
            }
            return null;
        }

        /// <summary>
        /// 预处理图片以解决加密系统问题：将像素-1保持横纵比
        /// </summary>
        /// <param name="imageData">原始图片数据</param>
        /// <returns>预处理后的图片数据，失败返回原始数据</returns>
        private byte[] PreprocessImageForEncryptionIssues(byte[] imageData)
        {
            try
            {
                if (imageData == null || imageData.Length == 0)
                {
                    Debug.WriteLine("预处理失败：图片数据为空");
                    return imageData;
                }

                using (var inputStream = new MemoryStream(imageData))
                using (var originalImage = Image.FromStream(inputStream))
                {
                    // 计算新尺寸：宽高各减1像素，保持横纵比
                    int newWidth = Math.Max(1, originalImage.Width - 1);
                    int newHeight = Math.Max(1, originalImage.Height - 1);
                    
                    Debug.WriteLine($"🔧 预处理图片以解决加密系统问题: {originalImage.Width}x{originalImage.Height} -> {newWidth}x{newHeight}");

                    // 创建新的图片
                    using (var resizedImage = new Bitmap(newWidth, newHeight))
                    using (var graphics = Graphics.FromImage(resizedImage))
                    {
                        // 设置高质量缩放
                        graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                        graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                        graphics.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
                        graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;

                        // 绘制缩放后的图片
                        graphics.DrawImage(originalImage, 0, 0, newWidth, newHeight);

                        // 保存为JPEG格式的byte[]
                        using (var outputStream = new MemoryStream())
                        {
                            // 保存为JPEG格式，质量设置为90%
                            var jpegEncoder = GetJpegEncoder();
                            var encoderParams = new EncoderParameters(1);
                            encoderParams.Param[0] = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, 90L);

                            resizedImage.Save(outputStream, jpegEncoder, encoderParams);
                            
                            var processedImageData = outputStream.ToArray();
                            Debug.WriteLine($"图片预处理完成，处理后大小: {processedImageData.Length} bytes");
                            return processedImageData;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ 图片预处理失败: {ex.Message}");
                Logger.Exception(ex);
                return imageData; // 预处理失败时返回原始数据
            }
        }

        /// <summary>
        /// 从PowerPoint形状中提取图片数据和尺寸信息
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <returns>图片的字节数组和实际像素尺寸</returns>
        private (byte[] imageData, int width, int height) ExtractImageDataFromShapeWithDimensions(PowerPoint.Shape shape)
        {
            try
            {
                Debug.WriteLine("开始从PowerPoint形状提取图片数据和尺寸");

                // 使用现有的ImageExtractor提取图片
                var image = ImageExtractor.ExtractImageFromShape(shape);

                if (image == null)
                {
                    Debug.WriteLine("ImageExtractor返回null");
                    return (null, 0, 0);
                }

                // 保存原始尺寸信息，避免在Dispose后访问
                int originalWidth = image.Width;
                int originalHeight = image.Height;

                Debug.WriteLine($"提取到图片，原始尺寸: {originalWidth}x{originalHeight}, 格式: {image.PixelFormat}");

                // 将图片转换为字节数组（JPEG格式）
                byte[] imageData;
                using (var memoryStream = new MemoryStream())
                {
                    // 保存为JPEG格式，质量设置为90%
                    var jpegEncoder = GetJpegEncoder();
                    var encoderParams = new EncoderParameters(1);
                    encoderParams.Param[0] = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, 90L);

                    image.Save(memoryStream, jpegEncoder, encoderParams);
                    imageData = memoryStream.ToArray();
                    Debug.WriteLine($"图片转换完成，JPEG数据大小: {imageData.Length} bytes");
                }

                // 释放原始图片资源
                image.Dispose();

                // 关键修复：从实际的字节数组获取真实尺寸（与API接收的一致）
                var (actualWidth, actualHeight) = GetImageDimensionsFromData(imageData);

                Debug.WriteLine($"=== 图片尺寸一致性检查 ===");
                Debug.WriteLine($"原始提取尺寸: {originalWidth}x{originalHeight}");
                Debug.WriteLine($"字节数组真实尺寸: {actualWidth}x{actualHeight}");

                if (originalWidth != actualWidth || originalHeight != actualHeight)
                {
                    Debug.WriteLine("⚠️ 检测到尺寸不一致！使用字节数组的真实尺寸");
                }
                else
                {
                    Debug.WriteLine("✓ 尺寸一致");
                }

                return (imageData, actualWidth, actualHeight);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"提取图片数据和尺寸失败: {ex.Message}");
                Logger.Exception(ex);
                return (null, 0, 0);
            }
        }



        /// <summary>
        /// 测试API连接
        /// </summary>
        /// <returns>连接测试结果</returns>
        public async Task<bool> TestApiConnectionAsync()
        {
            try
            {
                Debug.WriteLine("测试电芯检测API连接");
                return await _apiClient.TestConnectionAsync();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"测试API连接失败: {ex.Message}");
                Logger.Exception(ex);
                return false;
            }
        }

        /// <summary>
        /// 获取API客户端信息
        /// </summary>
        /// <returns>API客户端信息</returns>
        public string GetApiClientInfo()
        {
            return "电芯检测API客户端 - 服务器: http://10.5.100.15:8000";
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _apiClient?.Dispose();
        }
    }

    /// <summary>
    /// 批量电芯检测结果
    /// </summary>
    public class BatteryDetectionBatchResult
    {
        /// <summary>
        /// 是否成功（至少有一张图片检测成功）
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 成功处理的图片数量
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败的图片数量
        /// </summary>
        public int FailureCount { get; set; }

        /// <summary>
        /// 失败消息列表
        /// </summary>
        public List<string> FailureMessages { get; set; } = new List<string>();

        /// <summary>
        /// 错误消息（整体处理失败时）
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 总处理数量
        /// </summary>
        public int TotalCount => SuccessCount + FailureCount;
    }
}
