# 电芯检测图片处理 - API接口文档

## 接口概览

本文档描述了PowerPoint VSTO插件中使用的所有API接口，包括数据获取、文件下载和电芯检测等功能。

## 1. 安全测试数据获取接口

### 接口信息
- **URL**: `/safetyTest/getPictureBySafetyTestIds`
- **方法**: `POST`
- **基础地址**: `http://localhost:82`
- **认证**: Bearer Token

### 请求参数
```json
{
    "safetyTestIds": "1907295047497265154,1907295047497265155",
    "cycleTime": "all"
}
```

#### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| safetyTestIds | string | 是 | 安全测试ID列表，逗号分隔 |
| cycleTime | string | 是 | 时间周期，通常为"all" |

### 响应格式
```json
{
    "success": true,
    "code": 200,
    "message": "请求成功",
    "data": {
        "allSafetyTestList": [
            {
                "createTime": "2025-04-02 12:53:10",
                "sampleNo": "202412170004-0001",
                "picType": "大面2",
                "fileId": "1907295213944025090",
                "fileName": "202409120007-0001_2024-10-17_16-20-22_60℃&50%SOC高温存储1_大面2.JPG"
            }
        ]
    }
}
```

### 使用示例
```csharp
var requestData = new {
    safetyTestIds = "1907295047497265154",
    cycleTime = "all"
};

var response = await httpClient.PostAsync(
    "http://localhost:82/safetyTest/getPictureBySafetyTestIds",
    new StringContent(JsonConvert.SerializeObject(requestData), 
    Encoding.UTF8, "application/json"));
```

## 2. Minio文件下载接口

### 接口信息
- **URL**: `/minioFile/getFileUrl`
- **方法**: `GET`
- **基础地址**: `http://localhost:82`
- **认证**: Bearer Token

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| fileId | string | 是 | 文件ID |

### 响应格式
```json
{
    "success": true,
    "code": 200,
    "message": "请求成功",
    "data": "http://***********:9000/test/20250701/1939963048167264258/filename.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=..."
}
```

### 使用示例
```csharp
var url = $"http://localhost:82/minioFile/getFileUrl?fileId={fileId}";
var response = await httpClient.GetAsync(url);
var jsonResponse = await response.Content.ReadAsStringAsync();
var result = JsonConvert.DeserializeObject<MinioUrlResponse>(jsonResponse);
var downloadUrl = result.Data;

// 下载实际文件
var imageData = await httpClient.GetByteArrayAsync(downloadUrl);
```

## 3. 批量电芯检测接口

### 接口信息
- **URL**: `/detect/`
- **方法**: `POST`
- **基础地址**: `http://************:8011`
- **认证**: 无需认证
- **超时**: 30秒

### 请求格式
```json
{
    "images": [
        {
            "id": "1939963048167264258",
            "data": "base64_encoded_image_data_here"
        },
        {
            "id": "1907295213944025090", 
            "data": "base64_encoded_image_data_here"
        }
    ]
}
```

#### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| images | array | 是 | 图片数组 |
| images[].id | string | 是 | 图片唯一标识符 |
| images[].data | string | 是 | Base64编码的图片数据 |

### 响应格式
```json
{
    "results": [
        {
            "ImageId": "1939963048167264258",
            "CropLeft": 312,
            "CropTop": 355,
            "CropRight": 440,
            "CropBottom": 203,
            "CropLeftPercent": 0.305,
            "CropTopPercent": 0.462,
            "CropRightPercent": 0.430,
            "CropBottomPercent": 0.264,
            "Confidence": 0.932,
            "DPI": "120x120"
        }
    ]
}
```

#### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| ImageId | string | 图片ID |
| CropLeft | int | 左边裁剪像素数 |
| CropTop | int | 顶部裁剪像素数 |
| CropRight | int | 右边裁剪像素数 |
| CropBottom | int | 底部裁剪像素数 |
| CropLeftPercent | float | 左边裁剪百分比（推荐使用） |
| CropTopPercent | float | 顶部裁剪百分比（推荐使用） |
| CropRightPercent | float | 右边裁剪百分比（推荐使用） |
| CropBottomPercent | float | 底部裁剪百分比（推荐使用） |
| Confidence | float | 检测置信度 (0-1) |
| DPI | string | 图片DPI信息 |

### 使用示例
```csharp
var requestData = new {
    images = imageDataList.Select(img => new {
        id = img.ImageId,
        data = Convert.ToBase64String(img.ImageData)
    }).ToArray()
};

var json = JsonConvert.SerializeObject(requestData);
var content = new StringContent(json, Encoding.UTF8, "application/json");

var response = await httpClient.PostAsync(
    "http://************:8011/detect/", content);
var responseJson = await response.Content.ReadAsStringAsync();
var result = JsonConvert.DeserializeObject<BatteryDetectionResponse>(responseJson);
```

## 认证机制

### JWT Token获取
Token通过AuthManager统一管理：
```csharp
// 设置Token
AuthManager.SetAuthToken(jwtToken);

// 获取Token
var token = AuthManager.GetAuthToken();

// 设置请求头
httpClient.DefaultRequestHeaders.Authorization = 
    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
```

### Token格式
```
Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
```

## 错误处理

### HTTP状态码
| 状态码 | 说明 | 处理方式 |
|--------|------|----------|
| 200 | 成功 | 正常处理 |
| 400 | 请求参数错误 | 检查请求格式 |
| 401 | 认证失败 | 刷新Token |
| 403 | 权限不足 | 检查用户权限 |
| 404 | 资源不存在 | 检查URL和参数 |
| 500 | 服务器错误 | 重试或联系管理员 |
| 503 | 服务不可用 | 等待后重试 |

### 重试策略
```csharp
const int maxRetries = 3;
const int baseDelayMs = 500;

for (int attempt = 1; attempt <= maxRetries; attempt++) {
    try {
        var response = await httpClient.SendAsync(request);
        if (response.IsSuccessStatusCode) {
            return response;
        }
        
        // 5xx错误才重试
        if ((int)response.StatusCode >= 500 && attempt < maxRetries) {
            await Task.Delay(baseDelayMs * attempt);
            continue;
        }
        
        throw new HttpRequestException($"API调用失败: {response.StatusCode}");
    } catch (TaskCanceledException) when (attempt < maxRetries) {
        // 超时重试
        await Task.Delay(baseDelayMs * attempt);
    }
}
```

## 性能优化

### 1. 图片压缩
在发送到检测API前，对大图片进行压缩：
```csharp
// 压缩阈值
const int maxWidth = 1024;
const int maxHeight = 1024;
const int maxSizeBytes = 1024 * 1024; // 1MB

if (imageWidth > maxWidth || imageHeight > maxHeight || 
    imageData.Length > maxSizeBytes) {
    compressedData = CompressImage(imageData, maxWidth, maxHeight);
}
```

### 2. 并发控制
```csharp
// 限制并发数量
var semaphore = new SemaphoreSlim(5, 5); // 最多5个并发

var tasks = imageList.Select(async image => {
    await semaphore.WaitAsync();
    try {
        return await ProcessImage(image);
    } finally {
        semaphore.Release();
    }
});

var results = await Task.WhenAll(tasks);
```

### 3. 连接池配置
```csharp
var handler = new HttpClientHandler() {
    MaxConnectionsPerServer = 10
};

var httpClient = new HttpClient(handler) {
    Timeout = TimeSpan.FromSeconds(30)
};
```

## 监控和日志

### 请求日志
```csharp
Logger.Info($"发送API请求: {method} {url}");
Logger.Info($"请求参数: {JsonConvert.SerializeObject(requestData)}");
Logger.Info($"响应状态: {response.StatusCode}");
Logger.Info($"响应时间: {stopwatch.ElapsedMilliseconds}ms");
```

### 错误日志
```csharp
Logger.Error($"API调用失败: {url}");
Logger.Error($"错误信息: {ex.Message}");
Logger.Error($"状态码: {response?.StatusCode}");
Logger.Exception(ex); // 记录完整异常信息
```

---

**文档版本**: v1.0  
**创建日期**: 2025-07-02  
**API版本**: v1.0  
**维护团队**: PowerPoint VSTO 开发团队
