using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using PBIppt.ApiClient;
using PBIppt.Utils;
using PowerPoint = Microsoft.Office.Interop.PowerPoint;

namespace PBIppt.ImageProcessing
{
    /// <summary>
    /// 电池表格图片处理器
    /// 专门处理表格图片的电芯检测和裁剪功能
    /// </summary>
    public class BatteryTableImageProcessor
    {
        private readonly IBatteryDetectionApiClient _detectionApiClient;


        /// <summary>
        /// 创建电池表格图片处理器
        /// </summary>
        /// <param name="detectionApiClient">电芯检测API客户端（可选，默认创建新实例）</param>
        public BatteryTableImageProcessor(IBatteryDetectionApiClient detectionApiClient = null)
        {
            _detectionApiClient = detectionApiClient ?? new BatteryDetectionApiClient();
        }



        /// <summary>
        /// 批量检测多张图片中的电芯区域
        /// </summary>
        /// <param name="imageDataList">图片数据列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>电芯检测结果列表</returns>
        public async Task<List<BatteryDetectionApiResult>> DetectBatteryInMultipleImages(
            List<BatteryDetectionImageData> imageDataList,
            CancellationToken cancellationToken = default)
        {
            try
            {
                if (imageDataList == null || imageDataList.Count == 0)
                {
                    Logger.Warning("图片数据列表为空，无法进行批量检测");
                    return new List<BatteryDetectionApiResult>();
                }

                // 过滤有效的图片数据并进行预处理
                var validImageDataList = new List<BatteryDetectionImageData>();
                foreach (var imageData in imageDataList)
                {
                    if (imageData.ImageData != null && imageData.ImageData.Length > 0)
                    {
                        // 预处理图片：将像素-1保持横纵比（解决加密系统问题）
                        var processedImageData = PreprocessImageForEncryptionIssues(imageData);
                        if (processedImageData != null)
                        {
                            validImageDataList.Add(processedImageData);
                        }
                        else
                        {
                            // 如果预处理失败，使用原始数据
                            validImageDataList.Add(imageData);
                        }
                    }
                }

                if (validImageDataList.Count == 0)
                {
                    Logger.Warning("没有有效的图片数据可供检测");
                    return new List<BatteryDetectionApiResult>();
                }

                // 调用批量检测API
                var detectionResult = await _detectionApiClient.DetectMultipleImagesAsync(validImageDataList);

                // 检查检测结果
                if (!detectionResult.Success || detectionResult.Results == null)
                {
                    Logger.Warning($"批量电芯检测失败: {detectionResult.ErrorMessage}");
                    return new List<BatteryDetectionApiResult>();
                }

                // 验证和修复所有检测结果
                var validResults = new List<BatteryDetectionApiResult>();
                for (int i = 0; i < detectionResult.Results.Count; i++)
                {
                    var detection = detectionResult.Results[i];
                    var originalImageData = i < validImageDataList.Count ? validImageDataList[i].ImageData : null;

                    if (detection.Success && originalImageData != null)
                    {
                        var fixedDetection = ValidateAndFixDetectionResult(detection, originalImageData);
                        if (fixedDetection != null)
                        {
                            validResults.Add(fixedDetection);

                        }
                    }
                }
                return validResults;
            }
            catch (Exception ex)
            {
                Logger.Warning($"批量电芯检测处理失败: {ex.Message}");
                return new List<BatteryDetectionApiResult>();
            }
        }

        /// <summary>
        /// 应用检测结果到PowerPoint图片形状进行裁剪
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="detection">检测结果</param>
        /// <param name="actualPixelWidth">实际图片像素宽度</param>
        /// <param name="actualPixelHeight">实际图片像素高度</param>
        /// <returns>是否成功应用裁剪</returns>
        public bool ApplyDetectionToPowerPointShape(PowerPoint.Shape shape, BatteryDetectionApiResult detection, int actualPixelWidth, int actualPixelHeight)
        {
            try
            {
                if (shape == null || detection == null)
                {
                    Logger.Warning("PowerPoint形状或检测结果为空，无法应用裁剪");
                    return false;
                }



                // 使用现有的PowerPointCropHelper来应用裁剪
                PowerPointCropHelper.ApplyDetectionResultToCrop(shape, detection, actualPixelWidth, actualPixelHeight);


                return true;
            }
            catch (Exception ex)
            {
                Logger.Exception(ex);
                Logger.Warning($"❌ 应用PowerPoint裁剪失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 验证检测结果并计算百分比坐标
        /// </summary>
        /// <param name="detection">原始检测结果</param>
        /// <param name="imageData">图片数据</param>
        /// <returns>增强后的检测结果，如果验证失败则返回null</returns>
        private BatteryDetectionApiResult ValidateAndFixDetectionResult(BatteryDetectionApiResult detection, byte[] imageData)
        {
            try
            {
                // 获取图片尺寸
                var (imageWidth, imageHeight) = GetImageDimensionsFromData(imageData);



                // 使用API返回的原始坐标（不修复顺序）
                int left = detection.CropLeft;
                int top = detection.CropTop;
                int right = detection.CropRight;
                int bottom = detection.CropBottom;

                // 基本范围验证（允许API返回的特殊坐标格式）
                if (left < 0 || top < 0 || right < 0 || bottom < 0 ||
                    left > imageWidth || top > imageHeight || right > imageWidth || bottom > imageHeight)
                {
                    return null;
                }

                // 创建增强后的检测结果
                var enhancedDetection = new BatteryDetectionApiResult
                {
                    ImageId = detection.ImageId,
                    CropLeft = left,
                    CropTop = top,
                    CropRight = right,
                    CropBottom = bottom,
                    CropLeftPercent = detection.CropLeftPercent,
                    CropTopPercent = detection.CropTopPercent,
                    CropRightPercent = detection.CropRightPercent,
                    CropBottomPercent = detection.CropBottomPercent,
                    CenterXPercent = detection.CenterXPercent,
                    CenterYPercent = detection.CenterYPercent,
                    WidthPercent = detection.WidthPercent,     // 🎯 关键：复制width_percent
                    HeightPercent = detection.HeightPercent,   // 🎯 关键：复制height_percent
                    Confidence = detection.Confidence,
                    ImageWidth = detection.ImageWidth, // 保持API返回的image_info尺寸
                    ImageHeight = detection.ImageHeight, // 保持API返回的image_info尺寸
                    ImageDpi = detection.ImageDpi,
                    Success = true
                };

                // 计算百分比坐标（如果API没有返回或返回0）
                if (detection.CropLeftPercent == 0.0f && detection.CropTopPercent == 0.0f &&
                    detection.CropRightPercent == 0.0f && detection.CropBottomPercent == 0.0f)
                {
                    enhancedDetection.CropLeftPercent = (float)left / imageWidth;
                    enhancedDetection.CropTopPercent = (float)top / imageHeight;
                    enhancedDetection.CropRightPercent = (float)right / imageWidth;
                    enhancedDetection.CropBottomPercent = (float)bottom / imageHeight;

                    Logger.Info($"计算百分比坐标: Left={enhancedDetection.CropLeftPercent:F3}, Top={enhancedDetection.CropTopPercent:F3}, Right={enhancedDetection.CropRightPercent:F3}, Bottom={enhancedDetection.CropBottomPercent:F3}");
                }
                else
                {
                    enhancedDetection.CropLeftPercent = detection.CropLeftPercent;
                    enhancedDetection.CropTopPercent = detection.CropTopPercent;
                    enhancedDetection.CropRightPercent = detection.CropRightPercent;
                    enhancedDetection.CropBottomPercent = detection.CropBottomPercent;
                    Logger.Info($"使用API返回的百分比坐标");
                }

                Logger.Info($"验证通过，保持API原始坐标: Left={enhancedDetection.CropLeft}, Top={enhancedDetection.CropTop}, Right={enhancedDetection.CropRight}, Bottom={enhancedDetection.CropBottom}");
                return enhancedDetection;
            }
            catch (Exception ex)
            {
                Logger.Warning($"验证检测结果失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从图片数据获取图片尺寸（简化版，直接使用System.Drawing自动处理EXIF）
        /// </summary>
        /// <param name="imageData">图片数据</param>
        /// <returns>图片宽度和高度</returns>
        private (int width, int height) GetImageDimensionsFromData(byte[] imageData)
        {
            try
            {
                using (var stream = new MemoryStream(imageData))
                using (var image = Image.FromStream(stream))
                {
                    // 简化策略：直接使用System.Drawing自动处理的尺寸
                    // System.Drawing.Image.FromStream()会自动应用EXIF旋转
                    Logger.Info($"📐 图片尺寸: {image.Width}x{image.Height} (System.Drawing自动处理EXIF)");
                    return (image.Width, image.Height);
                }
            }
            catch (Exception ex)
            {
                Logger.Warning($"⚠️ 从图片数据获取尺寸失败（可能受加密系统影响）: {ex.Message}，使用默认尺寸");
                return (800, 600); // 默认尺寸
            }
        }

        /// <summary>
        /// 预处理图片以解决加密系统问题：将像素-1保持横纵比
        /// </summary>
        /// <param name="originalImageData">原始图片数据</param>
        /// <returns>预处理后的图片数据，失败返回null</returns>
        private BatteryDetectionImageData PreprocessImageForEncryptionIssues(BatteryDetectionImageData originalImageData)
        {
            try
            {
                using (var inputStream = new MemoryStream(originalImageData.ImageData))
                using (var originalImage = Image.FromStream(inputStream))
                {
                    // 计算新尺寸：宽高各减1像素，保持横纵比
                    int newWidth = Math.Max(1, originalImage.Width - 1);
                    int newHeight = Math.Max(1, originalImage.Height - 1);
                    
                    Logger.Info($"🔧 预处理图片以解决加密系统问题: {originalImage.Width}x{originalImage.Height} -> {newWidth}x{newHeight}");

                    // 创建新的图片
                    using (var resizedImage = new Bitmap(newWidth, newHeight))
                    using (var graphics = Graphics.FromImage(resizedImage))
                    {
                        // 设置高质量缩放
                        graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                        graphics.SmoothingMode = SmoothingMode.HighQuality;
                        graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;
                        graphics.CompositingQuality = CompositingQuality.HighQuality;

                        // 绘制缩放后的图片
                        graphics.DrawImage(originalImage, 0, 0, newWidth, newHeight);

                        // 保存为JPEG格式的byte[]
                        using (var outputStream = new MemoryStream())
                        {
                            // 使用原始图片格式，如果无法确定则使用JPEG
                            var format = originalImage.RawFormat;
                            if (format == null || format.Equals(ImageFormat.MemoryBmp))
                            {
                                format = ImageFormat.Jpeg;
                            }

                            resizedImage.Save(outputStream, format);
                            
                            // 创建新的BatteryDetectionImageData
                            return new BatteryDetectionImageData
                            {
                                ImageId = originalImageData.ImageId,
                                ImageData = outputStream.ToArray()
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Warning($"⚠️ 图片预处理失败 (ImageId: {originalImageData.ImageId}): {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _detectionApiClient?.Dispose();
        }
    }
}
