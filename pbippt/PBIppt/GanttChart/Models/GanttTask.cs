using System;
using System.Collections.Generic;
using System.Drawing;
using Newtonsoft.Json;

namespace PBIppt.GanttChart.Models
{
    /// <summary>
    /// 甘特图任务模型
    /// </summary>
    public class GanttTask
    {
        /// <summary>
        /// 任务唯一标识符
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 任务描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 任务进度（0-100）
        /// </summary>
        public double Progress { get; set; }

        /// <summary>
        /// 任务状态
        /// </summary>
        public TaskStatus Status { get; set; }

        /// <summary>
        /// 责任人
        /// </summary>
        public string ResponsiblePerson { get; set; }

        /// <summary>
        /// 任务颜色（十六进制格式）
        /// </summary>
        public string Color { get; set; }

        /// <summary>
        /// 是否在关键路径上
        /// </summary>
        public bool IsOnCriticalPath { get; set; }

        /// <summary>
        /// 任务优先级（1-5，5为最高）
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 依赖关系列表
        /// </summary>
        public List<TaskDependency> Dependencies { get; set; }

        /// <summary>
        /// 任务在甘特图中的行索引
        /// </summary>
        public int RowIndex { get; set; }

        /// <summary>
        /// 是否为汇总任务
        /// </summary>
        public bool IsSummaryTask { get; set; }

        /// <summary>
        /// 父任务ID（用于任务层级结构）
        /// </summary>
        public string ParentTaskId { get; set; }

        /// <summary>
        /// 子任务列表
        /// </summary>
        public List<string> SubTaskIds { get; set; }

        /// <summary>
        /// 任务层级（0为顶级任务）
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 任务备注
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModifiedDate { get; set; }

        /// <summary>
        /// 自定义属性字典
        /// </summary>
        public Dictionary<string, object> CustomProperties { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public GanttTask()
        {
            Id = Guid.NewGuid().ToString();
            Name = string.Empty;
            Description = string.Empty;
            StartDate = DateTime.Today;
            EndDate = DateTime.Today.AddDays(1);
            Progress = 0.0;
            Status = TaskStatus.NotStarted;
            ResponsiblePerson = string.Empty;
            Color = GanttConstants.DefaultColors.TaskBar;
            IsOnCriticalPath = false;
            Priority = 3; // 默认中等优先级
            Dependencies = new List<TaskDependency>();
            RowIndex = 0;
            IsSummaryTask = false;
            ParentTaskId = null;
            SubTaskIds = new List<string>();
            Level = 0;
            Notes = string.Empty;
            CreatedDate = DateTime.Now;
            LastModifiedDate = DateTime.Now;
            CustomProperties = new Dictionary<string, object>();
        }

        /// <summary>
        /// 构造函数（带基本参数）
        /// </summary>
        /// <param name="name">任务名称</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        public GanttTask(string name, DateTime startDate, DateTime endDate) : this()
        {
            Name = name;
            StartDate = startDate;
            EndDate = endDate;
        }

        /// <summary>
        /// 获取任务持续时间（天数）
        /// </summary>
        [JsonIgnore]
        public int DurationDays
        {
            get { return (EndDate - StartDate).Days + 1; }
        }

        /// <summary>
        /// 获取任务是否已完成
        /// </summary>
        [JsonIgnore]
        public bool IsCompleted
        {
            get { return Status == TaskStatus.Completed || Progress >= 100.0; }
        }

        /// <summary>
        /// 获取任务是否延期
        /// </summary>
        [JsonIgnore]
        public bool IsDelayed
        {
            get 
            { 
                return Status == TaskStatus.Delayed || 
                       (DateTime.Today > EndDate && !IsCompleted); 
            }
        }

        /// <summary>
        /// 获取任务颜色（Color对象）
        /// </summary>
        [JsonIgnore]
        public Color TaskColor
        {
            get
            {
                try
                {
                    return ColorTranslator.FromHtml(Color);
                }
                catch
                {
                    return ColorTranslator.FromHtml(GanttConstants.DefaultColors.TaskBar);
                }
            }
        }

        /// <summary>
        /// 验证任务数据有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(Name))
            {
                result.AddError("任务名称不能为空");
            }

            if (StartDate > EndDate)
            {
                result.AddError("开始日期不能晚于结束日期");
            }

            if (Progress < 0 || Progress > 100)
            {
                result.AddError("进度必须在0-100之间");
            }

            if (Priority < 1 || Priority > 5)
            {
                result.AddError("优先级必须在1-5之间");
            }

            return result;
        }

        /// <summary>
        /// 克隆任务
        /// </summary>
        /// <returns>任务副本</returns>
        public GanttTask Clone()
        {
            var json = JsonConvert.SerializeObject(this);
            var clone = JsonConvert.DeserializeObject<GanttTask>(json);
            clone.Id = Guid.NewGuid().ToString(); // 生成新的ID
            return clone;
        }

        /// <summary>
        /// 更新最后修改时间
        /// </summary>
        public void UpdateLastModified()
        {
            LastModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"{Name} ({StartDate:yyyy-MM-dd} - {EndDate:yyyy-MM-dd}, {Progress:F1}%)";
        }
    }

    /// <summary>
    /// 验证结果类
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; private set; } = true;
        public List<string> Errors { get; private set; } = new List<string>();

        public void AddError(string error)
        {
            Errors.Add(error);
            IsValid = false;
        }
    }
}
