using System;

namespace PBIppt.GanttChart.Models
{
    /// <summary>
    /// 任务状态枚举
    /// </summary>
    public enum TaskStatus
    {
        /// <summary>
        /// 未开始
        /// </summary>
        NotStarted,

        /// <summary>
        /// 进行中
        /// </summary>
        InProgress,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed,

        /// <summary>
        /// 延期
        /// </summary>
        Delayed,

        /// <summary>
        /// 暂停
        /// </summary>
        OnHold
    }

    /// <summary>
    /// 任务优先级枚举
    /// </summary>
    public enum TaskPriority
    {
        /// <summary>
        /// 低优先级
        /// </summary>
        Low,

        /// <summary>
        /// 普通优先级
        /// </summary>
        Normal,

        /// <summary>
        /// 中等优先级
        /// </summary>
        Medium,

        /// <summary>
        /// 高优先级
        /// </summary>
        High,

        /// <summary>
        /// 紧急优先级
        /// </summary>
        Critical
    }

    /// <summary>
    /// 依赖关系类型枚举（简化版，仅支持FS）
    /// </summary>
    public enum DependencyType
    {
        /// <summary>
        /// 完成-开始依赖（最常用）
        /// </summary>
        FinishToStart
    }

    /// <summary>
    /// 里程碑形状枚举
    /// </summary>
    public enum MilestoneShape
    {
        /// <summary>
        /// 菱形
        /// </summary>
        Diamond,

        /// <summary>
        /// 圆形
        /// </summary>
        Circle,

        /// <summary>
        /// 三角形
        /// </summary>
        Triangle,

        /// <summary>
        /// 星形
        /// </summary>
        Star
    }

    /// <summary>
    /// 时间轴刻度枚举
    /// </summary>
    public enum TimelineScale
    {
        /// <summary>
        /// 年
        /// </summary>
        Year,
        
        /// <summary>
        /// 月
        /// </summary>
        Month,
        
        /// <summary>
        /// 周
        /// </summary>
        Week,
        
        /// <summary>
        /// 日
        /// </summary>
        Day
    }

    /// <summary>
    /// 工作周类型枚举
    /// </summary>
    public enum WorkWeekType
    {
        /// <summary>
        /// 5天工作制
        /// </summary>
        FiveDays,
        
        /// <summary>
        /// 7天工作制
        /// </summary>
        SevenDays
    }

    /// <summary>
    /// 甘特图布局类型
    /// </summary>
    public enum GanttLayoutType
    {
        /// <summary>
        /// 标准布局
        /// </summary>
        Standard,
        
        /// <summary>
        /// 紧凑布局
        /// </summary>
        Compact,
        
        /// <summary>
        /// 详细布局
        /// </summary>
        Detailed
    }

    /// <summary>
    /// 甘特图常量定义
    /// </summary>
    public static class GanttConstants
    {
        /// <summary>
        /// 默认任务条高度（磅）
        /// </summary>
        public const double DefaultTaskBarHeight = 20.0;

        /// <summary>
        /// 默认行间距（磅）
        /// </summary>
        public const double DefaultRowSpacing = 5.0;

        /// <summary>
        /// 默认时间轴高度（磅）
        /// </summary>
        public const double DefaultTimelineHeight = 60.0;

        /// <summary>
        /// 默认标签列宽度（磅）
        /// </summary>
        public const double DefaultLabelColumnWidth = 150.0;

        /// <summary>
        /// 最小任务持续时间（天）
        /// </summary>
        public const int MinTaskDurationDays = 1;

        /// <summary>
        /// 最大支持任务数量
        /// </summary>
        public const int MaxSupportedTasks = 200;

        /// <summary>
        /// 默认颜色方案
        /// </summary>
        public static class DefaultColors
        {
            public const string TaskBar = "#4472C4";
            public const string CompletedTask = "#70AD47";
            public const string DelayedTask = "#E74C3C";
            public const string Milestone = "#FFC000";
            public const string CriticalPath = "#C5504B";
            public const string WeekendShade = "#F2F2F2";
            public const string GridLine = "#D9D9D9";
        }

        /// <summary>
        /// 预设日期格式
        /// </summary>
        public static class DateFormats
        {
            public const string YearMonth = "yyyy-MM";
            public const string MonthDay = "MM-dd";
            public const string ShortDate = "yyyy-MM-dd";
            public const string LongDate = "yyyy年MM月dd日";
            public const string WeekDay = "MM-dd (ddd)";
        }
    }
}
