using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;

namespace PBIppt.GanttChart.Models
{
    /// <summary>
    /// 时间轴设置模型
    /// </summary>
    public class TimelineSettings
    {
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 显示的时间刻度列表
        /// </summary>
        public List<TimelineScale> VisibleScales { get; set; }

        /// <summary>
        /// 是否显示周末
        /// </summary>
        public bool ShowWeekends { get; set; }

        /// <summary>
        /// 是否为周末添加阴影
        /// </summary>
        public bool ShadeWeekends { get; set; }

        /// <summary>
        /// 工作周类型
        /// </summary>
        public WorkWeekType WorkWeek { get; set; }

        /// <summary>
        /// 日期格式列表（按刻度级别）
        /// </summary>
        public Dictionary<TimelineScale, string> DateFormats { get; set; }

        /// <summary>
        /// 是否自动调整时间范围
        /// </summary>
        public bool AutoAdjustTimeRange { get; set; }

        /// <summary>
        /// 时间范围缓冲天数（自动调整时使用）
        /// </summary>
        public int TimeRangeBufferDays { get; set; }

        /// <summary>
        /// 最小显示单位（天）
        /// </summary>
        public int MinDisplayUnitDays { get; set; }

        /// <summary>
        /// 时间轴高度（磅）
        /// </summary>
        public double TimelineHeight { get; set; }

        /// <summary>
        /// 是否显示今日线
        /// </summary>
        public bool ShowTodayLine { get; set; }

        /// <summary>
        /// 今日线颜色
        /// </summary>
        public string TodayLineColor { get; set; }

        /// <summary>
        /// 网格线颜色
        /// </summary>
        public string GridLineColor { get; set; }

        /// <summary>
        /// 周末阴影颜色
        /// </summary>
        public string WeekendShadeColor { get; set; }

        /// <summary>
        /// 自定义假期列表
        /// </summary>
        public List<Holiday> CustomHolidays { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public TimelineSettings()
        {
            StartDate = DateTime.Today;
            EndDate = DateTime.Today.AddMonths(3);
            VisibleScales = new List<TimelineScale> { TimelineScale.Month, TimelineScale.Week };
            ShowWeekends = true;
            ShadeWeekends = true;
            WorkWeek = WorkWeekType.FiveDays;
            DateFormats = GetDefaultDateFormats();
            AutoAdjustTimeRange = true;
            TimeRangeBufferDays = 7;
            MinDisplayUnitDays = 1;
            TimelineHeight = GanttConstants.DefaultTimelineHeight;
            ShowTodayLine = true;
            TodayLineColor = "#FF0000";
            GridLineColor = GanttConstants.DefaultColors.GridLine;
            WeekendShadeColor = GanttConstants.DefaultColors.WeekendShade;
            CustomHolidays = new List<Holiday>();
        }

        /// <summary>
        /// 获取默认日期格式
        /// </summary>
        /// <returns>默认日期格式字典</returns>
        private Dictionary<TimelineScale, string> GetDefaultDateFormats()
        {
            return new Dictionary<TimelineScale, string>
            {
                { TimelineScale.Year, "yyyy年" },
                { TimelineScale.Month, "MM月" },
                { TimelineScale.Week, "第W周" },
                { TimelineScale.Day, "dd日" }
            };
        }

        /// <summary>
        /// 获取总天数
        /// </summary>
        [JsonIgnore]
        public int TotalDays
        {
            get { return (EndDate - StartDate).Days + 1; }
        }

        /// <summary>
        /// 获取工作日总数
        /// </summary>
        [JsonIgnore]
        public int WorkingDays
        {
            get
            {
                int workingDays = 0;
                var current = StartDate;
                
                while (current <= EndDate)
                {
                    if (IsWorkingDay(current))
                        workingDays++;
                    current = current.AddDays(1);
                }
                
                return workingDays;
            }
        }

        /// <summary>
        /// 判断指定日期是否为工作日
        /// </summary>
        /// <param name="date">日期</param>
        /// <returns>是否为工作日</returns>
        public bool IsWorkingDay(DateTime date)
        {
            // 检查是否为周末
            if (WorkWeek == WorkWeekType.FiveDays)
            {
                if (date.DayOfWeek == DayOfWeek.Saturday || date.DayOfWeek == DayOfWeek.Sunday)
                    return false;
            }

            // 检查是否为自定义假期
            if (CustomHolidays.Any(h => h.Date.Date == date.Date))
                return false;

            return true;
        }

        /// <summary>
        /// 判断指定日期是否为周末
        /// </summary>
        /// <param name="date">日期</param>
        /// <returns>是否为周末</returns>
        public bool IsWeekend(DateTime date)
        {
            return date.DayOfWeek == DayOfWeek.Saturday || date.DayOfWeek == DayOfWeek.Sunday;
        }

        /// <summary>
        /// 根据数据自动调整时间范围
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="milestones">里程碑列表</param>
        public void AutoAdjustRange(List<GanttTask> tasks, List<GanttMilestone> milestones)
        {
            if (!AutoAdjustTimeRange)
                return;

            var allDates = new List<DateTime>();

            // 收集所有任务的日期
            if (tasks != null)
            {
                foreach (var task in tasks)
                {
                    allDates.Add(task.StartDate);
                    allDates.Add(task.EndDate);
                }
            }

            // 收集所有里程碑的日期
            if (milestones != null)
            {
                allDates.AddRange(milestones.Select(m => m.Date));
            }

            if (allDates.Any())
            {
                var minDate = allDates.Min();
                var maxDate = allDates.Max();

                // 添加缓冲时间
                StartDate = minDate.AddDays(-TimeRangeBufferDays);
                EndDate = maxDate.AddDays(TimeRangeBufferDays);
            }
        }

        /// <summary>
        /// 自动选择合适的时间刻度
        /// </summary>
        public void AutoSelectScales()
        {
            var totalDays = TotalDays;
            VisibleScales.Clear();

            if (totalDays <= 30)
            {
                // 30天以内：显示周和日
                VisibleScales.Add(TimelineScale.Week);
                VisibleScales.Add(TimelineScale.Day);
            }
            else if (totalDays <= 90)
            {
                // 3个月以内：显示月和周
                VisibleScales.Add(TimelineScale.Month);
                VisibleScales.Add(TimelineScale.Week);
            }
            else if (totalDays <= 365)
            {
                // 1年以内：显示月
                VisibleScales.Add(TimelineScale.Month);
            }
            else
            {
                // 1年以上：显示年和月
                VisibleScales.Add(TimelineScale.Year);
                VisibleScales.Add(TimelineScale.Month);
            }
        }

        /// <summary>
        /// 验证设置有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (StartDate > EndDate)
            {
                result.AddError("开始日期不能晚于结束日期");
            }

            if (TotalDays > 3650) // 10年
            {
                result.AddError("时间范围不能超过10年");
            }

            if (VisibleScales == null || !VisibleScales.Any())
            {
                result.AddError("至少需要选择一个时间刻度");
            }

            if (TimelineHeight < 20 || TimelineHeight > 200)
            {
                result.AddError("时间轴高度必须在20-200磅之间");
            }

            return result;
        }

        /// <summary>
        /// 克隆设置
        /// </summary>
        /// <returns>设置副本</returns>
        public TimelineSettings Clone()
        {
            var json = JsonConvert.SerializeObject(this);
            return JsonConvert.DeserializeObject<TimelineSettings>(json);
        }
    }

    /// <summary>
    /// 假期模型
    /// </summary>
    public class Holiday
    {
        /// <summary>
        /// 假期日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 假期名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 是否为年度重复假期
        /// </summary>
        public bool IsRecurring { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public Holiday()
        {
            Date = DateTime.Today;
            Name = string.Empty;
            IsRecurring = false;
        }

        /// <summary>
        /// 构造函数（带参数）
        /// </summary>
        /// <param name="date">假期日期</param>
        /// <param name="name">假期名称</param>
        /// <param name="isRecurring">是否年度重复</param>
        public Holiday(DateTime date, string name, bool isRecurring = false)
        {
            Date = date;
            Name = name;
            IsRecurring = isRecurring;
        }
    }
}
