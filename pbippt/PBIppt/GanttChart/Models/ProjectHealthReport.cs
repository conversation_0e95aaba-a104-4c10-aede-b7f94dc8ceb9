using System;
using System.Collections.Generic;

namespace PBIppt.GanttChart.Models
{
    /// <summary>
    /// 项目健康度报告
    /// </summary>
    public class ProjectHealthReport
    {
        /// <summary>
        /// 项目名称
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime GeneratedDate { get; set; }

        /// <summary>
        /// 整体健康度
        /// </summary>
        public ProjectHealth OverallHealth { get; set; }

        /// <summary>
        /// 健康度描述
        /// </summary>
        public string HealthDescription { get; set; }

        /// <summary>
        /// 项目进度（百分比）
        /// </summary>
        public double ProjectProgress { get; set; }

        /// <summary>
        /// 关键任务数量
        /// </summary>
        public int CriticalTaskCount { get; set; }

        /// <summary>
        /// 改进建议
        /// </summary>
        public List<string> Recommendations { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ProjectHealthReport()
        {
            GeneratedDate = DateTime.Now;
            Recommendations = new List<string>();
        }
    }

    /// <summary>
    /// 项目健康度枚举
    /// </summary>
    public enum ProjectHealth
    {
        /// <summary>
        /// 优秀
        /// </summary>
        Excellent,

        /// <summary>
        /// 良好
        /// </summary>
        Good,

        /// <summary>
        /// 一般
        /// </summary>
        Fair,

        /// <summary>
        /// 差
        /// </summary>
        Poor,

        /// <summary>
        /// 危险
        /// </summary>
        Critical
    }
}
