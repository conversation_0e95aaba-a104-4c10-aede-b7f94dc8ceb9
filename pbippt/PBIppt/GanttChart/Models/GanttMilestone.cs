using System;
using System.Collections.Generic;
using System.Drawing;
using Newtonsoft.Json;

namespace PBIppt.GanttChart.Models
{
    /// <summary>
    /// 甘特图里程碑模型
    /// </summary>
    public class GanttMilestone
    {
        /// <summary>
        /// 里程碑唯一标识符
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 里程碑名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 里程碑描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 里程碑日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 里程碑形状
        /// </summary>
        public MilestoneShape Shape { get; set; }

        /// <summary>
        /// 里程碑颜色（十六进制格式）
        /// </summary>
        public string Color { get; set; }

        /// <summary>
        /// 是否在关键路径上
        /// </summary>
        public bool IsOnCriticalPath { get; set; }

        /// <summary>
        /// 里程碑优先级（1-5，5为最高）
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 责任人
        /// </summary>
        public string ResponsiblePerson { get; set; }

        /// <summary>
        /// 里程碑在甘特图中的行索引
        /// </summary>
        public int RowIndex { get; set; }

        /// <summary>
        /// 关联的任务ID列表
        /// </summary>
        public List<string> RelatedTaskIds { get; set; }

        /// <summary>
        /// 里程碑状态
        /// </summary>
        public MilestoneStatus Status { get; set; }

        /// <summary>
        /// 里程碑类型
        /// </summary>
        public MilestoneType Type { get; set; }

        /// <summary>
        /// 里程碑备注
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModifiedDate { get; set; }

        /// <summary>
        /// 自定义属性字典
        /// </summary>
        public Dictionary<string, object> CustomProperties { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public GanttMilestone()
        {
            Id = Guid.NewGuid().ToString();
            Name = string.Empty;
            Description = string.Empty;
            Date = DateTime.Today;
            Shape = MilestoneShape.Diamond;
            Color = GanttConstants.DefaultColors.Milestone;
            IsOnCriticalPath = false;
            Priority = 3; // 默认中等优先级
            ResponsiblePerson = string.Empty;
            RowIndex = 0;
            RelatedTaskIds = new List<string>();
            Status = MilestoneStatus.Pending;
            Type = MilestoneType.Standard;
            Notes = string.Empty;
            CreatedDate = DateTime.Now;
            LastModifiedDate = DateTime.Now;
            CustomProperties = new Dictionary<string, object>();
        }

        /// <summary>
        /// 构造函数（带基本参数）
        /// </summary>
        /// <param name="name">里程碑名称</param>
        /// <param name="date">里程碑日期</param>
        public GanttMilestone(string name, DateTime date) : this()
        {
            Name = name;
            Date = date;
        }

        /// <summary>
        /// 获取里程碑颜色（Color对象）
        /// </summary>
        [JsonIgnore]
        public Color MilestoneColor
        {
            get
            {
                try
                {
                    return ColorTranslator.FromHtml(Color);
                }
                catch
                {
                    return ColorTranslator.FromHtml(GanttConstants.DefaultColors.Milestone);
                }
            }
        }

        /// <summary>
        /// 获取里程碑是否已完成
        /// </summary>
        [JsonIgnore]
        public bool IsCompleted
        {
            get { return Status == MilestoneStatus.Completed; }
        }

        /// <summary>
        /// 获取里程碑是否延期
        /// </summary>
        [JsonIgnore]
        public bool IsDelayed
        {
            get 
            { 
                return Status == MilestoneStatus.Delayed || 
                       (DateTime.Today > Date && !IsCompleted); 
            }
        }

        /// <summary>
        /// 验证里程碑数据有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(Name))
            {
                result.AddError("里程碑名称不能为空");
            }

            if (Priority < 1 || Priority > 5)
            {
                result.AddError("优先级必须在1-5之间");
            }

            return result;
        }

        /// <summary>
        /// 克隆里程碑
        /// </summary>
        /// <returns>里程碑副本</returns>
        public GanttMilestone Clone()
        {
            var json = JsonConvert.SerializeObject(this);
            var clone = JsonConvert.DeserializeObject<GanttMilestone>(json);
            clone.Id = Guid.NewGuid().ToString(); // 生成新的ID
            return clone;
        }

        /// <summary>
        /// 更新最后修改时间
        /// </summary>
        public void UpdateLastModified()
        {
            LastModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"{Name} ({Date:yyyy-MM-dd})";
        }
    }

    /// <summary>
    /// 里程碑状态枚举
    /// </summary>
    public enum MilestoneStatus
    {
        /// <summary>
        /// 待完成
        /// </summary>
        Pending,
        
        /// <summary>
        /// 已完成
        /// </summary>
        Completed,
        
        /// <summary>
        /// 延期
        /// </summary>
        Delayed,
        
        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled
    }

    /// <summary>
    /// 里程碑类型枚举
    /// </summary>
    public enum MilestoneType
    {
        /// <summary>
        /// 标准里程碑
        /// </summary>
        Standard,
        
        /// <summary>
        /// 项目开始
        /// </summary>
        ProjectStart,
        
        /// <summary>
        /// 项目结束
        /// </summary>
        ProjectEnd,
        
        /// <summary>
        /// 阶段完成
        /// </summary>
        PhaseCompletion,
        
        /// <summary>
        /// 关键决策点
        /// </summary>
        DecisionPoint,
        
        /// <summary>
        /// 交付物
        /// </summary>
        Deliverable
    }
}
