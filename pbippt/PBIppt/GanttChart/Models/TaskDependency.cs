using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace PBIppt.GanttChart.Models
{
    /// <summary>
    /// 任务依赖关系模型
    /// </summary>
    public class TaskDependency
    {
        /// <summary>
        /// 依赖关系唯一标识符
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 前置任务ID
        /// </summary>
        public string PredecessorTaskId { get; set; }

        /// <summary>
        /// 后续任务ID
        /// </summary>
        public string SuccessorTaskId { get; set; }

        /// <summary>
        /// 前置任务ID（别名，用于兼容性）
        /// </summary>
        [JsonIgnore]
        public string PredecessorId
        {
            get => PredecessorTaskId;
            set => PredecessorTaskId = value;
        }

        /// <summary>
        /// 后续任务ID（别名，用于兼容性）
        /// </summary>
        [JsonIgnore]
        public string SuccessorId
        {
            get => SuccessorTaskId;
            set => SuccessorTaskId = value;
        }

        /// <summary>
        /// 前置任务对象（运行时填充）
        /// </summary>
        [JsonIgnore]
        public GanttTask PredecessorTask { get; set; }

        /// <summary>
        /// 后续任务对象（运行时填充）
        /// </summary>
        [JsonIgnore]
        public GanttTask SuccessorTask { get; set; }

        /// <summary>
        /// 依赖关系类型（简化版，仅支持FS）
        /// </summary>
        public DependencyType Type { get; set; }

        /// <summary>
        /// 延迟天数（可以为负数表示提前）
        /// </summary>
        public int LagDays { get; set; }

        /// <summary>
        /// 依赖关系描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 是否为硬依赖（true）还是软依赖（false）
        /// </summary>
        public bool IsHardDependency { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModifiedDate { get; set; }

        /// <summary>
        /// 自定义属性字典
        /// </summary>
        public Dictionary<string, object> CustomProperties { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public TaskDependency()
        {
            Id = Guid.NewGuid().ToString();
            PredecessorTaskId = string.Empty;
            SuccessorTaskId = string.Empty;
            Type = DependencyType.FinishToStart;
            LagDays = 0;
            Description = string.Empty;
            IsHardDependency = true;
            CreatedDate = DateTime.Now;
            LastModifiedDate = DateTime.Now;
            CustomProperties = new Dictionary<string, object>();
        }

        /// <summary>
        /// 构造函数（带基本参数）
        /// </summary>
        /// <param name="predecessorTaskId">前置任务ID</param>
        /// <param name="successorTaskId">后续任务ID</param>
        public TaskDependency(string predecessorTaskId, string successorTaskId) : this()
        {
            PredecessorTaskId = predecessorTaskId;
            SuccessorTaskId = successorTaskId;
        }

        /// <summary>
        /// 构造函数（带完整参数）
        /// </summary>
        /// <param name="predecessorTaskId">前置任务ID</param>
        /// <param name="successorTaskId">后续任务ID</param>
        /// <param name="type">依赖类型</param>
        /// <param name="lagDays">延迟天数</param>
        public TaskDependency(string predecessorTaskId, string successorTaskId, 
                             DependencyType type, int lagDays) : this()
        {
            PredecessorTaskId = predecessorTaskId;
            SuccessorTaskId = successorTaskId;
            Type = type;
            LagDays = lagDays;
        }

        /// <summary>
        /// 获取依赖关系类型的显示名称
        /// </summary>
        [JsonIgnore]
        public string TypeDisplayName
        {
            get
            {
                switch (Type)
                {
                    case DependencyType.FinishToStart:
                        return "完成-开始";
                    default:
                        return "未知";
                }
            }
        }

        /// <summary>
        /// 获取延迟描述
        /// </summary>
        [JsonIgnore]
        public string LagDescription
        {
            get
            {
                if (LagDays == 0)
                    return "无延迟";
                else if (LagDays > 0)
                    return $"延迟{LagDays}天";
                else
                    return $"提前{Math.Abs(LagDays)}天";
            }
        }

        /// <summary>
        /// 验证依赖关系数据有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(PredecessorTaskId))
            {
                result.AddError("前置任务ID不能为空");
            }

            if (string.IsNullOrWhiteSpace(SuccessorTaskId))
            {
                result.AddError("后续任务ID不能为空");
            }

            if (PredecessorTaskId == SuccessorTaskId)
            {
                result.AddError("前置任务和后续任务不能是同一个任务");
            }

            if (Math.Abs(LagDays) > 365)
            {
                result.AddError("延迟天数不能超过365天");
            }

            return result;
        }

        /// <summary>
        /// 检查是否会形成循环依赖
        /// </summary>
        /// <param name="allDependencies">所有依赖关系</param>
        /// <returns>是否形成循环依赖</returns>
        public bool WouldCreateCycle(List<TaskDependency> allDependencies)
        {
            var visited = new HashSet<string>();
            var recursionStack = new HashSet<string>();

            return HasCycleDFS(SuccessorTaskId, PredecessorTaskId, allDependencies, visited, recursionStack);
        }

        /// <summary>
        /// 深度优先搜索检测循环依赖
        /// </summary>
        private bool HasCycleDFS(string currentTask, string targetTask, 
                                List<TaskDependency> dependencies, 
                                HashSet<string> visited, 
                                HashSet<string> recursionStack)
        {
            if (currentTask == targetTask)
                return true;

            if (recursionStack.Contains(currentTask))
                return true;

            if (visited.Contains(currentTask))
                return false;

            visited.Add(currentTask);
            recursionStack.Add(currentTask);

            foreach (var dep in dependencies)
            {
                if (dep.PredecessorTaskId == currentTask)
                {
                    if (HasCycleDFS(dep.SuccessorTaskId, targetTask, dependencies, visited, recursionStack))
                        return true;
                }
            }

            recursionStack.Remove(currentTask);
            return false;
        }

        /// <summary>
        /// 克隆依赖关系
        /// </summary>
        /// <returns>依赖关系副本</returns>
        public TaskDependency Clone()
        {
            var json = JsonConvert.SerializeObject(this);
            var clone = JsonConvert.DeserializeObject<TaskDependency>(json);
            clone.Id = Guid.NewGuid().ToString(); // 生成新的ID
            return clone;
        }

        /// <summary>
        /// 更新最后修改时间
        /// </summary>
        public void UpdateLastModified()
        {
            LastModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"{TypeDisplayName}: {PredecessorTaskId} -> {SuccessorTaskId} ({LagDescription})";
        }

        /// <summary>
        /// 比较两个依赖关系是否相等
        /// </summary>
        /// <param name="obj">比较对象</param>
        /// <returns>是否相等</returns>
        public override bool Equals(object obj)
        {
            if (obj is TaskDependency other)
            {
                return PredecessorTaskId == other.PredecessorTaskId &&
                       SuccessorTaskId == other.SuccessorTaskId &&
                       Type == other.Type;
            }
            return false;
        }

        /// <summary>
        /// 获取哈希码
        /// </summary>
        /// <returns>哈希码</returns>
        public override int GetHashCode()
        {
            return $"{PredecessorTaskId}_{SuccessorTaskId}_{Type}".GetHashCode();
        }
    }
}
