using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;

namespace PBIppt.GanttChart.Models
{
    /// <summary>
    /// 甘特图项目模型 - 整个甘特图的容器
    /// </summary>
    public class GanttProject
    {
        /// <summary>
        /// 项目唯一标识符
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 项目描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 项目经理
        /// </summary>
        public string ProjectManager { get; set; }

        /// <summary>
        /// 任务列表
        /// </summary>
        public List<GanttTask> Tasks { get; set; }

        /// <summary>
        /// 里程碑列表
        /// </summary>
        public List<GanttMilestone> Milestones { get; set; }

        /// <summary>
        /// 依赖关系列表
        /// </summary>
        public List<TaskDependency> Dependencies { get; set; }

        /// <summary>
        /// 时间轴设置
        /// </summary>
        public TimelineSettings TimelineSettings { get; set; }

        /// <summary>
        /// 甘特图布局类型
        /// </summary>
        public GanttLayoutType LayoutType { get; set; }

        /// <summary>
        /// 项目状态
        /// </summary>
        public ProjectStatus Status { get; set; }

        /// <summary>
        /// 项目开始日期
        /// </summary>
        public DateTime ProjectStartDate { get; set; }

        /// <summary>
        /// 项目结束日期
        /// </summary>
        public DateTime ProjectEndDate { get; set; }

        /// <summary>
        /// 项目进度（0-100）
        /// </summary>
        public double ProjectProgress { get; set; }

        /// <summary>
        /// 关键路径任务ID列表
        /// </summary>
        public List<string> CriticalPathTaskIds { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModifiedDate { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// 自定义属性字典
        /// </summary>
        public Dictionary<string, object> CustomProperties { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public GanttProject()
        {
            Id = Guid.NewGuid().ToString();
            Name = "新建甘特图项目";
            Description = string.Empty;
            ProjectManager = string.Empty;
            Tasks = new List<GanttTask>();
            Milestones = new List<GanttMilestone>();
            Dependencies = new List<TaskDependency>();
            TimelineSettings = new TimelineSettings();
            LayoutType = GanttLayoutType.Standard;
            Status = ProjectStatus.Planning;
            ProjectStartDate = DateTime.Today;
            ProjectEndDate = DateTime.Today.AddMonths(3);
            ProjectProgress = 0.0;
            CriticalPathTaskIds = new List<string>();
            CreatedDate = DateTime.Now;
            LastModifiedDate = DateTime.Now;
            Version = "1.0";
            CustomProperties = new Dictionary<string, object>();
        }

        /// <summary>
        /// 构造函数（带项目名称）
        /// </summary>
        /// <param name="name">项目名称</param>
        public GanttProject(string name) : this()
        {
            Name = name;
        }

        /// <summary>
        /// 获取项目总任务数
        /// </summary>
        [JsonIgnore]
        public int TotalTasks
        {
            get { return Tasks?.Count ?? 0; }
        }

        /// <summary>
        /// 获取已完成任务数
        /// </summary>
        [JsonIgnore]
        public int CompletedTasks
        {
            get { return Tasks?.Count(t => t.IsCompleted) ?? 0; }
        }

        /// <summary>
        /// 获取延期任务数
        /// </summary>
        [JsonIgnore]
        public int DelayedTasks
        {
            get { return Tasks?.Count(t => t.IsDelayed) ?? 0; }
        }

        /// <summary>
        /// 获取项目实际开始日期
        /// </summary>
        [JsonIgnore]
        public DateTime ActualStartDate
        {
            get
            {
                if (Tasks == null || !Tasks.Any())
                    return ProjectStartDate;
                return Tasks.Min(t => t.StartDate);
            }
        }

        /// <summary>
        /// 获取项目实际结束日期
        /// </summary>
        [JsonIgnore]
        public DateTime ActualEndDate
        {
            get
            {
                if (Tasks == null || !Tasks.Any())
                    return ProjectEndDate;
                return Tasks.Max(t => t.EndDate);
            }
        }

        /// <summary>
        /// 添加任务
        /// </summary>
        /// <param name="task">任务</param>
        public void AddTask(GanttTask task)
        {
            if (task == null) return;
            
            task.RowIndex = Tasks.Count;
            Tasks.Add(task);
            UpdateLastModified();
            
            // 自动调整时间范围
            if (TimelineSettings.AutoAdjustTimeRange)
            {
                TimelineSettings.AutoAdjustRange(Tasks, Milestones);
            }
        }

        /// <summary>
        /// 移除任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveTask(string taskId)
        {
            var task = Tasks.FirstOrDefault(t => t.Id == taskId);
            if (task == null) return false;

            // 移除相关依赖关系
            Dependencies.RemoveAll(d => d.PredecessorTaskId == taskId || d.SuccessorTaskId == taskId);
            
            // 移除任务
            Tasks.Remove(task);
            
            // 重新分配行索引
            for (int i = 0; i < Tasks.Count; i++)
            {
                Tasks[i].RowIndex = i;
            }
            
            UpdateLastModified();
            return true;
        }

        /// <summary>
        /// 添加里程碑
        /// </summary>
        /// <param name="milestone">里程碑</param>
        public void AddMilestone(GanttMilestone milestone)
        {
            if (milestone == null) return;
            
            milestone.RowIndex = Tasks.Count + Milestones.Count;
            Milestones.Add(milestone);
            UpdateLastModified();
            
            // 自动调整时间范围
            if (TimelineSettings.AutoAdjustTimeRange)
            {
                TimelineSettings.AutoAdjustRange(Tasks, Milestones);
            }
        }

        /// <summary>
        /// 移除里程碑
        /// </summary>
        /// <param name="milestoneId">里程碑ID</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveMilestone(string milestoneId)
        {
            var milestone = Milestones.FirstOrDefault(m => m.Id == milestoneId);
            if (milestone == null) return false;

            Milestones.Remove(milestone);
            UpdateLastModified();
            return true;
        }

        /// <summary>
        /// 添加依赖关系
        /// </summary>
        /// <param name="dependency">依赖关系</param>
        /// <returns>是否成功添加</returns>
        public bool AddDependency(TaskDependency dependency)
        {
            if (dependency == null) return false;

            // 验证依赖关系
            var validation = dependency.Validate();
            if (!validation.IsValid) return false;

            // 检查是否会形成循环依赖
            if (dependency.WouldCreateCycle(Dependencies)) return false;

            // 检查是否已存在相同依赖
            if (Dependencies.Any(d => d.Equals(dependency))) return false;

            Dependencies.Add(dependency);
            UpdateLastModified();
            return true;
        }

        /// <summary>
        /// 移除依赖关系
        /// </summary>
        /// <param name="dependencyId">依赖关系ID</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveDependency(string dependencyId)
        {
            var dependency = Dependencies.FirstOrDefault(d => d.Id == dependencyId);
            if (dependency == null) return false;

            Dependencies.Remove(dependency);
            UpdateLastModified();
            return true;
        }

        /// <summary>
        /// 计算项目整体进度
        /// </summary>
        public void CalculateProjectProgress()
        {
            if (Tasks == null || !Tasks.Any())
            {
                ProjectProgress = 0.0;
                return;
            }

            var totalWeight = Tasks.Sum(t => t.DurationDays);
            var completedWeight = Tasks.Sum(t => t.DurationDays * (t.Progress / 100.0));
            
            ProjectProgress = totalWeight > 0 ? (completedWeight / totalWeight) * 100.0 : 0.0;
            UpdateLastModified();
        }

        /// <summary>
        /// 验证项目数据有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(Name))
            {
                result.AddError("项目名称不能为空");
            }

            if (ProjectStartDate > ProjectEndDate)
            {
                result.AddError("项目开始日期不能晚于结束日期");
            }

            // 验证任务
            foreach (var task in Tasks)
            {
                var taskValidation = task.Validate();
                if (!taskValidation.IsValid)
                {
                    foreach (var error in taskValidation.Errors)
                    {
                        result.AddError($"任务 '{task.Name}': {error}");
                    }
                }
            }

            // 验证里程碑
            foreach (var milestone in Milestones)
            {
                var milestoneValidation = milestone.Validate();
                if (!milestoneValidation.IsValid)
                {
                    foreach (var error in milestoneValidation.Errors)
                    {
                        result.AddError($"里程碑 '{milestone.Name}': {error}");
                    }
                }
            }

            // 验证依赖关系
            foreach (var dependency in Dependencies)
            {
                var depValidation = dependency.Validate();
                if (!depValidation.IsValid)
                {
                    foreach (var error in depValidation.Errors)
                    {
                        result.AddError($"依赖关系: {error}");
                    }
                }
            }

            // 验证时间轴设置
            var timelineValidation = TimelineSettings.Validate();
            if (!timelineValidation.IsValid)
            {
                foreach (var error in timelineValidation.Errors)
                {
                    result.AddError($"时间轴设置: {error}");
                }
            }

            return result;
        }

        /// <summary>
        /// 更新最后修改时间
        /// </summary>
        public void UpdateLastModified()
        {
            LastModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// 克隆项目
        /// </summary>
        /// <returns>项目副本</returns>
        public GanttProject Clone()
        {
            var json = JsonConvert.SerializeObject(this);
            var clone = JsonConvert.DeserializeObject<GanttProject>(json);
            clone.Id = Guid.NewGuid().ToString(); // 生成新的ID
            return clone;
        }
    }

    /// <summary>
    /// 项目状态枚举
    /// </summary>
    public enum ProjectStatus
    {
        /// <summary>
        /// 规划中
        /// </summary>
        Planning,
        
        /// <summary>
        /// 进行中
        /// </summary>
        InProgress,
        
        /// <summary>
        /// 已完成
        /// </summary>
        Completed,
        
        /// <summary>
        /// 已暂停
        /// </summary>
        Paused,
        
        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled
    }
}
