using System;
using System.Drawing;
using Microsoft.Office.Interop.PowerPoint;
using PBIppt.GanttChart.Models;
using DrawingPoint = System.Drawing.Point;
using PowerPointPoint = Microsoft.Office.Interop.PowerPoint.Point;

namespace PBIppt.GanttChart.v2.Components
{
    /// <summary>
    /// 甘特图组件统一接口
    /// 定义v2.0版本的核心组件能力
    /// </summary>
    public interface IGanttChartComponent : IDisposable
    {
        #region 基本属性
        
        /// <summary>
        /// 组件唯一标识
        /// </summary>
        string ComponentId { get; }
        
        /// <summary>
        /// 组件当前状态
        /// </summary>
        ComponentState State { get; }
        
        /// <summary>
        /// 是否已初始化
        /// </summary>
        bool IsInitialized { get; }
        
        /// <summary>
        /// 是否处于编辑模式
        /// </summary>
        bool IsEditMode { get; set; }
        
        /// <summary>
        /// 关联的PowerPoint幻灯片
        /// </summary>
        Slide ParentSlide { get; }
        
        /// <summary>
        /// 甘特图项目数据
        /// </summary>
        GanttProject Project { get; set; }
        
        #endregion
        
        #region 生命周期管理
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        /// <param name="slide">目标幻灯片</param>
        /// <param name="project">甘特图项目</param>
        /// <param name="bounds">组件边界</param>
        void Initialize(Slide slide, GanttProject project, Rectangle bounds);
        
        /// <summary>
        /// 更新组件显示
        /// </summary>
        void Update();
        
        /// <summary>
        /// 刷新组件渲染
        /// </summary>
        void Refresh();
        
        /// <summary>
        /// 激活组件（获得焦点）
        /// </summary>
        void Activate();
        
        /// <summary>
        /// 停用组件（失去焦点）
        /// </summary>
        void Deactivate();

        /// <summary>
        /// 渲染甘特图到PowerPoint幻灯片
        /// </summary>
        void Render();

        #endregion
        
        #region 交互事件处理
        
        /// <summary>
        /// 处理鼠标事件
        /// </summary>
        /// <param name="e">鼠标事件参数</param>
        /// <returns>是否处理了该事件</returns>
        bool HandleMouseEvent(MouseEventArgs e);
        
        /// <summary>
        /// 处理键盘事件
        /// </summary>
        /// <param name="e">键盘事件参数</param>
        /// <returns>是否处理了该事件</returns>
        bool HandleKeyboardEvent(KeyboardEventArgs e);
        
        /// <summary>
        /// 显示上下文菜单
        /// </summary>
        /// <param name="location">菜单显示位置</param>
        /// <param name="context">上下文信息</param>
        void ShowContextMenu(DrawingPoint location, object context = null);

        /// <summary>
        /// 开始拖拽操作
        /// </summary>
        /// <param name="startPoint">拖拽起始点</param>
        /// <param name="dragObject">拖拽对象</param>
        /// <returns>是否成功开始拖拽</returns>
        bool StartDrag(DrawingPoint startPoint, object dragObject);

        /// <summary>
        /// 更新拖拽状态
        /// </summary>
        /// <param name="currentPoint">当前鼠标位置</param>
        void UpdateDrag(DrawingPoint currentPoint);

        /// <summary>
        /// 完成拖拽操作
        /// </summary>
        /// <param name="endPoint">拖拽结束点</param>
        /// <returns>是否成功完成拖拽</returns>
        bool CompleteDrag(DrawingPoint endPoint);
        
        /// <summary>
        /// 取消拖拽操作
        /// </summary>
        void CancelDrag();
        
        #endregion
        
        #region 编辑功能
        
        /// <summary>
        /// 开始直接编辑
        /// </summary>
        /// <param name="editTarget">编辑目标</param>
        /// <param name="editType">编辑类型</param>
        /// <returns>是否成功开始编辑</returns>
        bool StartDirectEdit(object editTarget, EditType editType);
        
        /// <summary>
        /// 完成直接编辑
        /// </summary>
        /// <param name="newValue">新值</param>
        /// <returns>是否成功完成编辑</returns>
        bool CompleteDirectEdit(object newValue);
        
        /// <summary>
        /// 取消直接编辑
        /// </summary>
        void CancelDirectEdit();
        
        #endregion
        
        #region 状态管理
        
        /// <summary>
        /// 获取组件状态快照
        /// </summary>
        /// <returns>状态快照</returns>
        ComponentStateSnapshot GetStateSnapshot();
        
        /// <summary>
        /// 恢复组件状态
        /// </summary>
        /// <param name="snapshot">状态快照</param>
        void RestoreState(ComponentStateSnapshot snapshot);
        
        /// <summary>
        /// 是否可以撤销
        /// </summary>
        bool CanUndo { get; }
        
        /// <summary>
        /// 是否可以重做
        /// </summary>
        bool CanRedo { get; }
        
        /// <summary>
        /// 撤销操作
        /// </summary>
        /// <returns>是否成功撤销</returns>
        bool Undo();
        
        /// <summary>
        /// 重做操作
        /// </summary>
        /// <returns>是否成功重做</returns>
        bool Redo();
        
        #endregion
        
        #region 选择管理
        
        /// <summary>
        /// 选择指定对象
        /// </summary>
        /// <param name="target">选择目标</param>
        /// <param name="multiSelect">是否多选</param>
        void SelectObject(object target, bool multiSelect = false);
        
        /// <summary>
        /// 清除所有选择
        /// </summary>
        void ClearSelection();
        
        /// <summary>
        /// 获取当前选择的对象
        /// </summary>
        /// <returns>选择的对象列表</returns>
        object[] GetSelectedObjects();
        
        /// <summary>
        /// 是否有选择的对象
        /// </summary>
        bool HasSelection { get; }
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 组件状态改变事件
        /// </summary>
        event EventHandler<ComponentStateChangedEventArgs> StateChanged;
        
        /// <summary>
        /// 选择改变事件
        /// </summary>
        event EventHandler<SelectionChangedEventArgs> SelectionChanged;
        
        /// <summary>
        /// 拖拽状态改变事件
        /// </summary>
        event EventHandler<DragStateChangedEventArgs> DragStateChanged;
        
        /// <summary>
        /// 编辑状态改变事件
        /// </summary>
        event EventHandler<EditStateChangedEventArgs> EditStateChanged;
        
        /// <summary>
        /// 数据改变事件
        /// </summary>
        event EventHandler<DataChangedEventArgs> DataChanged;
        
        #endregion
    }
    
    #region 枚举定义
    
    /// <summary>
    /// 组件状态枚举
    /// </summary>
    public enum ComponentState
    {
        /// <summary>未初始化</summary>
        Uninitialized,
        /// <summary>正常状态</summary>
        Normal,
        /// <summary>激活状态</summary>
        Active,
        /// <summary>非激活状态</summary>
        Inactive,
        /// <summary>编辑状态</summary>
        Editing,
        /// <summary>拖拽状态</summary>
        Dragging,
        /// <summary>选择状态</summary>
        Selecting,
        /// <summary>错误状态</summary>
        Error,
        /// <summary>已销毁</summary>
        Disposed
    }
    
    /// <summary>
    /// 编辑类型枚举
    /// </summary>
    public enum EditType
    {
        /// <summary>文本编辑</summary>
        Text,
        /// <summary>日期编辑</summary>
        Date,
        /// <summary>数值编辑</summary>
        Number,
        /// <summary>颜色编辑</summary>
        Color,
        /// <summary>状态编辑</summary>
        Status
    }
    
    #endregion
    
    #region 事件参数类
    
    /// <summary>
    /// 鼠标事件参数
    /// </summary>
    public class MouseEventArgs : EventArgs
    {
        public DrawingPoint Location { get; set; }
        public MouseButtons Button { get; set; }
        public int Clicks { get; set; }
        public bool Handled { get; set; }
    }
    
    /// <summary>
    /// 键盘事件参数
    /// </summary>
    public class KeyboardEventArgs : EventArgs
    {
        public int KeyCode { get; set; }
        public bool Shift { get; set; }
        public bool Ctrl { get; set; }
        public bool Alt { get; set; }
        public bool Handled { get; set; }
    }
    
    /// <summary>
    /// 组件状态改变事件参数
    /// </summary>
    public class ComponentStateChangedEventArgs : EventArgs
    {
        public ComponentState OldState { get; set; }
        public ComponentState NewState { get; set; }
    }
    
    /// <summary>
    /// 选择改变事件参数
    /// </summary>
    public class SelectionChangedEventArgs : EventArgs
    {
        public object[] OldSelection { get; set; }
        public object[] NewSelection { get; set; }
    }
    
    /// <summary>
    /// 拖拽状态改变事件参数
    /// </summary>
    public class DragStateChangedEventArgs : EventArgs
    {
        public bool IsDragging { get; set; }
        public object DragObject { get; set; }
        public DrawingPoint CurrentLocation { get; set; }
    }
    
    /// <summary>
    /// 编辑状态改变事件参数
    /// </summary>
    public class EditStateChangedEventArgs : EventArgs
    {
        public bool IsEditing { get; set; }
        public object EditTarget { get; set; }
        public EditType EditType { get; set; }
    }
    
    /// <summary>
    /// 数据改变事件参数
    /// </summary>
    public class DataChangedEventArgs : EventArgs
    {
        public string PropertyName { get; set; }
        public object OldValue { get; set; }
        public object NewValue { get; set; }
    }
    
    /// <summary>
    /// 组件状态快照
    /// </summary>
    public class ComponentStateSnapshot
    {
        public string ComponentId { get; set; }
        public DateTime Timestamp { get; set; }
        public ComponentState State { get; set; }
        public string SerializedData { get; set; }
        public string Description { get; set; }
    }
    
    /// <summary>
    /// 鼠标按钮枚举
    /// </summary>
    public enum MouseButtons
    {
        None = 0,
        Left = 1,
        Right = 2,
        Middle = 4
    }
    
    #endregion
}
