using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Microsoft.Office.Interop.PowerPoint;
using PBIppt.GanttChart.Models;
using PBIppt.GanttChart.v2.Events;
using PBIppt.GanttChart.v2.State;
using PBIppt.Utils;
using DrawingPoint = System.Drawing.Point;
using PowerPointPoint = Microsoft.Office.Interop.PowerPoint.Point;

namespace PBIppt.GanttChart.v2.Components
{
    /// <summary>
    /// 甘特图组件 v2.0 主实现
    /// 统一的甘特图组件，提供完整的交互和渲染能力
    /// </summary>
    public class GanttChartComponent : IGanttChartComponent
    {
        #region 私有字段
        
        private readonly string _componentId;
        private StateManager _stateManager;
        private Slide _parentSlide;
        private GanttProject _project;
        private Rectangle _bounds;
        private bool _isEditMode;
        private bool _disposed = false;
        
        // 交互管理器
        private object _currentDragObject;
        private DrawingPoint _dragStartPoint;
        private bool _isDragging;
        private object _currentEditTarget;
        private EditType _currentEditType;
        private bool _isEditing;
        
        // 选择管理
        private object[] _selectedObjects = new object[0];
        
        #endregion
        
        #region 属性实现
        
        public string ComponentId => _componentId;
        
        public ComponentState State => _stateManager?.CurrentState ?? ComponentState.Uninitialized;
        
        public bool IsInitialized => State != ComponentState.Uninitialized && State != ComponentState.Disposed;
        
        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                if (_isEditMode != value)
                {
                    _isEditMode = value;
                    _stateManager?.UpdateState(value ? ComponentState.Editing : ComponentState.Normal);
                }
            }
        }
        
        public Slide ParentSlide => _parentSlide;
        
        public GanttProject Project
        {
            get => _project;
            set
            {
                if (_project != value)
                {
                    _project = value;
                    if (_stateManager != null)
                    {
                        _stateManager.CurrentProject = value;
                    }
                    OnDataChanged("Project", null, value);
                }
            }
        }
        
        public bool CanUndo => _stateManager?.CanUndo ?? false;
        
        public bool CanRedo => _stateManager?.CanRedo ?? false;
        
        public bool HasSelection => _selectedObjects?.Length > 0;
        
        #endregion
        
        #region 事件
        
        public event EventHandler<ComponentStateChangedEventArgs> StateChanged;
        public event EventHandler<SelectionChangedEventArgs> SelectionChanged;
        public event EventHandler<DragStateChangedEventArgs> DragStateChanged;
        public event EventHandler<EditStateChangedEventArgs> EditStateChanged;
        public event EventHandler<DataChangedEventArgs> DataChanged;
        
        #endregion
        
        #region 构造函数
        
        /// <summary>
        /// 初始化甘特图组件
        /// </summary>
        /// <param name="componentId">组件ID，如果为空则自动生成</param>
        public GanttChartComponent(string componentId = null)
        {
            _componentId = componentId ?? $"GanttChart_{Guid.NewGuid():N}";
            
            // 订阅事件总线
            SubscribeToEvents();
            
            Logger.Info($"甘特图组件已创建: {_componentId}");
        }
        
        #endregion
        
        #region 生命周期管理
        
        public void Initialize(Slide slide, GanttProject project, Rectangle bounds)
        {
            if (slide == null)
                throw new ArgumentNullException(nameof(slide));
            if (project == null)
                throw new ArgumentNullException(nameof(project));

            try
            {
                _parentSlide = slide;
                _project = project;
                _bounds = bounds;

                // 初始化状态管理器
                _stateManager = new StateManager(_componentId);
                _stateManager.StateChanged += OnStateManagerStateChanged;
                _stateManager.Initialize(project);

                // 发布初始化事件
                EventBusSystem.Instance.Publish(new ComponentInitializedEvent(
                    _componentId, project, "GanttChartComponent"));

                Logger.Info($"甘特图组件已初始化: {_componentId}");
            }
            catch (Exception ex)
            {
                Logger.Error($"甘特图组件初始化失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 初始化甘特图组件（使用默认边界）
        /// </summary>
        /// <param name="slide">PowerPoint幻灯片</param>
        /// <param name="project">甘特图项目</param>
        public void Initialize(Slide slide, GanttProject project)
        {
            // 使用默认边界：幻灯片中心区域
            var defaultBounds = new Rectangle(100, 100, 600, 400);
            Initialize(slide, project, defaultBounds);
        }
        
        public void Update()
        {
            if (!IsInitialized) return;

            try
            {
                // 更新组件状态和显示
                // 这里会调用渲染引擎进行更新
                Logger.Debug($"甘特图组件已更新: {_componentId}");
            }
            catch (Exception ex)
            {
                Logger.Error($"甘特图组件更新失败: {ex.Message}");
            }
        }

        public void Refresh()
        {
            if (!IsInitialized) return;

            try
            {
                // 强制刷新组件显示
                Update();
                Logger.Debug($"甘特图组件已刷新: {_componentId}");
            }
            catch (Exception ex)
            {
                Logger.Error($"甘特图组件刷新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 渲染甘特图到PowerPoint幻灯片
        /// </summary>
        public void Render()
        {
            if (!IsInitialized) return;

            try
            {
                Logger.Info($"开始渲染甘特图v2.0: {_componentId}");

                // 使用v2.0统一渲染引擎
                var unifiedRenderer = new Rendering.UnifiedGanttRenderer();
                var ganttShape = unifiedRenderer.CreateUnifiedGanttChart(
                    _parentSlide,
                    _project,
                    _bounds,
                    _componentId
                );

                if (ganttShape != null)
                {
                    // 启用交互事件处理
                    EnableInteractiveEvents(ganttShape);

                    Logger.Info($"甘特图v2.0渲染成功: {_componentId} (统一组件)");

                    // 发布渲染完成事件
                    EventBusSystem.Instance.Publish(new Events.RenderCompleteEvent(
                        "GanttChart", TimeSpan.Zero, true, _componentId));
                }
                else
                {
                    Logger.Warning($"甘特图v2.0渲染失败: {_componentId}");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"甘特图渲染失败: {ex.Message}");

                // 发布渲染失败事件
                EventBusSystem.Instance.Publish(new Events.RenderCompleteEvent(
                    "GanttChart", TimeSpan.Zero, false, _componentId));

                throw;
            }
        }

        /// <summary>
        /// 启用交互事件处理
        /// </summary>
        /// <param name="ganttShape">甘特图Shape对象</param>
        private void EnableInteractiveEvents(Shape ganttShape)
        {
            try
            {
                // 注册PowerPoint事件处理器
                // 注意：PowerPoint的事件处理需要特殊的COM事件绑定
                Logger.Debug($"为甘特图组件启用交互事件: {_componentId}");

                // 这里将在后续实现具体的事件绑定逻辑
                // 包括鼠标点击、拖拽、右键菜单等

                // 临时：设置Shape为可选择状态，便于后续交互
                ganttShape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;
            }
            catch (Exception ex)
            {
                Logger.Warning($"启用交互事件失败: {ex.Message}");
            }
        }

        public void Activate()
        {
            if (!IsInitialized) return;
            
            try
            {
                // 激活组件，获得焦点
                _stateManager?.UpdateState(ComponentState.Normal);
                Logger.Debug($"甘特图组件已激活: {_componentId}");
            }
            catch (Exception ex)
            {
                Logger.Error($"甘特图组件激活失败: {ex.Message}");
            }
        }
        
        public void Deactivate()
        {
            if (!IsInitialized) return;
            
            try
            {
                // 停用组件，失去焦点
                CancelDirectEdit();
                CancelDrag();
                ClearSelection();
                
                Logger.Debug($"甘特图组件已停用: {_componentId}");
            }
            catch (Exception ex)
            {
                Logger.Error($"甘特图组件停用失败: {ex.Message}");
            }
        }
        
        #endregion
        
        #region 交互事件处理
        
        public bool HandleMouseEvent(MouseEventArgs e)
        {
            if (!IsInitialized || e == null) return false;
            
            try
            {
                // 发布鼠标事件
                EventBusSystem.Instance.Publish(new MouseClickEvent(
                    e.Location, e.Button, e.Clicks, null, _componentId));
                
                // 处理不同类型的鼠标事件
                switch (e.Button)
                {
                    case MouseButtons.Left:
                        return HandleLeftClick(e);
                    case MouseButtons.Right:
                        return HandleRightClick(e);
                    default:
                        return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"处理鼠标事件失败: {ex.Message}");
                return false;
            }
        }
        
        public bool HandleKeyboardEvent(KeyboardEventArgs e)
        {
            if (!IsInitialized || e == null) return false;
            
            try
            {
                // 处理常用快捷键
                if (e.Ctrl)
                {
                    switch (e.KeyCode)
                    {
                        case 90: // Ctrl+Z
                            return Undo();
                        case 89: // Ctrl+Y
                            return Redo();
                        case 67: // Ctrl+C
                            return CopySelection();
                        case 86: // Ctrl+V
                            return PasteSelection();
                    }
                }
                else
                {
                    switch (e.KeyCode)
                    {
                        case 46: // Delete
                            return DeleteSelection();
                        case 113: // F2
                            return StartRenameSelection();
                        case 27: // Escape
                            CancelCurrentOperation();
                            return true;
                    }
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error($"处理键盘事件失败: {ex.Message}");
                return false;
            }
        }
        
        public void ShowContextMenu(DrawingPoint location, object context = null)
        {
            if (!IsInitialized) return;
            
            try
            {
                // 显示上下文菜单
                // 这里会调用上下文菜单管理器
                Logger.Debug($"显示上下文菜单: {location}");
            }
            catch (Exception ex)
            {
                Logger.Error($"显示上下文菜单失败: {ex.Message}");
            }
        }
        
        #endregion

        #region 状态管理

        public ComponentStateSnapshot GetStateSnapshot()
        {
            if (!IsInitialized) return null;

            try
            {
                return new ComponentStateSnapshot
                {
                    ComponentId = _componentId,
                    Timestamp = DateTime.Now,
                    State = State,
                    SerializedData = _stateManager?.GetUndoHistory().FirstOrDefault()?.ProjectData,
                    Description = "当前状态快照"
                };
            }
            catch (Exception ex)
            {
                Logger.Error($"获取状态快照失败: {ex.Message}");
                return null;
            }
        }

        public void RestoreState(ComponentStateSnapshot snapshot)
        {
            if (!IsInitialized || snapshot == null) return;

            try
            {
                // 恢复状态逻辑
                Logger.Debug($"恢复状态快照: {snapshot.Description}");
            }
            catch (Exception ex)
            {
                Logger.Error($"恢复状态快照失败: {ex.Message}");
            }
        }

        public bool Undo()
        {
            if (!CanUndo) return false;

            try
            {
                var result = _stateManager.Undo();
                if (result)
                {
                    Refresh();
                }
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"撤销操作失败: {ex.Message}");
                return false;
            }
        }

        public bool Redo()
        {
            if (!CanRedo) return false;

            try
            {
                var result = _stateManager.Redo();
                if (result)
                {
                    Refresh();
                }
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"重做操作失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 选择管理

        public void SelectObject(object target, bool multiSelect = false)
        {
            if (!IsInitialized || target == null) return;

            try
            {
                var oldSelection = _selectedObjects;

                if (multiSelect)
                {
                    // 多选模式
                    if (_selectedObjects.Contains(target))
                    {
                        // 取消选择
                        _selectedObjects = _selectedObjects.Where(o => o != target).ToArray();
                    }
                    else
                    {
                        // 添加选择
                        var newSelection = new object[_selectedObjects.Length + 1];
                        _selectedObjects.CopyTo(newSelection, 0);
                        newSelection[_selectedObjects.Length] = target;
                        _selectedObjects = newSelection;
                    }
                }
                else
                {
                    // 单选模式
                    _selectedObjects = new[] { target };
                }

                OnSelectionChanged(oldSelection, _selectedObjects);

                Logger.Debug($"选择对象: {target.GetType().Name}, 多选: {multiSelect}");
            }
            catch (Exception ex)
            {
                Logger.Error($"选择对象失败: {ex.Message}");
            }
        }

        public void ClearSelection()
        {
            if (!HasSelection) return;

            try
            {
                var oldSelection = _selectedObjects;
                _selectedObjects = new object[0];

                OnSelectionChanged(oldSelection, _selectedObjects);

                // 发布选择清除事件
                EventBusSystem.Instance.Publish(new SelectionClearedEvent(
                    oldSelection, _componentId));

                Logger.Debug("已清除选择");
            }
            catch (Exception ex)
            {
                Logger.Error($"清除选择失败: {ex.Message}");
            }
        }

        public object[] GetSelectedObjects()
        {
            return _selectedObjects?.ToArray() ?? new object[0];
        }

        #endregion

        #region 私有辅助方法

        private void SubscribeToEvents()
        {
            // 订阅相关事件
            EventBusSystem.Instance.Subscribe<ErrorEvent>(OnErrorEvent, $"{_componentId}_ErrorHandler");
        }

        private void UnsubscribeFromEvents()
        {
            // 取消订阅事件
            EventBusSystem.Instance.Unsubscribe($"{_componentId}_ErrorHandler");
        }

        private bool HandleLeftClick(MouseEventArgs e)
        {
            // 处理左键点击
            if (e.Clicks == 2)
            {
                // 双击编辑
                return HandleDoubleClick(e);
            }
            else
            {
                // 单击选择
                return HandleSingleClick(e);
            }
        }

        private bool HandleRightClick(MouseEventArgs e)
        {
            // 处理右键点击，显示上下文菜单
            ShowContextMenu(e.Location);
            return true;
        }

        private bool HandleDoubleClick(MouseEventArgs e)
        {
            // 处理双击编辑
            var target = GetObjectAtPoint(e.Location);
            if (target != null)
            {
                return StartDirectEdit(target, EditType.Text);
            }
            return false;
        }

        private bool HandleSingleClick(MouseEventArgs e)
        {
            // 处理单击选择
            var target = GetObjectAtPoint(e.Location);
            if (target != null)
            {
                bool ctrlPressed = (Control.ModifierKeys & Keys.Control) == Keys.Control;
                SelectObject(target, ctrlPressed);
                return true;
            }
            else
            {
                ClearSelection();
                return true;
            }
        }

        private object GetObjectAtPoint(DrawingPoint location)
        {
            // 根据位置获取对象
            // 这里需要实现具体的命中测试逻辑
            return null;
        }

        private bool ProcessDragCompletion(DrawingPoint endPoint)
        {
            // 处理拖拽完成逻辑
            if (_currentDragObject == null) return false;

            // 保存状态快照
            _stateManager?.SaveSnapshot("拖拽操作");

            return true;
        }

        private void CleanupDrag()
        {
            _isDragging = false;
            _currentDragObject = null;
            _dragStartPoint = DrawingPoint.Empty;

            _stateManager?.UpdateState(ComponentState.Normal);
            OnDragStateChanged(false, null, DrawingPoint.Empty);
        }

        private object GetCurrentValue(object target, EditType editType)
        {
            // 获取当前编辑值
            return null;
        }

        private bool ApplyEditValue(object target, EditType editType, object newValue)
        {
            // 应用编辑值
            return true;
        }

        private void CleanupEdit()
        {
            _isEditing = false;
            _currentEditTarget = null;
            _currentEditType = EditType.Text;

            _stateManager?.UpdateState(ComponentState.Normal);
            OnEditStateChanged(false, null, EditType.Text);
        }

        private bool CopySelection()
        {
            // 复制选择的对象
            if (!HasSelection) return false;

            Logger.Debug($"复制选择: {_selectedObjects.Length} 个对象");
            return true;
        }

        private bool PasteSelection()
        {
            // 粘贴对象
            Logger.Debug("粘贴对象");
            return true;
        }

        private bool DeleteSelection()
        {
            // 删除选择的对象
            if (!HasSelection) return false;

            _stateManager?.SaveSnapshot("删除对象");
            ClearSelection();

            Logger.Debug($"删除选择: {_selectedObjects.Length} 个对象");
            return true;
        }

        private bool StartRenameSelection()
        {
            // 重命名选择的对象
            if (!HasSelection) return false;

            var target = _selectedObjects.FirstOrDefault();
            return StartDirectEdit(target, EditType.Text);
        }

        private void CancelCurrentOperation()
        {
            // 取消当前操作
            if (_isDragging)
            {
                CancelDrag();
            }
            else if (_isEditing)
            {
                CancelDirectEdit();
            }
        }

        #endregion

        #region 事件处理

        private void OnStateManagerStateChanged(object sender, StateChangedEventArgs e)
        {
            StateChanged?.Invoke(this, new ComponentStateChangedEventArgs
            {
                OldState = e.OldState,
                NewState = e.NewState
            });
        }

        private void OnSelectionChanged(object[] oldSelection, object[] newSelection)
        {
            SelectionChanged?.Invoke(this, new SelectionChangedEventArgs
            {
                OldSelection = oldSelection,
                NewSelection = newSelection
            });

            // 发布选择改变事件
            EventBusSystem.Instance.Publish(new SelectionChangedEvent(
                oldSelection, newSelection, newSelection.Length > 1, _componentId));
        }

        private void OnDragStateChanged(bool isDragging, object dragObject, DrawingPoint location)
        {
            DragStateChanged?.Invoke(this, new DragStateChangedEventArgs
            {
                IsDragging = isDragging,
                DragObject = dragObject,
                CurrentLocation = location
            });
        }

        private void OnEditStateChanged(bool isEditing, object editTarget, EditType editType)
        {
            EditStateChanged?.Invoke(this, new EditStateChangedEventArgs
            {
                IsEditing = isEditing,
                EditTarget = editTarget,
                EditType = editType
            });
        }

        private void OnDataChanged(string propertyName, object oldValue, object newValue)
        {
            DataChanged?.Invoke(this, new DataChangedEventArgs
            {
                PropertyName = propertyName,
                OldValue = oldValue,
                NewValue = newValue
            });
        }

        private void OnErrorEvent(ErrorEvent errorEvent)
        {
            Logger.Error($"组件错误事件: {errorEvent.ErrorMessage}");
        }

        #endregion

        #region 扩展方法（用于兼容性）

        /// <summary>
        /// 获取组件状态（兼容性方法）
        /// </summary>
        /// <returns>组件状态</returns>
        public ComponentState GetState()
        {
            return State;
        }

        /// <summary>
        /// 启用PowerPoint集成（占位符方法）
        /// </summary>
        public void EnablePowerPointIntegration()
        {
            Logger.Debug($"启用PowerPoint集成: {_componentId}");
        }

        /// <summary>
        /// 设置智能布局（暂时禁用）
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public void SetSmartLayout(bool enabled)
        {
            // 暂时禁用SmartLayout功能，专注核心渲染
            Logger.Debug($"SmartLayout功能暂时禁用: {enabled}");
        }

        /// <summary>
        /// 组件选择事件处理（占位符方法）
        /// </summary>
        public void OnComponentSelected()
        {
            Logger.Debug($"组件已选择: {_componentId}");
        }

        /// <summary>
        /// 幻灯片激活事件处理（占位符方法）
        /// </summary>
        public void OnSlideActivated()
        {
            Logger.Debug($"幻灯片已激活: {_componentId}");
        }

        /// <summary>
        /// 窗口激活事件处理（占位符方法）
        /// </summary>
        public void OnWindowActivated()
        {
            Logger.Debug($"窗口已激活: {_componentId}");
        }

        #endregion

        #region IDisposable实现

        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    // 清理资源
                    Deactivate();
                    UnsubscribeFromEvents();

                    _stateManager?.Dispose();
                    _stateManager = null;

                    // 发布组件销毁事件
                    EventBusSystem.Instance.Publish(new ComponentDisposedEvent(
                        _componentId, "GanttChartComponent"));

                    _disposed = true;

                    Logger.Info($"甘特图组件已销毁: {_componentId}");
                }
                catch (Exception ex)
                {
                    Logger.Error($"甘特图组件销毁失败: {ex.Message}");
                }
            }
        }

        #endregion

        #region 拖拽操作
        
        public bool StartDrag(DrawingPoint startPoint, object dragObject)
        {
            if (!IsInitialized || _isDragging) return false;
            
            try
            {
                _dragStartPoint = startPoint;
                _currentDragObject = dragObject;
                _isDragging = true;
                
                _stateManager?.UpdateState(ComponentState.Dragging);
                
                // 发布拖拽开始事件
                EventBusSystem.Instance.Publish(new DragStartEvent(
                    startPoint, dragObject, dragObject?.GetType().Name, _componentId));
                
                OnDragStateChanged(true, dragObject, startPoint);
                
                Logger.Debug($"开始拖拽: {dragObject?.GetType().Name}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"开始拖拽失败: {ex.Message}");
                return false;
            }
        }
        
        public void UpdateDrag(DrawingPoint currentPoint)
        {
            if (!_isDragging) return;
            
            try
            {
                // 计算拖拽偏移
                var deltaX = currentPoint.X - _dragStartPoint.X;
                var deltaY = currentPoint.Y - _dragStartPoint.Y;
                
                // 发布拖拽更新事件
                EventBusSystem.Instance.Publish(new DragUpdateEvent(
                    currentPoint, _dragStartPoint, _currentDragObject, 
                    TimeSpan.Zero, _componentId)); // TimeSpan计算需要布局信息
                
                OnDragStateChanged(true, _currentDragObject, currentPoint);
                
                Logger.Debug($"更新拖拽: ({deltaX}, {deltaY})");
            }
            catch (Exception ex)
            {
                Logger.Error($"更新拖拽失败: {ex.Message}");
            }
        }
        
        public bool CompleteDrag(DrawingPoint endPoint)
        {
            if (!_isDragging) return false;
            
            try
            {
                var success = ProcessDragCompletion(endPoint);
                
                // 发布拖拽完成事件
                EventBusSystem.Instance.Publish(new DragCompleteEvent(
                    endPoint, _dragStartPoint, _currentDragObject, success, 
                    success ? "拖拽成功" : "拖拽失败", _componentId));
                
                CleanupDrag();
                
                Logger.Debug($"完成拖拽: {(success ? "成功" : "失败")}");
                return success;
            }
            catch (Exception ex)
            {
                Logger.Error($"完成拖拽失败: {ex.Message}");
                CleanupDrag();
                return false;
            }
        }
        
        public void CancelDrag()
        {
            if (!_isDragging) return;
            
            try
            {
                // 发布拖拽取消事件
                EventBusSystem.Instance.Publish(new DragCancelEvent(
                    _currentDragObject, "用户取消", _componentId));
                
                CleanupDrag();
                
                Logger.Debug("拖拽已取消");
            }
            catch (Exception ex)
            {
                Logger.Error($"取消拖拽失败: {ex.Message}");
            }
        }
        
        #endregion
        
        #region 直接编辑
        
        public bool StartDirectEdit(object editTarget, EditType editType)
        {
            if (!IsInitialized || _isEditing) return false;
            
            try
            {
                _currentEditTarget = editTarget;
                _currentEditType = editType;
                _isEditing = true;
                
                _stateManager?.UpdateState(ComponentState.Editing);
                
                // 发布编辑开始事件
                EventBusSystem.Instance.Publish(new EditStartEvent(
                    editTarget, editType, GetCurrentValue(editTarget, editType), _componentId));
                
                OnEditStateChanged(true, editTarget, editType);
                
                Logger.Debug($"开始直接编辑: {editType}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"开始直接编辑失败: {ex.Message}");
                return false;
            }
        }
        
        public bool CompleteDirectEdit(object newValue)
        {
            if (!_isEditing) return false;
            
            try
            {
                var oldValue = GetCurrentValue(_currentEditTarget, _currentEditType);
                var success = ApplyEditValue(_currentEditTarget, _currentEditType, newValue);
                
                if (success)
                {
                    _stateManager?.SaveSnapshot($"编辑{_currentEditType}");
                }
                
                // 发布编辑完成事件
                EventBusSystem.Instance.Publish(new EditCompleteEvent(
                    _currentEditTarget, _currentEditType, oldValue, newValue, success, _componentId));
                
                CleanupEdit();
                
                Logger.Debug($"完成直接编辑: {(success ? "成功" : "失败")}");
                return success;
            }
            catch (Exception ex)
            {
                Logger.Error($"完成直接编辑失败: {ex.Message}");
                CleanupEdit();
                return false;
            }
        }
        
        public void CancelDirectEdit()
        {
            if (!_isEditing) return;
            
            try
            {
                // 发布编辑取消事件
                EventBusSystem.Instance.Publish(new EditCancelEvent(
                    _currentEditTarget, _currentEditType, "用户取消", _componentId));
                
                CleanupEdit();
                
                Logger.Debug("直接编辑已取消");
            }
            catch (Exception ex)
            {
                Logger.Error($"取消直接编辑失败: {ex.Message}");
            }
        }

        #endregion
    }
}
