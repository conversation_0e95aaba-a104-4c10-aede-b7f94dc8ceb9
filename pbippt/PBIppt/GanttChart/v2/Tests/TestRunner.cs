using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace PBIppt.GanttChart.v2.Tests
{
    /// <summary>
    /// 甘特图v2.0测试运行器
    /// 提供便捷的测试执行和报告生成功能
    /// </summary>
    public class TestRunner
    {
        private readonly GanttV2TestFramework _testFramework;
        private readonly TestConfig _config;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="config">测试配置</param>
        public TestRunner(TestConfig config = null)
        {
            _config = config ?? TestConfig.CreateDefault();
            _testFramework = new GanttV2TestFramework(_config);
        }

        /// <summary>
        /// 运行快速测试
        /// </summary>
        /// <returns>测试报告</returns>
        public async Task<TestReport> RunQuickTestAsync()
        {
            Console.WriteLine("🚀 开始快速测试...");
            
            var quickConfig = TestConfig.CreateQuick();
            var framework = new GanttV2TestFramework(quickConfig);
            
            return await Task.Run(() => framework.RunFullTestSuite());
        }

        /// <summary>
        /// 运行完整测试
        /// </summary>
        /// <returns>测试报告</returns>
        public async Task<TestReport> RunFullTestAsync()
        {
            Console.WriteLine("🔬 开始完整测试...");
            
            return await Task.Run(() => _testFramework.RunFullTestSuite());
        }

        /// <summary>
        /// 运行性能基准测试
        /// </summary>
        /// <returns>测试报告</returns>
        public async Task<TestReport> RunPerformanceBenchmarkAsync()
        {
            Console.WriteLine("⚡ 开始性能基准测试...");
            
            var perfConfig = new TestConfig
            {
                EnableComponentTests = false,
                EnableLayoutTests = false,
                EnableInteractionTests = false,
                EnablePerformanceTests = true,
                EnableIntegrationTests = false,
                TestDataSize = TestDataSize.Large,
                VerboseOutput = true
            };
            
            var framework = new GanttV2TestFramework(perfConfig);
            return await Task.Run(() => framework.RunFullTestSuite());
        }

        /// <summary>
        /// 运行回归测试
        /// </summary>
        /// <returns>测试报告</returns>
        public async Task<TestReport> RunRegressionTestAsync()
        {
            Console.WriteLine("🔄 开始回归测试...");
            
            var regressionConfig = TestConfig.CreateComprehensive();
            var framework = new GanttV2TestFramework(regressionConfig);
            
            return await Task.Run(() => framework.RunFullTestSuite());
        }

        /// <summary>
        /// 保存测试报告
        /// </summary>
        /// <param name="report">测试报告</param>
        /// <param name="filePath">文件路径</param>
        public void SaveReport(TestReport report, string filePath = null)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    filePath = $"GanttV2_TestReport_{timestamp}.txt";
                }

                var reportContent = report.GenerateDetailedReport();
                File.WriteAllText(filePath, reportContent);
                
                Console.WriteLine($"📄 测试报告已保存到: {filePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 保存测试报告失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示测试报告摘要
        /// </summary>
        /// <param name="report">测试报告</param>
        public void DisplaySummary(TestReport report)
        {
            Console.WriteLine("\n" + report.GenerateSummary());
        }

        /// <summary>
        /// 显示详细测试报告
        /// </summary>
        /// <param name="report">测试报告</param>
        public void DisplayDetailedReport(TestReport report)
        {
            Console.WriteLine("\n" + report.GenerateDetailedReport());
        }

        /// <summary>
        /// 运行交互式测试
        /// </summary>
        public async Task RunInteractiveTestAsync()
        {
            Console.WriteLine("🎮 甘特图v2.0交互式测试");
            Console.WriteLine("========================");
            
            while (true)
            {
                Console.WriteLine("\n请选择测试类型:");
                Console.WriteLine("1. 快速测试 (约30秒)");
                Console.WriteLine("2. 完整测试 (约2分钟)");
                Console.WriteLine("3. 性能基准测试 (约1分钟)");
                Console.WriteLine("4. 回归测试 (约5分钟)");
                Console.WriteLine("5. 自定义测试");
                Console.WriteLine("0. 退出");
                Console.Write("\n请输入选择 (0-5): ");

                var choice = Console.ReadLine();
                TestReport report = null;

                switch (choice)
                {
                    case "1":
                        report = await RunQuickTestAsync();
                        break;
                    case "2":
                        report = await RunFullTestAsync();
                        break;
                    case "3":
                        report = await RunPerformanceBenchmarkAsync();
                        break;
                    case "4":
                        report = await RunRegressionTestAsync();
                        break;
                    case "5":
                        report = await RunCustomTestAsync();
                        break;
                    case "0":
                        Console.WriteLine("👋 测试结束，再见！");
                        return;
                    default:
                        Console.WriteLine("❌ 无效选择，请重新输入");
                        continue;
                }

                if (report != null)
                {
                    DisplaySummary(report);
                    
                    Console.Write("\n是否保存详细报告? (y/n): ");
                    if (Console.ReadLine()?.ToLower() == "y")
                    {
                        SaveReport(report);
                    }
                    
                    Console.Write("是否显示详细结果? (y/n): ");
                    if (Console.ReadLine()?.ToLower() == "y")
                    {
                        DisplayDetailedReport(report);
                    }
                }
            }
        }

        /// <summary>
        /// 运行自定义测试
        /// </summary>
        private async Task<TestReport> RunCustomTestAsync()
        {
            Console.WriteLine("\n🔧 自定义测试配置");
            
            var config = new TestConfig();
            
            Console.Write("启用组件测试? (y/n): ");
            config.EnableComponentTests = Console.ReadLine()?.ToLower() == "y";
            
            Console.Write("启用布局测试? (y/n): ");
            config.EnableLayoutTests = Console.ReadLine()?.ToLower() == "y";
            
            Console.Write("启用交互测试? (y/n): ");
            config.EnableInteractionTests = Console.ReadLine()?.ToLower() == "y";
            
            Console.Write("启用性能测试? (y/n): ");
            config.EnablePerformanceTests = Console.ReadLine()?.ToLower() == "y";
            
            Console.Write("启用集成测试? (y/n): ");
            config.EnableIntegrationTests = Console.ReadLine()?.ToLower() == "y";
            
            Console.WriteLine("选择测试数据大小:");
            Console.WriteLine("1. 小 (10个任务)");
            Console.WriteLine("2. 中 (50个任务)");
            Console.WriteLine("3. 大 (200个任务)");
            Console.WriteLine("4. 超大 (1000个任务)");
            Console.Write("请选择 (1-4): ");
            
            var sizeChoice = Console.ReadLine();
            config.TestDataSize = sizeChoice switch
            {
                "1" => TestDataSize.Small,
                "2" => TestDataSize.Medium,
                "3" => TestDataSize.Large,
                "4" => TestDataSize.XLarge,
                _ => TestDataSize.Medium
            };
            
            Console.Write("启用详细输出? (y/n): ");
            config.VerboseOutput = Console.ReadLine()?.ToLower() == "y";
            
            Console.WriteLine("\n🚀 开始自定义测试...");
            
            var framework = new GanttV2TestFramework(config);
            return await Task.Run(() => framework.RunFullTestSuite());
        }

        /// <summary>
        /// 验证测试环境
        /// </summary>
        /// <returns>是否验证通过</returns>
        public bool ValidateTestEnvironment()
        {
            Console.WriteLine("🔍 验证测试环境...");
            
            try
            {
                // 检查必要的依赖
                var eventBus = Events.EventBusSystem.Instance;
                if (eventBus == null)
                {
                    Console.WriteLine("❌ 事件总线系统未初始化");
                    return false;
                }

                // 检查组件系统
                var component = new Components.GanttChartComponent();
                if (component == null)
                {
                    Console.WriteLine("❌ 组件系统不可用");
                    return false;
                }

                // 检查布局系统
                var layoutAlgorithm = new Rendering.SmartLayout.SmartLayoutAlgorithm();
                if (layoutAlgorithm == null)
                {
                    Console.WriteLine("❌ 布局系统不可用");
                    return false;
                }

                Console.WriteLine("✅ 测试环境验证通过");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试环境验证失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 生成测试报告HTML版本
        /// </summary>
        /// <param name="report">测试报告</param>
        /// <param name="filePath">HTML文件路径</param>
        public void GenerateHtmlReport(TestReport report, string filePath = null)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    filePath = $"GanttV2_TestReport_{timestamp}.html";
                }

                var stats = report.GetStatistics();
                var html = $@"
<!DOCTYPE html>
<html>
<head>
    <title>甘特图v2.0测试报告</title>
    <meta charset='utf-8'>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .stats {{ display: flex; gap: 20px; margin: 20px 0; }}
        .stat-card {{ background: #fff; border: 1px solid #ddd; padding: 15px; border-radius: 5px; flex: 1; }}
        .passed {{ color: #28a745; }}
        .failed {{ color: #dc3545; }}
        .warning {{ color: #ffc107; }}
        .test-section {{ margin: 20px 0; }}
        .test-result {{ padding: 5px; margin: 2px 0; border-radius: 3px; }}
        .test-passed {{ background: #d4edda; }}
        .test-failed {{ background: #f8d7da; }}
        .test-warning {{ background: #fff3cd; }}
    </style>
</head>
<body>
    <div class='header'>
        <h1>甘特图v2.0测试报告</h1>
        <p>测试时间: {report.StartTime:yyyy-MM-dd HH:mm:ss} - {report.EndTime:yyyy-MM-dd HH:mm:ss}</p>
        <p>总耗时: {report.TotalDuration.TotalSeconds:F2}秒</p>
        <p>成功率: {stats.SuccessRate:F1}%</p>
    </div>
    
    <div class='stats'>
        <div class='stat-card'>
            <h3>总测试数</h3>
            <h2>{stats.TotalTests}</h2>
        </div>
        <div class='stat-card passed'>
            <h3>通过</h3>
            <h2>{stats.PassedTests}</h2>
        </div>
        <div class='stat-card failed'>
            <h3>失败</h3>
            <h2>{stats.FailedTests}</h2>
        </div>
        <div class='stat-card warning'>
            <h3>警告</h3>
            <h2>{stats.WarningTests}</h2>
        </div>
    </div>
    
    {GenerateTestSectionHtml("组件测试", report.ComponentTestResults)}
    {GenerateTestSectionHtml("布局测试", report.LayoutTestResults)}
    {GenerateTestSectionHtml("交互测试", report.InteractionTestResults)}
    {GenerateTestSectionHtml("性能测试", report.PerformanceTestResults)}
    {GenerateTestSectionHtml("集成测试", report.IntegrationTestResults)}
    
</body>
</html>";

                File.WriteAllText(filePath, html);
                Console.WriteLine($"📄 HTML测试报告已保存到: {filePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 生成HTML报告失败: {ex.Message}");
            }
        }

        private string GenerateTestSectionHtml(string sectionName, List<TestResult> results)
        {
            if (!results.Any()) return "";

            var html = $"<div class='test-section'><h2>{sectionName}</h2>";
            
            foreach (var result in results)
            {
                var cssClass = result.Result switch
                {
                    TestResult.ResultType.Passed => "test-passed",
                    TestResult.ResultType.Failed => "test-failed",
                    TestResult.ResultType.Warning => "test-warning",
                    _ => ""
                };

                var icon = result.Result switch
                {
                    TestResult.ResultType.Passed => "✅",
                    TestResult.ResultType.Failed => "❌",
                    TestResult.ResultType.Warning => "⚠️",
                    _ => "❓"
                };

                html += $"<div class='test-result {cssClass}'>{icon} {result.TestName} ({result.Duration.TotalMilliseconds:F0}ms) - {result.Message}</div>";
            }
            
            html += "</div>";
            return html;
        }
    }
}
