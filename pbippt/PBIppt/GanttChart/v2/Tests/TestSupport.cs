using System;
using System.Collections.Generic;
using System.Linq;

namespace PBIppt.GanttChart.v2.Tests
{
    /// <summary>
    /// 测试配置
    /// </summary>
    public class TestConfig
    {
        /// <summary>
        /// 启用组件测试
        /// </summary>
        public bool EnableComponentTests { get; set; } = true;

        /// <summary>
        /// 启用布局测试
        /// </summary>
        public bool EnableLayoutTests { get; set; } = true;

        /// <summary>
        /// 启用交互测试
        /// </summary>
        public bool EnableInteractionTests { get; set; } = true;

        /// <summary>
        /// 启用性能测试
        /// </summary>
        public bool EnablePerformanceTests { get; set; } = true;

        /// <summary>
        /// 启用集成测试
        /// </summary>
        public bool EnableIntegrationTests { get; set; } = true;

        /// <summary>
        /// 性能测试阈值（毫秒）
        /// </summary>
        public int PerformanceThreshold { get; set; } = 1000;

        /// <summary>
        /// 详细输出模式
        /// </summary>
        public bool VerboseOutput { get; set; } = false;

        /// <summary>
        /// 测试数据大小
        /// </summary>
        public TestDataSize TestDataSize { get; set; } = TestDataSize.Medium;

        /// <summary>
        /// 创建默认配置
        /// </summary>
        public static TestConfig CreateDefault() => new TestConfig();

        /// <summary>
        /// 创建快速测试配置
        /// </summary>
        public static TestConfig CreateQuick()
        {
            return new TestConfig
            {
                EnablePerformanceTests = false,
                EnableIntegrationTests = false,
                TestDataSize = TestDataSize.Small,
                PerformanceThreshold = 500
            };
        }

        /// <summary>
        /// 创建完整测试配置
        /// </summary>
        public static TestConfig CreateComprehensive()
        {
            return new TestConfig
            {
                EnableComponentTests = true,
                EnableLayoutTests = true,
                EnableInteractionTests = true,
                EnablePerformanceTests = true,
                EnableIntegrationTests = true,
                VerboseOutput = true,
                TestDataSize = TestDataSize.Large,
                PerformanceThreshold = 2000
            };
        }
    }

    /// <summary>
    /// 测试结果
    /// </summary>
    public class TestResult
    {
        /// <summary>
        /// 测试名称
        /// </summary>
        public string TestName { get; set; }

        /// <summary>
        /// 测试结果
        /// </summary>
        public ResultType Result { get; set; }

        /// <summary>
        /// 执行时间
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 错误详情
        /// </summary>
        public string ErrorDetails { get; set; }

        /// <summary>
        /// 测试时间
        /// </summary>
        public DateTime TestTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否通过
        /// </summary>
        public bool IsPassed => Result == ResultType.Passed;

        /// <summary>
        /// 是否失败
        /// </summary>
        public bool IsFailed => Result == ResultType.Failed;

        /// <summary>
        /// 获取结果摘要
        /// </summary>
        public string GetSummary()
        {
            var status = Result switch
            {
                ResultType.Passed => "✅",
                ResultType.Failed => "❌",
                ResultType.Warning => "⚠️",
                _ => "❓"
            };

            return $"{status} {TestName} ({Duration.TotalMilliseconds:F0}ms) - {Message}";
        }

        /// <summary>
        /// 结果类型
        /// </summary>
        public enum ResultType
        {
            Passed,
            Failed,
            Warning,
            Skipped
        }

        // 静态常量
        public static readonly ResultType Passed = ResultType.Passed;
        public static readonly ResultType Failed = ResultType.Failed;
        public static readonly ResultType Warning = ResultType.Warning;
        public static readonly ResultType Skipped = ResultType.Skipped;
    }

    /// <summary>
    /// 测试报告
    /// </summary>
    public class TestReport
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 总耗时
        /// </summary>
        public TimeSpan TotalDuration { get; set; }

        /// <summary>
        /// 测试框架版本
        /// </summary>
        public string TestFrameworkVersion { get; set; }

        /// <summary>
        /// 测试配置
        /// </summary>
        public TestConfig Configuration { get; set; }

        /// <summary>
        /// 组件测试结果
        /// </summary>
        public List<TestResult> ComponentTestResults { get; set; } = new List<TestResult>();

        /// <summary>
        /// 布局测试结果
        /// </summary>
        public List<TestResult> LayoutTestResults { get; set; } = new List<TestResult>();

        /// <summary>
        /// 交互测试结果
        /// </summary>
        public List<TestResult> InteractionTestResults { get; set; } = new List<TestResult>();

        /// <summary>
        /// 性能测试结果
        /// </summary>
        public List<TestResult> PerformanceTestResults { get; set; } = new List<TestResult>();

        /// <summary>
        /// 集成测试结果
        /// </summary>
        public List<TestResult> IntegrationTestResults { get; set; } = new List<TestResult>();

        /// <summary>
        /// 总体结果
        /// </summary>
        public TestResult.ResultType OverallResult { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 获取所有测试结果
        /// </summary>
        public List<TestResult> GetAllResults()
        {
            var allResults = new List<TestResult>();
            allResults.AddRange(ComponentTestResults);
            allResults.AddRange(LayoutTestResults);
            allResults.AddRange(InteractionTestResults);
            allResults.AddRange(PerformanceTestResults);
            allResults.AddRange(IntegrationTestResults);
            return allResults;
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        public TestStatistics GetStatistics()
        {
            var allResults = GetAllResults();
            
            return new TestStatistics
            {
                TotalTests = allResults.Count,
                PassedTests = allResults.Count(r => r.Result == TestResult.Passed),
                FailedTests = allResults.Count(r => r.Result == TestResult.Failed),
                WarningTests = allResults.Count(r => r.Result == TestResult.Warning),
                SkippedTests = allResults.Count(r => r.Result == TestResult.Skipped),
                AverageExecutionTime = allResults.Any() ? 
                    TimeSpan.FromMilliseconds(allResults.Average(r => r.Duration.TotalMilliseconds)) : 
                    TimeSpan.Zero,
                TotalExecutionTime = TimeSpan.FromMilliseconds(allResults.Sum(r => r.Duration.TotalMilliseconds))
            };
        }

        /// <summary>
        /// 生成报告摘要
        /// </summary>
        public string GenerateSummary()
        {
            var stats = GetStatistics();
            var successRate = stats.TotalTests > 0 ? (double)stats.PassedTests / stats.TotalTests * 100 : 0;

            return $@"📊 甘特图v2.0测试报告摘要
================================
🕐 测试时间: {StartTime:yyyy-MM-dd HH:mm:ss} - {EndTime:yyyy-MM-dd HH:mm:ss}
⏱️ 总耗时: {TotalDuration.TotalSeconds:F2}秒
📈 成功率: {successRate:F1}%

📋 测试统计:
• 总测试数: {stats.TotalTests}
• 通过: {stats.PassedTests} ✅
• 失败: {stats.FailedTests} ❌
• 警告: {stats.WarningTests} ⚠️
• 跳过: {stats.SkippedTests} ⏭️

⚡ 性能统计:
• 平均执行时间: {stats.AverageExecutionTime.TotalMilliseconds:F0}ms
• 总执行时间: {stats.TotalExecutionTime.TotalSeconds:F2}秒

🎯 总体结果: {GetOverallResultText()}";
        }

        /// <summary>
        /// 生成详细报告
        /// </summary>
        public string GenerateDetailedReport()
        {
            var summary = GenerateSummary();
            var details = "";

            if (ComponentTestResults.Any())
            {
                details += "\n📦 组件测试详情:\n";
                details += string.Join("\n", ComponentTestResults.Select(r => $"  {r.GetSummary()}"));
            }

            if (LayoutTestResults.Any())
            {
                details += "\n\n📐 布局测试详情:\n";
                details += string.Join("\n", LayoutTestResults.Select(r => $"  {r.GetSummary()}"));
            }

            if (InteractionTestResults.Any())
            {
                details += "\n\n🖱️ 交互测试详情:\n";
                details += string.Join("\n", InteractionTestResults.Select(r => $"  {r.GetSummary()}"));
            }

            if (PerformanceTestResults.Any())
            {
                details += "\n\n⚡ 性能测试详情:\n";
                details += string.Join("\n", PerformanceTestResults.Select(r => $"  {r.GetSummary()}"));
            }

            if (IntegrationTestResults.Any())
            {
                details += "\n\n🔗 集成测试详情:\n";
                details += string.Join("\n", IntegrationTestResults.Select(r => $"  {r.GetSummary()}"));
            }

            if (!string.IsNullOrEmpty(ErrorMessage))
            {
                details += $"\n\n❌ 错误信息:\n{ErrorMessage}";
            }

            return summary + details;
        }

        private string GetOverallResultText()
        {
            return OverallResult switch
            {
                TestResult.ResultType.Passed => "全部通过 ✅",
                TestResult.ResultType.Failed => "存在失败 ❌",
                TestResult.ResultType.Warning => "存在警告 ⚠️",
                _ => "未知状态 ❓"
            };
        }
    }

    /// <summary>
    /// 测试统计信息
    /// </summary>
    public class TestStatistics
    {
        /// <summary>
        /// 总测试数
        /// </summary>
        public int TotalTests { get; set; }

        /// <summary>
        /// 通过测试数
        /// </summary>
        public int PassedTests { get; set; }

        /// <summary>
        /// 失败测试数
        /// </summary>
        public int FailedTests { get; set; }

        /// <summary>
        /// 警告测试数
        /// </summary>
        public int WarningTests { get; set; }

        /// <summary>
        /// 跳过测试数
        /// </summary>
        public int SkippedTests { get; set; }

        /// <summary>
        /// 平均执行时间
        /// </summary>
        public TimeSpan AverageExecutionTime { get; set; }

        /// <summary>
        /// 总执行时间
        /// </summary>
        public TimeSpan TotalExecutionTime { get; set; }

        /// <summary>
        /// 成功率
        /// </summary>
        public double SuccessRate => TotalTests > 0 ? (double)PassedTests / TotalTests * 100 : 0;

        /// <summary>
        /// 是否全部通过
        /// </summary>
        public bool AllPassed => TotalTests > 0 && PassedTests == TotalTests;

        /// <summary>
        /// 是否有失败
        /// </summary>
        public bool HasFailures => FailedTests > 0;
    }

    /// <summary>
    /// 测试数据大小
    /// </summary>
    public enum TestDataSize
    {
        Small,   // 小数据集：10个任务
        Medium,  // 中数据集：50个任务
        Large,   // 大数据集：200个任务
        XLarge   // 超大数据集：1000个任务
    }

    /// <summary>
    /// 性能基准
    /// </summary>
    public static class PerformanceBenchmarks
    {
        /// <summary>
        /// 组件创建基准（毫秒）
        /// </summary>
        public static readonly int ComponentCreation = 100;

        /// <summary>
        /// 布局计算基准（毫秒）
        /// </summary>
        public static readonly int LayoutCalculation = 500;

        /// <summary>
        /// 渲染基准（毫秒）
        /// </summary>
        public static readonly int Rendering = 1000;

        /// <summary>
        /// 交互响应基准（毫秒）
        /// </summary>
        public static readonly int InteractionResponse = 50;

        /// <summary>
        /// 内存使用基准（MB）
        /// </summary>
        public static readonly int MemoryUsage = 50;
    }
}
