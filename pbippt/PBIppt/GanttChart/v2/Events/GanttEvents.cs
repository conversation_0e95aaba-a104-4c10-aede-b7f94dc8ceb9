using System;
using System.Drawing;
using PBIppt.GanttChart.Models;
using PBIppt.GanttChart.v2.Components;

namespace PBIppt.GanttChart.v2.Events
{
    #region 基础事件类
    
    /// <summary>
    /// 甘特图事件基类
    /// </summary>
    public abstract class GanttEventBase : IEvent
    {
        public string EventId { get; }
        public DateTime Timestamp { get; }
        public string Source { get; }
        
        protected GanttEventBase(string source = null)
        {
            EventId = Guid.NewGuid().ToString("N");
            Timestamp = DateTime.Now;
            Source = source ?? "GanttChart";
        }
    }
    
    #endregion
    
    #region 组件生命周期事件
    
    /// <summary>
    /// 组件初始化事件
    /// </summary>
    public class ComponentInitializedEvent : GanttEventBase
    {
        public string ComponentId { get; }
        public GanttProject Project { get; }
        
        public ComponentInitializedEvent(string componentId, GanttProject project, string source = null) 
            : base(source)
        {
            ComponentId = componentId;
            Project = project;
        }
    }
    
    /// <summary>
    /// 组件状态改变事件
    /// </summary>
    public class ComponentStateChangedEvent : GanttEventBase
    {
        public string ComponentId { get; }
        public ComponentState OldState { get; }
        public ComponentState NewState { get; }
        
        public ComponentStateChangedEvent(string componentId, ComponentState oldState, 
            ComponentState newState, string source = null) : base(source)
        {
            ComponentId = componentId;
            OldState = oldState;
            NewState = newState;
        }
    }
    
    /// <summary>
    /// 组件销毁事件
    /// </summary>
    public class ComponentDisposedEvent : GanttEventBase
    {
        public string ComponentId { get; }
        
        public ComponentDisposedEvent(string componentId, string source = null) : base(source)
        {
            ComponentId = componentId;
        }
    }
    
    #endregion
    
    #region 交互事件
    
    /// <summary>
    /// 鼠标点击事件
    /// </summary>
    public class MouseClickEvent : GanttEventBase
    {
        public Point Location { get; }
        public MouseButtons Button { get; }
        public int ClickCount { get; }
        public object Target { get; }
        
        public MouseClickEvent(Point location, MouseButtons button, int clickCount, 
            object target = null, string source = null) : base(source)
        {
            Location = location;
            Button = button;
            ClickCount = clickCount;
            Target = target;
        }
    }
    
    /// <summary>
    /// 拖拽开始事件
    /// </summary>
    public class DragStartEvent : GanttEventBase
    {
        public Point StartLocation { get; }
        public Point StartPoint => StartLocation;
        public object DragObject { get; }
        public object Target => DragObject;
        public string DragType { get; }

        public DragStartEvent(Point startLocation, object dragObject, string dragType,
            string source = null) : base(source)
        {
            StartLocation = startLocation;
            DragObject = dragObject;
            DragType = dragType;
        }
    }
    
    /// <summary>
    /// 拖拽更新事件
    /// </summary>
    public class DragUpdateEvent : GanttEventBase
    {
        public Point CurrentLocation { get; }
        public Point StartLocation { get; }
        public object DragObject { get; }
        public TimeSpan TimeOffset { get; }
        
        public DragUpdateEvent(Point currentLocation, Point startLocation, 
            object dragObject, TimeSpan timeOffset, string source = null) : base(source)
        {
            CurrentLocation = currentLocation;
            StartLocation = startLocation;
            DragObject = dragObject;
            TimeOffset = timeOffset;
        }
    }
    
    /// <summary>
    /// 拖拽完成事件
    /// </summary>
    public class DragCompleteEvent : GanttEventBase
    {
        public Point EndLocation { get; }
        public Point StartLocation { get; }
        public object DragObject { get; }
        public object Target => DragObject;
        public bool Success { get; }
        public string Result { get; }

        public DragCompleteEvent(Point endLocation, Point startLocation, object dragObject,
            bool success, string result = null, string source = null) : base(source)
        {
            EndLocation = endLocation;
            StartLocation = startLocation;
            DragObject = dragObject;
            Success = success;
            Result = result;
        }
    }
    
    /// <summary>
    /// 拖拽取消事件
    /// </summary>
    public class DragCancelEvent : GanttEventBase
    {
        public object DragObject { get; }
        public string Reason { get; }
        
        public DragCancelEvent(object dragObject, string reason = null, string source = null) 
            : base(source)
        {
            DragObject = dragObject;
            Reason = reason;
        }
    }
    
    #endregion
    
    #region 选择事件
    
    /// <summary>
    /// 选择改变事件
    /// </summary>
    public class SelectionChangedEvent : GanttEventBase
    {
        public object[] OldSelection { get; }
        public object[] NewSelection { get; }
        public bool IsMultiSelect { get; }
        
        public SelectionChangedEvent(object[] oldSelection, object[] newSelection, 
            bool isMultiSelect = false, string source = null) : base(source)
        {
            OldSelection = oldSelection ?? new object[0];
            NewSelection = newSelection ?? new object[0];
            IsMultiSelect = isMultiSelect;
        }
    }
    
    /// <summary>
    /// 选择清除事件
    /// </summary>
    public class SelectionClearedEvent : GanttEventBase
    {
        public object[] PreviousSelection { get; }
        
        public SelectionClearedEvent(object[] previousSelection, string source = null) 
            : base(source)
        {
            PreviousSelection = previousSelection ?? new object[0];
        }
    }
    
    #endregion
    
    #region 编辑事件
    
    /// <summary>
    /// 编辑开始事件
    /// </summary>
    public class EditStartEvent : GanttEventBase
    {
        public object EditTarget { get; }
        public object Target => EditTarget;
        public EditType EditType { get; }
        public object CurrentValue { get; }

        public EditStartEvent(object editTarget, EditType editType, object currentValue,
            string source = null) : base(source)
        {
            EditTarget = editTarget;
            EditType = editType;
            CurrentValue = currentValue;
        }
    }
    
    /// <summary>
    /// 编辑完成事件
    /// </summary>
    public class EditCompleteEvent : GanttEventBase
    {
        public object EditTarget { get; }
        public object Target => EditTarget;
        public EditType EditType { get; }
        public object OldValue { get; }
        public object NewValue { get; }
        public bool Success { get; }

        public EditCompleteEvent(object editTarget, EditType editType, object oldValue,
            object newValue, bool success, string source = null) : base(source)
        {
            EditTarget = editTarget;
            EditType = editType;
            OldValue = oldValue;
            NewValue = newValue;
            Success = success;
        }
    }
    
    /// <summary>
    /// 编辑取消事件
    /// </summary>
    public class EditCancelEvent : GanttEventBase
    {
        public object EditTarget { get; }
        public EditType EditType { get; }
        public string Reason { get; }
        
        public EditCancelEvent(object editTarget, EditType editType, string reason = null, 
            string source = null) : base(source)
        {
            EditTarget = editTarget;
            EditType = editType;
            Reason = reason;
        }
    }
    
    #endregion
    
    #region 数据事件
    
    /// <summary>
    /// 任务添加事件
    /// </summary>
    public class TaskAddedEvent : GanttEventBase
    {
        public GanttTask Task { get; }
        public int Position { get; }
        
        public TaskAddedEvent(GanttTask task, int position, string source = null) : base(source)
        {
            Task = task;
            Position = position;
        }
    }
    
    /// <summary>
    /// 任务删除事件
    /// </summary>
    public class TaskDeletedEvent : GanttEventBase
    {
        public GanttTask Task { get; }
        public int Position { get; }
        
        public TaskDeletedEvent(GanttTask task, int position, string source = null) : base(source)
        {
            Task = task;
            Position = position;
        }
    }
    
    /// <summary>
    /// 任务更新事件
    /// </summary>
    public class TaskUpdatedEvent : GanttEventBase
    {
        public GanttTask Task { get; }
        public string PropertyName { get; }
        public object OldValue { get; }
        public object NewValue { get; }
        
        public TaskUpdatedEvent(GanttTask task, string propertyName, object oldValue, 
            object newValue, string source = null) : base(source)
        {
            Task = task;
            PropertyName = propertyName;
            OldValue = oldValue;
            NewValue = newValue;
        }
    }
    
    /// <summary>
    /// 里程碑添加事件
    /// </summary>
    public class MilestoneAddedEvent : GanttEventBase
    {
        public GanttMilestone Milestone { get; }
        
        public MilestoneAddedEvent(GanttMilestone milestone, string source = null) : base(source)
        {
            Milestone = milestone;
        }
    }
    
    /// <summary>
    /// 里程碑删除事件
    /// </summary>
    public class MilestoneDeletedEvent : GanttEventBase
    {
        public GanttMilestone Milestone { get; }
        
        public MilestoneDeletedEvent(GanttMilestone milestone, string source = null) : base(source)
        {
            Milestone = milestone;
        }
    }
    
    /// <summary>
    /// 里程碑更新事件
    /// </summary>
    public class MilestoneUpdatedEvent : GanttEventBase
    {
        public GanttMilestone Milestone { get; }
        public string PropertyName { get; }
        public object OldValue { get; }
        public object NewValue { get; }
        
        public MilestoneUpdatedEvent(GanttMilestone milestone, string propertyName, 
            object oldValue, object newValue, string source = null) : base(source)
        {
            Milestone = milestone;
            PropertyName = propertyName;
            OldValue = oldValue;
            NewValue = newValue;
        }
    }
    
    #endregion
    
    #region 渲染事件
    
    /// <summary>
    /// 渲染开始事件
    /// </summary>
    public class RenderStartEvent : GanttEventBase
    {
        public string RenderType { get; }
        public object RenderContext { get; }
        
        public RenderStartEvent(string renderType, object renderContext = null, 
            string source = null) : base(source)
        {
            RenderType = renderType;
            RenderContext = renderContext;
        }
    }
    
    /// <summary>
    /// 渲染完成事件
    /// </summary>
    public class RenderCompleteEvent : GanttEventBase
    {
        public string RenderType { get; }
        public TimeSpan Duration { get; }
        public bool Success { get; }
        
        public RenderCompleteEvent(string renderType, TimeSpan duration, bool success, 
            string source = null) : base(source)
        {
            RenderType = renderType;
            Duration = duration;
            Success = success;
        }
    }
    
    #endregion

    #region 布局事件

    /// <summary>
    /// 布局计算开始事件
    /// </summary>
    public class LayoutCalculationStarted : GanttEventBase
    {
        public string ProjectId { get; set; }
        public int TaskCount { get; set; }
        public int MilestoneCount { get; set; }

        public LayoutCalculationStarted(string source = null) : base(source) { }
    }

    /// <summary>
    /// 布局计算完成事件
    /// </summary>
    public class LayoutCalculationCompleted : GanttEventBase
    {
        public string ProjectId { get; set; }
        public double OptimizationScore { get; set; }
        public DateTime CalculationTime { get; set; }

        public LayoutCalculationCompleted(string source = null) : base(source) { }
    }

    /// <summary>
    /// 布局计算失败事件
    /// </summary>
    public class LayoutCalculationFailed : GanttEventBase
    {
        public string ProjectId { get; set; }
        public string Error { get; set; }

        public LayoutCalculationFailed(string source = null) : base(source) { }
    }

    /// <summary>
    /// 布局优化开始事件
    /// </summary>
    public class LayoutOptimizationStarted : GanttEventBase
    {
        public string LayoutId { get; set; }
        public string OptimizationType { get; set; }

        public LayoutOptimizationStarted(string source = null) : base(source) { }
    }

    /// <summary>
    /// 布局优化完成事件
    /// </summary>
    public class LayoutOptimizationCompleted : GanttEventBase
    {
        public string LayoutId { get; set; }
        public double ImprovementScore { get; set; }

        public LayoutOptimizationCompleted(string source = null) : base(source) { }
    }

    /// <summary>
    /// 自适应布局开始事件
    /// </summary>
    public class AdaptiveLayoutStarted : GanttEventBase
    {
        public float OriginalWidth { get; set; }
        public float OriginalHeight { get; set; }

        public AdaptiveLayoutStarted(string source = null) : base(source) { }
    }

    /// <summary>
    /// 自适应布局完成事件
    /// </summary>
    public class AdaptiveLayoutCompleted : GanttEventBase
    {
        public float FinalWidth { get; set; }
        public float FinalHeight { get; set; }
        public double AdaptationScore { get; set; }

        public AdaptiveLayoutCompleted(string source = null) : base(source) { }
    }

    #endregion

    #region 任务选择事件

    /// <summary>
    /// 任务选择事件
    /// </summary>
    public class TaskSelectedEvent : GanttEventBase
    {
        public string TaskId { get; }
        public bool IsSelected { get; }

        public TaskSelectedEvent(string taskId, bool isSelected, string source = null) : base(source)
        {
            TaskId = taskId;
            IsSelected = isSelected;
        }
    }

    #endregion

    #region 组件状态改变事件

    /// <summary>
    /// 组件状态改变事件（简化版）
    /// </summary>
    public class ComponentStateChanged : GanttEventBase
    {
        public string ComponentId { get; }
        public ComponentState OldState { get; }
        public ComponentState NewState { get; }

        public ComponentStateChanged(string componentId, ComponentState oldState,
            ComponentState newState, string source = null) : base(source)
        {
            ComponentId = componentId;
            OldState = oldState;
            NewState = newState;
        }
    }

    #endregion

    #region 错误事件
    
    /// <summary>
    /// 错误事件
    /// </summary>
    public class ErrorEvent : GanttEventBase
    {
        public Exception Exception { get; }
        public string ErrorMessage { get; }
        public string Context { get; }
        
        public ErrorEvent(Exception exception, string context = null, string source = null) 
            : base(source)
        {
            Exception = exception;
            ErrorMessage = exception?.Message;
            Context = context;
        }
        
        public ErrorEvent(string errorMessage, string context = null, string source = null) 
            : base(source)
        {
            ErrorMessage = errorMessage;
            Context = context;
        }
    }
    
    #endregion
}
