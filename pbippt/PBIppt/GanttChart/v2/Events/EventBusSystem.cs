using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using PBIppt.Utils;

namespace PBIppt.GanttChart.v2.Events
{
    /// <summary>
    /// 事件总线系统
    /// 提供组件间松耦合的事件通信机制
    /// </summary>
    public class EventBusSystem : IDisposable
    {
        #region 单例模式
        
        private static readonly Lazy<EventBusSystem> _instance = 
            new Lazy<EventBusSystem>(() => new EventBusSystem());
        
        /// <summary>
        /// 获取事件总线实例
        /// </summary>
        public static EventBusSystem Instance => _instance.Value;
        
        #endregion
        
        #region 私有字段
        
        private readonly ConcurrentDictionary<Type, ConcurrentBag<IEventHandler>> _handlers;
        private readonly ConcurrentDictionary<string, IEventHandler> _namedHandlers;
        private readonly object _lockObject = new object();
        private bool _disposed = false;
        
        #endregion
        
        #region 构造函数
        
        private EventBusSystem()
        {
            _handlers = new ConcurrentDictionary<Type, ConcurrentBag<IEventHandler>>();
            _namedHandlers = new ConcurrentDictionary<string, IEventHandler>();
            Logger.Info("事件总线系统已初始化");
        }
        
        #endregion
        
        #region 事件订阅
        
        /// <summary>
        /// 订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        /// <param name="handlerName">处理器名称（可选）</param>
        public void Subscribe<T>(IEventHandler<T> handler, string handlerName = null) 
            where T : class, IEvent
        {
            if (handler == null)
                throw new ArgumentNullException(nameof(handler));
            
            var eventType = typeof(T);
            
            lock (_lockObject)
            {
                // 添加到类型处理器集合
                var handlers = _handlers.GetOrAdd(eventType, _ => new ConcurrentBag<IEventHandler>());
                handlers.Add(handler);
                
                // 如果有名称，添加到命名处理器集合
                if (!string.IsNullOrEmpty(handlerName))
                {
                    _namedHandlers.TryAdd(handlerName, handler);
                }
            }
            
            Logger.Debug($"已订阅事件: {eventType.Name}, 处理器: {handlerName ?? "匿名"}");
        }
        
        /// <summary>
        /// 订阅事件（使用委托）
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="action">处理委托</param>
        /// <param name="handlerName">处理器名称（可选）</param>
        public void Subscribe<T>(Action<T> action, string handlerName = null) 
            where T : class, IEvent
        {
            if (action == null)
                throw new ArgumentNullException(nameof(action));
            
            var handler = new DelegateEventHandler<T>(action);
            Subscribe(handler, handlerName);
        }
        
        #endregion
        
        #region 事件取消订阅
        
        /// <summary>
        /// 取消订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        public void Unsubscribe<T>(IEventHandler<T> handler) 
            where T : class, IEvent
        {
            if (handler == null) return;
            
            var eventType = typeof(T);
            
            lock (_lockObject)
            {
                if (_handlers.TryGetValue(eventType, out var handlers))
                {
                    // 创建新的处理器集合，排除指定处理器
                    var newHandlers = new ConcurrentBag<IEventHandler>(
                        handlers.Where(h => !ReferenceEquals(h, handler)));
                    _handlers.TryUpdate(eventType, newHandlers, handlers);
                }
                
                // 从命名处理器中移除
                var toRemove = _namedHandlers.Where(kvp => ReferenceEquals(kvp.Value, handler))
                                           .Select(kvp => kvp.Key)
                                           .ToList();
                foreach (var key in toRemove)
                {
                    _namedHandlers.TryRemove(key, out _);
                }
            }
            
            Logger.Debug($"已取消订阅事件: {eventType.Name}");
        }
        
        /// <summary>
        /// 根据名称取消订阅
        /// </summary>
        /// <param name="handlerName">处理器名称</param>
        public void Unsubscribe(string handlerName)
        {
            if (string.IsNullOrEmpty(handlerName)) return;
            
            lock (_lockObject)
            {
                if (_namedHandlers.TryRemove(handlerName, out var handler))
                {
                    // 从所有类型处理器中移除
                    foreach (var kvp in _handlers)
                    {
                        var newHandlers = new ConcurrentBag<IEventHandler>(
                            kvp.Value.Where(h => !ReferenceEquals(h, handler)));
                        _handlers.TryUpdate(kvp.Key, newHandlers, kvp.Value);
                    }
                }
            }
            
            Logger.Debug($"已取消订阅处理器: {handlerName}");
        }
        
        #endregion
        
        #region 事件发布
        
        /// <summary>
        /// 发布事件（同步）
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        public void Publish<T>(T eventData) where T : class, IEvent
        {
            if (eventData == null)
                throw new ArgumentNullException(nameof(eventData));
            
            var eventType = typeof(T);
            
            if (_handlers.TryGetValue(eventType, out var handlers))
            {
                var handlerList = handlers.ToList();
                
                foreach (var handler in handlerList)
                {
                    try
                    {
                        if (handler is IEventHandler<T> typedHandler)
                        {
                            typedHandler.Handle(eventData);
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"事件处理器执行失败: {ex.Message}");
                        // 继续处理其他处理器，不因一个失败而中断
                    }
                }
                
                Logger.Debug($"已发布事件: {eventType.Name}, 处理器数量: {handlerList.Count}");
            }
        }
        
        /// <summary>
        /// 发布事件（异步）
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        public async Task PublishAsync<T>(T eventData) where T : class, IEvent
        {
            if (eventData == null)
                throw new ArgumentNullException(nameof(eventData));
            
            var eventType = typeof(T);
            
            if (_handlers.TryGetValue(eventType, out var handlers))
            {
                var handlerList = handlers.ToList();
                var tasks = new List<Task>();
                
                foreach (var handler in handlerList)
                {
                    if (handler is IEventHandler<T> typedHandler)
                    {
                        tasks.Add(Task.Run(() =>
                        {
                            try
                            {
                                typedHandler.Handle(eventData);
                            }
                            catch (Exception ex)
                            {
                                Logger.Error($"异步事件处理器执行失败: {ex.Message}");
                            }
                        }));
                    }
                }
                
                await Task.WhenAll(tasks);
                Logger.Debug($"已异步发布事件: {eventType.Name}, 处理器数量: {handlerList.Count}");
            }
        }
        
        #endregion
        
        #region 查询方法
        
        /// <summary>
        /// 获取指定事件类型的处理器数量
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <returns>处理器数量</returns>
        public int GetHandlerCount<T>() where T : class, IEvent
        {
            var eventType = typeof(T);
            return _handlers.TryGetValue(eventType, out var handlers) ? handlers.Count : 0;
        }
        
        /// <summary>
        /// 检查是否有指定事件的处理器
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <returns>是否有处理器</returns>
        public bool HasHandlers<T>() where T : class, IEvent
        {
            return GetHandlerCount<T>() > 0;
        }
        
        /// <summary>
        /// 获取所有已注册的事件类型
        /// </summary>
        /// <returns>事件类型列表</returns>
        public Type[] GetRegisteredEventTypes()
        {
            return _handlers.Keys.ToArray();
        }
        
        #endregion
        
        #region 清理方法
        
        /// <summary>
        /// 清除所有事件处理器
        /// </summary>
        public void Clear()
        {
            lock (_lockObject)
            {
                _handlers.Clear();
                _namedHandlers.Clear();
            }
            
            Logger.Info("已清除所有事件处理器");
        }
        
        /// <summary>
        /// 清除指定事件类型的所有处理器
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        public void Clear<T>() where T : class, IEvent
        {
            var eventType = typeof(T);
            
            lock (_lockObject)
            {
                _handlers.TryRemove(eventType, out _);
                
                // 从命名处理器中移除相关处理器
                var toRemove = _namedHandlers.Where(kvp => kvp.Value is IEventHandler<T>)
                                           .Select(kvp => kvp.Key)
                                           .ToList();
                foreach (var key in toRemove)
                {
                    _namedHandlers.TryRemove(key, out _);
                }
            }
            
            Logger.Debug($"已清除事件类型的所有处理器: {eventType.Name}");
        }
        
        #endregion
        
        #region IDisposable实现
        
        public void Dispose()
        {
            if (!_disposed)
            {
                Clear();
                _disposed = true;
                Logger.Info("事件总线系统已销毁");
            }
        }
        
        #endregion
    }
    
    #region 接口定义
    
    /// <summary>
    /// 事件接口
    /// </summary>
    public interface IEvent
    {
        /// <summary>
        /// 事件ID
        /// </summary>
        string EventId { get; }
        
        /// <summary>
        /// 事件时间戳
        /// </summary>
        DateTime Timestamp { get; }
        
        /// <summary>
        /// 事件源
        /// </summary>
        string Source { get; }
    }
    
    /// <summary>
    /// 事件处理器接口
    /// </summary>
    public interface IEventHandler
    {
        /// <summary>
        /// 处理器名称
        /// </summary>
        string Name { get; }
    }
    
    /// <summary>
    /// 泛型事件处理器接口
    /// </summary>
    /// <typeparam name="T">事件类型</typeparam>
    public interface IEventHandler<in T> : IEventHandler where T : class, IEvent
    {
        /// <summary>
        /// 处理事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        void Handle(T eventData);
    }
    
    #endregion
    
    #region 委托事件处理器
    
    /// <summary>
    /// 委托事件处理器
    /// </summary>
    /// <typeparam name="T">事件类型</typeparam>
    internal class DelegateEventHandler<T> : IEventHandler<T> where T : class, IEvent
    {
        private readonly Action<T> _action;
        
        public string Name { get; }
        
        public DelegateEventHandler(Action<T> action, string name = null)
        {
            _action = action ?? throw new ArgumentNullException(nameof(action));
            Name = name ?? $"Delegate_{Guid.NewGuid():N}";
        }
        
        public void Handle(T eventData)
        {
            _action(eventData);
        }
    }
    
    #endregion
}
