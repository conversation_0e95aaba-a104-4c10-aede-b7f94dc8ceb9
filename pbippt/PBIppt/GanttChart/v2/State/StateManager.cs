using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using PBIppt.GanttChart.Models;
using PBIppt.GanttChart.v2.Components;
using PBIppt.GanttChart.v2.Events;
using PBIppt.Utils;

namespace PBIppt.GanttChart.v2.State
{
    /// <summary>
    /// 状态管理器 v2.0
    /// 提供统一的状态管理、撤销重做和数据验证功能
    /// </summary>
    public class StateManager : IDisposable
    {
        #region 私有字段
        
        private readonly string _componentId;
        private readonly Stack<StateSnapshot> _undoStack;
        private readonly Stack<StateSnapshot> _redoStack;
        private readonly int _maxHistorySize;
        private ComponentState _currentState;
        private GanttProject _currentProject;
        private readonly object _lockObject = new object();
        private bool _disposed = false;
        
        #endregion
        
        #region 属性
        
        /// <summary>
        /// 组件ID
        /// </summary>
        public string ComponentId => _componentId;
        
        /// <summary>
        /// 当前状态
        /// </summary>
        public ComponentState CurrentState
        {
            get => _currentState;
            private set
            {
                if (_currentState != value)
                {
                    var oldState = _currentState;
                    _currentState = value;
                    OnStateChanged(oldState, value);
                }
            }
        }
        
        /// <summary>
        /// 当前项目数据
        /// </summary>
        public GanttProject CurrentProject
        {
            get => _currentProject;
            set
            {
                if (_currentProject != value)
                {
                    _currentProject = value;
                    OnProjectChanged();
                }
            }
        }
        
        /// <summary>
        /// 是否可以撤销
        /// </summary>
        public bool CanUndo => _undoStack.Count > 0;
        
        /// <summary>
        /// 是否可以重做
        /// </summary>
        public bool CanRedo => _redoStack.Count > 0;
        
        /// <summary>
        /// 撤销栈大小
        /// </summary>
        public int UndoStackSize => _undoStack.Count;
        
        /// <summary>
        /// 重做栈大小
        /// </summary>
        public int RedoStackSize => _redoStack.Count;
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 状态改变事件
        /// </summary>
        public event EventHandler<StateChangedEventArgs> StateChanged;
        
        /// <summary>
        /// 项目数据改变事件
        /// </summary>
        public event EventHandler<ProjectChangedEventArgs> ProjectChanged;
        
        /// <summary>
        /// 撤销重做状态改变事件
        /// </summary>
        public event EventHandler<UndoRedoStateChangedEventArgs> UndoRedoStateChanged;
        
        #endregion
        
        #region 构造函数
        
        /// <summary>
        /// 初始化状态管理器
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <param name="maxHistorySize">最大历史记录数量</param>
        public StateManager(string componentId, int maxHistorySize = 50)
        {
            _componentId = componentId ?? throw new ArgumentNullException(nameof(componentId));
            _maxHistorySize = Math.Max(1, maxHistorySize);
            _undoStack = new Stack<StateSnapshot>();
            _redoStack = new Stack<StateSnapshot>();
            _currentState = ComponentState.Uninitialized;
            
            Logger.Info($"状态管理器已初始化: {componentId}, 最大历史: {_maxHistorySize}");
        }
        
        #endregion
        
        #region 状态管理
        
        /// <summary>
        /// 初始化状态
        /// </summary>
        /// <param name="project">初始项目数据</param>
        public void Initialize(GanttProject project)
        {
            lock (_lockObject)
            {
                CurrentProject = project;
                CurrentState = ComponentState.Normal;
                
                // 创建初始快照
                SaveSnapshot("初始化", false);
            }
            
            Logger.Info($"状态管理器已初始化项目: {project?.Name}");
        }
        
        /// <summary>
        /// 更新状态
        /// </summary>
        /// <param name="newState">新状态</param>
        public void UpdateState(ComponentState newState)
        {
            lock (_lockObject)
            {
                CurrentState = newState;
            }
        }
        
        /// <summary>
        /// 保存当前状态快照
        /// </summary>
        /// <param name="description">操作描述</param>
        /// <param name="clearRedoStack">是否清除重做栈</param>
        public void SaveSnapshot(string description = null, bool clearRedoStack = true)
        {
            lock (_lockObject)
            {
                try
                {
                    var snapshot = CreateSnapshot(description);
                    _undoStack.Push(snapshot);
                    
                    // 限制历史记录大小
                    while (_undoStack.Count > _maxHistorySize)
                    {
                        var oldSnapshots = _undoStack.ToArray().Reverse().ToArray();
                        _undoStack.Clear();
                        for (int i = 1; i < oldSnapshots.Length; i++)
                        {
                            _undoStack.Push(oldSnapshots[i]);
                        }
                    }
                    
                    // 清除重做栈
                    if (clearRedoStack)
                    {
                        _redoStack.Clear();
                    }
                    
                    OnUndoRedoStateChanged();
                    Logger.Debug($"已保存状态快照: {description}");
                }
                catch (Exception ex)
                {
                    Logger.Error($"保存状态快照失败: {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// 撤销操作
        /// </summary>
        /// <returns>是否成功撤销</returns>
        public bool Undo()
        {
            lock (_lockObject)
            {
                if (!CanUndo) return false;
                
                try
                {
                    // 保存当前状态到重做栈
                    var currentSnapshot = CreateSnapshot("撤销前状态");
                    _redoStack.Push(currentSnapshot);
                    
                    // 恢复上一个状态
                    var previousSnapshot = _undoStack.Pop();
                    RestoreSnapshot(previousSnapshot);
                    
                    OnUndoRedoStateChanged();
                    Logger.Info($"已撤销操作: {previousSnapshot.Description}");
                    return true;
                }
                catch (Exception ex)
                {
                    Logger.Error($"撤销操作失败: {ex.Message}");
                    return false;
                }
            }
        }
        
        /// <summary>
        /// 重做操作
        /// </summary>
        /// <returns>是否成功重做</returns>
        public bool Redo()
        {
            lock (_lockObject)
            {
                if (!CanRedo) return false;
                
                try
                {
                    // 保存当前状态到撤销栈
                    var currentSnapshot = CreateSnapshot("重做前状态");
                    _undoStack.Push(currentSnapshot);
                    
                    // 恢复重做状态
                    var redoSnapshot = _redoStack.Pop();
                    RestoreSnapshot(redoSnapshot);
                    
                    OnUndoRedoStateChanged();
                    Logger.Info($"已重做操作: {redoSnapshot.Description}");
                    return true;
                }
                catch (Exception ex)
                {
                    Logger.Error($"重做操作失败: {ex.Message}");
                    return false;
                }
            }
        }
        
        #endregion
        
        #region 快照管理
        
        /// <summary>
        /// 创建状态快照
        /// </summary>
        /// <param name="description">快照描述</param>
        /// <returns>状态快照</returns>
        private StateSnapshot CreateSnapshot(string description)
        {
            return new StateSnapshot
            {
                Id = Guid.NewGuid().ToString("N"),
                ComponentId = _componentId,
                Timestamp = DateTime.Now,
                Description = description ?? "未命名操作",
                State = _currentState,
                ProjectData = SerializeProject(_currentProject),
                Version = "2.0"
            };
        }
        
        /// <summary>
        /// 恢复状态快照
        /// </summary>
        /// <param name="snapshot">状态快照</param>
        private void RestoreSnapshot(StateSnapshot snapshot)
        {
            if (snapshot == null) return;
            
            CurrentState = snapshot.State;
            CurrentProject = DeserializeProject(snapshot.ProjectData);
        }
        
        /// <summary>
        /// 序列化项目数据
        /// </summary>
        /// <param name="project">项目对象</param>
        /// <returns>序列化字符串</returns>
        private string SerializeProject(GanttProject project)
        {
            if (project == null) return null;
            
            try
            {
                var settings = new JsonSerializerSettings
                {
                    DateFormatHandling = DateFormatHandling.IsoDateFormat,
                    NullValueHandling = NullValueHandling.Ignore,
                    Formatting = Formatting.None
                };
                
                return JsonConvert.SerializeObject(project, settings);
            }
            catch (Exception ex)
            {
                Logger.Error($"序列化项目数据失败: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 反序列化项目数据
        /// </summary>
        /// <param name="projectData">序列化字符串</param>
        /// <returns>项目对象</returns>
        private GanttProject DeserializeProject(string projectData)
        {
            if (string.IsNullOrEmpty(projectData)) return null;
            
            try
            {
                var settings = new JsonSerializerSettings
                {
                    DateFormatHandling = DateFormatHandling.IsoDateFormat,
                    NullValueHandling = NullValueHandling.Ignore
                };
                
                return JsonConvert.DeserializeObject<GanttProject>(projectData, settings);
            }
            catch (Exception ex)
            {
                Logger.Error($"反序列化项目数据失败: {ex.Message}");
                return null;
            }
        }
        
        #endregion
        
        #region 查询方法
        
        /// <summary>
        /// 获取撤销历史
        /// </summary>
        /// <returns>撤销历史列表</returns>
        public StateSnapshot[] GetUndoHistory()
        {
            lock (_lockObject)
            {
                return _undoStack.ToArray();
            }
        }
        
        /// <summary>
        /// 获取重做历史
        /// </summary>
        /// <returns>重做历史列表</returns>
        public StateSnapshot[] GetRedoHistory()
        {
            lock (_lockObject)
            {
                return _redoStack.ToArray();
            }
        }
        
        /// <summary>
        /// 清除所有历史记录
        /// </summary>
        public void ClearHistory()
        {
            lock (_lockObject)
            {
                _undoStack.Clear();
                _redoStack.Clear();
                OnUndoRedoStateChanged();
            }

            Logger.Info("已清除所有历史记录");
        }

        /// <summary>
        /// 保存状态（别名方法，用于兼容性）
        /// </summary>
        /// <param name="description">操作描述</param>
        public void SaveState(string description = null)
        {
            SaveSnapshot(description, true);
        }
        
        #endregion
        
        #region 事件处理
        
        private void OnStateChanged(ComponentState oldState, ComponentState newState)
        {
            StateChanged?.Invoke(this, new StateChangedEventArgs
            {
                ComponentId = _componentId,
                OldState = oldState,
                NewState = newState,
                Timestamp = DateTime.Now
            });
            
            // 发布事件到事件总线
            EventBusSystem.Instance.Publish(new ComponentStateChangedEvent(
                _componentId, oldState, newState, "StateManager"));
        }
        
        private void OnProjectChanged()
        {
            ProjectChanged?.Invoke(this, new ProjectChangedEventArgs
            {
                ComponentId = _componentId,
                Project = _currentProject,
                Timestamp = DateTime.Now
            });
        }
        
        private void OnUndoRedoStateChanged()
        {
            UndoRedoStateChanged?.Invoke(this, new UndoRedoStateChangedEventArgs
            {
                ComponentId = _componentId,
                CanUndo = CanUndo,
                CanRedo = CanRedo,
                UndoStackSize = UndoStackSize,
                RedoStackSize = RedoStackSize,
                Timestamp = DateTime.Now
            });
        }
        
        #endregion
        
        #region IDisposable实现
        
        public void Dispose()
        {
            if (!_disposed)
            {
                ClearHistory();
                _currentProject = null;
                CurrentState = ComponentState.Disposed;
                _disposed = true;
                
                Logger.Info($"状态管理器已销毁: {_componentId}");
            }
        }
        
        #endregion
    }
    
    #region 事件参数类
    
    /// <summary>
    /// 状态改变事件参数
    /// </summary>
    public class StateChangedEventArgs : EventArgs
    {
        public string ComponentId { get; set; }
        public ComponentState OldState { get; set; }
        public ComponentState NewState { get; set; }
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 项目改变事件参数
    /// </summary>
    public class ProjectChangedEventArgs : EventArgs
    {
        public string ComponentId { get; set; }
        public GanttProject Project { get; set; }
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 撤销重做状态改变事件参数
    /// </summary>
    public class UndoRedoStateChangedEventArgs : EventArgs
    {
        public string ComponentId { get; set; }
        public bool CanUndo { get; set; }
        public bool CanRedo { get; set; }
        public int UndoStackSize { get; set; }
        public int RedoStackSize { get; set; }
        public DateTime Timestamp { get; set; }
    }
    
    #endregion
    
    #region 状态快照类
    
    /// <summary>
    /// 状态快照
    /// </summary>
    public class StateSnapshot
    {
        /// <summary>快照ID</summary>
        public string Id { get; set; }
        
        /// <summary>组件ID</summary>
        public string ComponentId { get; set; }
        
        /// <summary>时间戳</summary>
        public DateTime Timestamp { get; set; }
        
        /// <summary>操作描述</summary>
        public string Description { get; set; }
        
        /// <summary>组件状态</summary>
        public ComponentState State { get; set; }
        
        /// <summary>项目数据</summary>
        public string ProjectData { get; set; }
        
        /// <summary>版本号</summary>
        public string Version { get; set; }
    }
    
    #endregion
}
