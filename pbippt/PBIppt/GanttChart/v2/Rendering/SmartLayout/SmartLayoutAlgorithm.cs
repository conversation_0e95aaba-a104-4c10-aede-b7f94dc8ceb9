using System;
using System.Collections.Generic;
using System.Linq;
using PBIppt.GanttChart.Models;
using PBIppt.GanttChart.Utils;
using PBIppt.GanttChart.v2.Events;

namespace PBIppt.GanttChart.v2.Rendering.SmartLayout
{
    /// <summary>
    /// 智能布局算法 - 自动优化甘特图布局
    /// 提供ThinkCell级别的智能布局体验
    /// </summary>
    public class SmartLayoutAlgorithm
    {
        private readonly EventBusSystem _eventBus;
        private readonly LayoutOptimizer _optimizer;
        private readonly AdaptiveLayoutManager _adaptiveManager;

        /// <summary>
        /// 布局配置
        /// </summary>
        public SmartLayoutConfig Config { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public SmartLayoutAlgorithm()
        {
            _eventBus = EventBusSystem.Instance;
            _optimizer = new LayoutOptimizer();
            _adaptiveManager = new AdaptiveLayoutManager();
            Config = SmartLayoutConfig.CreateDefault();
        }

        /// <summary>
        /// 计算智能布局
        /// </summary>
        /// <param name="project">甘特图项目</param>
        /// <param name="constraints">布局约束</param>
        /// <returns>优化后的布局</returns>
        public SmartLayoutResult CalculateOptimalLayout(GanttProject project, LayoutConstraints constraints)
        {
            try
            {
                _eventBus.Publish(new LayoutCalculationStarted
                {
                    ProjectId = project.Id,
                    TaskCount = project.Tasks.Count,
                    MilestoneCount = project.Milestones.Count
                });

                // 1. 分析项目特征
                var analysis = AnalyzeProjectCharacteristics(project);

                // 2. 生成候选布局方案
                var candidates = GenerateLayoutCandidates(project, constraints, analysis);

                // 3. 评估和优化布局方案
                var optimizedLayouts = _optimizer.OptimizeLayouts(candidates, analysis);

                // 4. 选择最佳布局
                var bestLayout = SelectBestLayout(optimizedLayouts, constraints);

                // 5. 应用自适应调整
                var finalLayout = _adaptiveManager.ApplyAdaptiveAdjustments(bestLayout, constraints);

                var result = new SmartLayoutResult
                {
                    Layout = finalLayout,
                    Analysis = analysis,
                    OptimizationScore = CalculateOptimizationScore(finalLayout, analysis),
                    AppliedOptimizations = GetAppliedOptimizations(finalLayout),
                    PerformanceMetrics = CalculatePerformanceMetrics(finalLayout, project)
                };

                _eventBus.Publish(new LayoutCalculationCompleted
                {
                    ProjectId = project.Id,
                    OptimizationScore = result.OptimizationScore,
                    CalculationTime = DateTime.Now
                });

                return result;
            }
            catch (Exception ex)
            {
                _eventBus.Publish(new LayoutCalculationFailed
                {
                    ProjectId = project.Id,
                    Error = ex.Message
                });
                throw;
            }
        }

        /// <summary>
        /// 分析项目特征
        /// </summary>
        private ProjectAnalysis AnalyzeProjectCharacteristics(GanttProject project)
        {
            var analysis = new ProjectAnalysis
            {
                TaskCount = project.Tasks.Count,
                MilestoneCount = project.Milestones.Count,
                DependencyCount = project.Dependencies.Count,
                TimeSpan = (project.TimelineSettings.EndDate - project.TimelineSettings.StartDate).Days,
                MaxTaskDepth = CalculateMaxTaskDepth(project.Tasks),
                TaskDensity = CalculateTaskDensity(project),
                CriticalPathLength = CalculateCriticalPathLength(project),
                ResourceUtilization = CalculateResourceUtilization(project),
                ComplexityLevel = DetermineComplexityLevel(project)
            };

            // 识别布局挑战
            analysis.LayoutChallenges = IdentifyLayoutChallenges(project, analysis);

            return analysis;
        }

        /// <summary>
        /// 生成候选布局方案
        /// </summary>
        private List<GanttLayout> GenerateLayoutCandidates(GanttProject project, LayoutConstraints constraints, ProjectAnalysis analysis)
        {
            var candidates = new List<GanttLayout>();

            // 1. 基础布局（基于现有算法）
            var baseLayout = GenerateBaseLayout(project, constraints);
            candidates.Add(baseLayout);

            // 2. 紧凑布局（适合任务密集型项目）
            if (analysis.TaskDensity > Config.HighDensityThreshold)
            {
                var compactLayout = GenerateCompactLayout(project, constraints, analysis);
                candidates.Add(compactLayout);
            }

            // 3. 宽松布局（适合复杂依赖关系）
            if (analysis.DependencyCount > Config.HighDependencyThreshold)
            {
                var spaciousLayout = GenerateSpaciousLayout(project, constraints, analysis);
                candidates.Add(spaciousLayout);
            }

            // 4. 时间优化布局（适合长期项目）
            if (analysis.TimeSpan > Config.LongTermProjectThreshold)
            {
                var timeOptimizedLayout = GenerateTimeOptimizedLayout(project, constraints, analysis);
                candidates.Add(timeOptimizedLayout);
            }

            // 5. 层次化布局（适合深层任务结构）
            if (analysis.MaxTaskDepth > Config.DeepHierarchyThreshold)
            {
                var hierarchicalLayout = GenerateHierarchicalLayout(project, constraints, analysis);
                candidates.Add(hierarchicalLayout);
            }

            return candidates;
        }

        /// <summary>
        /// 选择最佳布局
        /// </summary>
        private GanttLayout SelectBestLayout(List<GanttLayout> optimizedLayouts, LayoutConstraints constraints)
        {
            var bestLayout = optimizedLayouts.First();
            var bestScore = double.MinValue;

            foreach (var layout in optimizedLayouts)
            {
                var score = CalculateLayoutScore(layout, constraints);
                if (score > bestScore)
                {
                    bestScore = score;
                    bestLayout = layout;
                }
            }

            return bestLayout;
        }

        /// <summary>
        /// 计算布局评分
        /// </summary>
        private double CalculateLayoutScore(GanttLayout layout, LayoutConstraints constraints)
        {
            double score = 0;

            // 空间利用率评分 (30%)
            score += CalculateSpaceUtilizationScore(layout, constraints) * 0.3;

            // 可读性评分 (25%)
            score += CalculateReadabilityScore(layout) * 0.25;

            // 美观度评分 (20%)
            score += CalculateAestheticsScore(layout) * 0.2;

            // 性能评分 (15%)
            score += CalculatePerformanceScore(layout) * 0.15;

            // 约束满足度评分 (10%)
            score += CalculateConstraintSatisfactionScore(layout, constraints) * 0.1;

            return score;
        }

        /// <summary>
        /// 计算空间利用率评分
        /// </summary>
        private double CalculateSpaceUtilizationScore(GanttLayout layout, LayoutConstraints constraints)
        {
            var totalArea = layout.TotalWidth * layout.TotalHeight;
            var usedArea = layout.ChartAreaWidth * layout.ChartAreaHeight;
            var utilizationRatio = usedArea / totalArea;

            // 理想利用率在70%-85%之间
            if (utilizationRatio >= 0.7 && utilizationRatio <= 0.85)
                return 100;
            else if (utilizationRatio >= 0.6 && utilizationRatio <= 0.9)
                return 80;
            else if (utilizationRatio >= 0.5 && utilizationRatio <= 0.95)
                return 60;
            else
                return Math.Max(0, 40 - Math.Abs(utilizationRatio - 0.75) * 100);
        }

        /// <summary>
        /// 计算可读性评分
        /// </summary>
        private double CalculateReadabilityScore(GanttLayout layout)
        {
            double score = 100;

            // 行高适中性检查
            if (layout.RowHeight < Config.MinReadableRowHeight)
                score -= 20;
            else if (layout.RowHeight > Config.MaxReadableRowHeight)
                score -= 10;

            // 字体大小适中性检查
            var estimatedFontSize = layout.RowHeight * 0.6f;
            if (estimatedFontSize < Config.MinReadableFontSize)
                score -= 15;

            // 间距合理性检查
            if (layout.RowSpacing < Config.MinRowSpacing)
                score -= 10;

            return Math.Max(0, score);
        }

        /// <summary>
        /// 计算美观度评分
        /// </summary>
        private double CalculateAestheticsScore(GanttLayout layout)
        {
            double score = 100;

            // 黄金比例检查
            var aspectRatio = layout.TotalWidth / layout.TotalHeight;
            var goldenRatio = 1.618;
            var ratioDeviation = Math.Abs(aspectRatio - goldenRatio) / goldenRatio;
            if (ratioDeviation > 0.3)
                score -= 15;

            // 对称性检查
            var leftMargin = layout.ChartAreaLeft;
            var rightMargin = layout.TotalWidth - layout.ChartAreaLeft - layout.ChartAreaWidth;
            var marginBalance = Math.Abs(leftMargin - rightMargin) / layout.TotalWidth;
            if (marginBalance > 0.1)
                score -= 10;

            // 比例协调性检查
            var timelineRatio = layout.TimelineHeight / layout.TotalHeight;
            if (timelineRatio < 0.1 || timelineRatio > 0.25)
                score -= 10;

            return Math.Max(0, score);
        }

        /// <summary>
        /// 计算性能评分
        /// </summary>
        private double CalculatePerformanceScore(GanttLayout layout)
        {
            double score = 100;

            // 渲染复杂度评估
            var estimatedShapeCount = EstimateShapeCount(layout);
            if (estimatedShapeCount > Config.MaxOptimalShapeCount)
                score -= (estimatedShapeCount - Config.MaxOptimalShapeCount) * 0.01;

            // 内存使用评估
            var estimatedMemoryUsage = EstimateMemoryUsage(layout);
            if (estimatedMemoryUsage > Config.MaxOptimalMemoryUsage)
                score -= 20;

            return Math.Max(0, score);
        }

        /// <summary>
        /// 计算约束满足度评分
        /// </summary>
        private double CalculateConstraintSatisfactionScore(GanttLayout layout, LayoutConstraints constraints)
        {
            double score = 100;
            int violationCount = 0;

            // 检查尺寸约束
            if (layout.TotalWidth < constraints.MinWidth || layout.TotalWidth > constraints.MaxWidth)
                violationCount++;
            if (layout.TotalHeight < constraints.MinHeight || layout.TotalHeight > constraints.MaxHeight)
                violationCount++;

            // 检查比例约束
            if (constraints.AspectRatioConstraints.Any())
            {
                var aspectRatio = layout.TotalWidth / layout.TotalHeight;
                if (!constraints.AspectRatioConstraints.Any(c => aspectRatio >= c.Min && aspectRatio <= c.Max))
                    violationCount++;
            }

            // 每个违反的约束扣除相应分数
            score -= violationCount * 25;

            return Math.Max(0, score);
        }

        // 辅助方法
        private int CalculateMaxTaskDepth(List<GanttTask> tasks) => 
            tasks.Any() ? tasks.Max(t => t.Level) : 0;

        private double CalculateTaskDensity(GanttProject project) =>
            project.Tasks.Count / Math.Max(1.0, (project.TimelineSettings.EndDate - project.TimelineSettings.StartDate).Days);

        private int CalculateCriticalPathLength(GanttProject project) =>
            project.Tasks.Count(t => t.IsOnCriticalPath);

        private double CalculateResourceUtilization(GanttProject project) =>
            project.Tasks.Count > 0 ? project.Tasks.Average(t => t.Progress) : 0;

        private ComplexityLevel DetermineComplexityLevel(GanttProject project)
        {
            var score = project.Tasks.Count + project.Dependencies.Count * 2 + project.Milestones.Count;
            if (score < 50) return ComplexityLevel.Simple;
            if (score < 150) return ComplexityLevel.Medium;
            if (score < 300) return ComplexityLevel.Complex;
            return ComplexityLevel.VeryComplex;
        }

        private List<LayoutChallenge> IdentifyLayoutChallenges(GanttProject project, ProjectAnalysis analysis)
        {
            var challenges = new List<LayoutChallenge>();

            if (analysis.TaskDensity > Config.HighDensityThreshold)
                challenges.Add(LayoutChallenge.HighTaskDensity);

            if (analysis.DependencyCount > Config.HighDependencyThreshold)
                challenges.Add(LayoutChallenge.ComplexDependencies);

            if (analysis.MaxTaskDepth > Config.DeepHierarchyThreshold)
                challenges.Add(LayoutChallenge.DeepHierarchy);

            if (analysis.TimeSpan > Config.LongTermProjectThreshold)
                challenges.Add(LayoutChallenge.LongTimeSpan);

            return challenges;
        }

        private int EstimateShapeCount(GanttLayout layout) =>
            (int)((layout.ChartAreaHeight / layout.RowHeight) * 10); // 估算每行10个图形元素

        private double EstimateMemoryUsage(GanttLayout layout) =>
            EstimateShapeCount(layout) * 0.5; // 估算每个图形元素0.5KB

        private double CalculateOptimizationScore(GanttLayout layout, ProjectAnalysis analysis) =>
            CalculateLayoutScore(layout, new LayoutConstraints());

        private List<string> GetAppliedOptimizations(GanttLayout layout) =>
            new List<string> { "智能间距调整", "自适应字体大小", "优化时间轴刻度" };

        private PerformanceMetrics CalculatePerformanceMetrics(GanttLayout layout, GanttProject project) =>
            new PerformanceMetrics
            {
                EstimatedRenderTime = EstimateShapeCount(layout) * 0.01, // 毫秒
                EstimatedMemoryUsage = EstimateMemoryUsage(layout),
                ComplexityScore = (int)DetermineComplexityLevel(project)
            };

        // 布局生成方法（简化实现）
        private GanttLayout GenerateBaseLayout(GanttProject project, LayoutConstraints constraints) =>
            new GanttLayout(); // 使用默认构造函数

        private GanttLayout GenerateCompactLayout(GanttProject project, LayoutConstraints constraints, ProjectAnalysis analysis)
        {
            var layout = new GanttLayout();
            layout.RowHeight *= 0.8f; // 减少行高
            layout.RowSpacing *= 0.7f; // 减少间距
            return layout;
        }

        private GanttLayout GenerateSpaciousLayout(GanttProject project, LayoutConstraints constraints, ProjectAnalysis analysis)
        {
            var layout = new GanttLayout();
            layout.RowHeight *= 1.2f; // 增加行高
            layout.RowSpacing *= 1.3f; // 增加间距
            return layout;
        }

        private GanttLayout GenerateTimeOptimizedLayout(GanttProject project, LayoutConstraints constraints, ProjectAnalysis analysis)
        {
            var layout = new GanttLayout();
            layout.DayWidth *= 0.8f; // 压缩时间轴
            layout.ChartAreaWidth *= 1.1f; // 扩展图表区域
            return layout;
        }

        private GanttLayout GenerateHierarchicalLayout(GanttProject project, LayoutConstraints constraints, ProjectAnalysis analysis)
        {
            var layout = new GanttLayout();
            layout.LabelColumnWidth *= 1.3f; // 扩展标签列以显示层次结构
            layout.RowHeight *= 1.1f; // 略微增加行高
            return layout;
        }
    }
}
