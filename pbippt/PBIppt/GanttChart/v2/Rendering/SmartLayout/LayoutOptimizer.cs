using System;
using System.Collections.Generic;
using System.Linq;
using PBIppt.GanttChart.Utils;
using PBIppt.GanttChart.v2.Events;

namespace PBIppt.GanttChart.v2.Rendering.SmartLayout
{
    /// <summary>
    /// 布局优化器 - 对候选布局进行优化
    /// </summary>
    public class LayoutOptimizer
    {
        private readonly EventBusSystem _eventBus;

        /// <summary>
        /// 优化配置
        /// </summary>
        public OptimizationConfig Config { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public LayoutOptimizer()
        {
            _eventBus = EventBusSystem.Instance;
            Config = OptimizationConfig.CreateDefault();
        }

        /// <summary>
        /// 优化布局列表
        /// </summary>
        /// <param name="candidates">候选布局</param>
        /// <param name="analysis">项目分析</param>
        /// <returns>优化后的布局列表</returns>
        public List<GanttLayout> OptimizeLayouts(List<GanttLayout> candidates, ProjectAnalysis analysis)
        {
            var optimizedLayouts = new List<GanttLayout>();

            foreach (var candidate in candidates)
            {
                _eventBus.Publish(new LayoutOptimizationStarted
                {
                    LayoutId = candidate.GetHashCode().ToString(),
                    OptimizationType = "综合优化"
                });

                var optimized = OptimizeSingleLayout(candidate, analysis);
                optimizedLayouts.Add(optimized);

                _eventBus.Publish(new LayoutOptimizationCompleted
                {
                    LayoutId = optimized.GetHashCode().ToString(),
                    ImprovementScore = CalculateImprovementScore(candidate, optimized)
                });
            }

            return optimizedLayouts;
        }

        /// <summary>
        /// 优化单个布局
        /// </summary>
        private GanttLayout OptimizeSingleLayout(GanttLayout layout, ProjectAnalysis analysis)
        {
            var optimized = layout.Clone();

            // 1. 空间优化
            if (Config.EnableSpaceOptimization)
            {
                optimized = OptimizeSpaceUtilization(optimized, analysis);
            }

            // 2. 可读性优化
            if (Config.EnableReadabilityOptimization)
            {
                optimized = OptimizeReadability(optimized, analysis);
            }

            // 3. 性能优化
            if (Config.EnablePerformanceOptimization)
            {
                optimized = OptimizePerformance(optimized, analysis);
            }

            // 4. 美观度优化
            if (Config.EnableAestheticsOptimization)
            {
                optimized = OptimizeAesthetics(optimized, analysis);
            }

            // 5. 特定挑战优化
            optimized = OptimizeForSpecificChallenges(optimized, analysis);

            return optimized;
        }

        /// <summary>
        /// 优化空间利用率
        /// </summary>
        private GanttLayout OptimizeSpaceUtilization(GanttLayout layout, ProjectAnalysis analysis)
        {
            var optimized = layout.Clone();

            // 根据任务数量调整行高
            if (analysis.TaskCount > 50)
            {
                optimized.RowHeight = Math.Max(Config.MinRowHeight, optimized.RowHeight * 0.9f);
                optimized.RowSpacing = Math.Max(Config.MinRowSpacing, optimized.RowSpacing * 0.8f);
            }

            // 根据时间跨度调整日宽度
            if (analysis.TimeSpan > 365)
            {
                optimized.DayWidth = Math.Max(Config.MinDayWidth, optimized.DayWidth * 0.8f);
            }

            // 优化标签列宽度
            var estimatedLabelWidth = EstimateOptimalLabelWidth(analysis);
            optimized.LabelColumnWidth = Math.Min(optimized.LabelColumnWidth, estimatedLabelWidth);

            // 重新计算图表区域
            optimized.ChartAreaLeft = optimized.LabelColumnWidth;
            optimized.ChartAreaWidth = optimized.TotalWidth - optimized.LabelColumnWidth;

            return optimized;
        }

        /// <summary>
        /// 优化可读性
        /// </summary>
        private GanttLayout OptimizeReadability(GanttLayout layout, ProjectAnalysis analysis)
        {
            var optimized = layout.Clone();

            // 确保最小可读行高
            optimized.RowHeight = Math.Max(Config.MinReadableRowHeight, optimized.RowHeight);

            // 确保足够的行间距
            optimized.RowSpacing = Math.Max(Config.MinReadableRowSpacing, optimized.RowSpacing);

            // 根据复杂度调整字体大小相关参数
            switch (analysis.ComplexityLevel)
            {
                case ComplexityLevel.VeryComplex:
                    optimized.RowHeight *= 1.1f;
                    optimized.RowSpacing *= 1.2f;
                    break;
                case ComplexityLevel.Complex:
                    optimized.RowHeight *= 1.05f;
                    optimized.RowSpacing *= 1.1f;
                    break;
            }

            // 确保时间轴高度适中
            var minTimelineHeight = optimized.TotalHeight * 0.1f;
            var maxTimelineHeight = optimized.TotalHeight * 0.25f;
            optimized.TimelineHeight = Math.Max(minTimelineHeight, 
                                              Math.Min(maxTimelineHeight, optimized.TimelineHeight));

            return optimized;
        }

        /// <summary>
        /// 优化性能
        /// </summary>
        private GanttLayout OptimizePerformance(GanttLayout layout, ProjectAnalysis analysis)
        {
            var optimized = layout.Clone();

            // 如果项目复杂度高，减少细节以提升性能
            if (analysis.ComplexityLevel >= ComplexityLevel.Complex)
            {
                // 减少任务条高度以减少渲染复杂度
                optimized.TaskBarHeight = Math.Max(Config.MinTaskBarHeight, optimized.TaskBarHeight * 0.9f);

                // 如果任务密度高，增加日宽度以减少重叠
                if (analysis.TaskDensity > 2.0)
                {
                    optimized.DayWidth *= 1.1f;
                }
            }

            // 限制最大图表区域以控制内存使用
            var maxChartArea = Config.MaxChartArea;
            var currentChartArea = optimized.ChartAreaWidth * optimized.ChartAreaHeight;
            if (currentChartArea > maxChartArea)
            {
                var scaleFactor = Math.Sqrt(maxChartArea / currentChartArea);
                optimized.ChartAreaWidth *= (float)scaleFactor;
                optimized.ChartAreaHeight *= (float)scaleFactor;
            }

            return optimized;
        }

        /// <summary>
        /// 优化美观度
        /// </summary>
        private GanttLayout OptimizeAesthetics(GanttLayout layout, ProjectAnalysis analysis)
        {
            var optimized = layout.Clone();

            // 应用黄金比例
            var currentAspectRatio = optimized.TotalWidth / optimized.TotalHeight;
            var goldenRatio = 1.618f;
            
            if (Math.Abs(currentAspectRatio - goldenRatio) > 0.3)
            {
                if (currentAspectRatio > goldenRatio)
                {
                    // 太宽，增加高度或减少宽度
                    optimized.TotalHeight = optimized.TotalWidth / goldenRatio;
                }
                else
                {
                    // 太高，增加宽度或减少高度
                    optimized.TotalWidth = optimized.TotalHeight * goldenRatio;
                }
            }

            // 优化边距平衡
            var leftMargin = optimized.ChartAreaLeft;
            var rightMargin = optimized.TotalWidth - optimized.ChartAreaLeft - optimized.ChartAreaWidth;
            var targetRightMargin = leftMargin * 0.3f; // 右边距为左边距的30%

            if (Math.Abs(rightMargin - targetRightMargin) > 10)
            {
                optimized.ChartAreaWidth = optimized.TotalWidth - optimized.ChartAreaLeft - targetRightMargin;
            }

            // 优化垂直比例
            var timelineRatio = optimized.TimelineHeight / optimized.TotalHeight;
            var targetTimelineRatio = 0.15f; // 时间轴占15%

            if (Math.Abs(timelineRatio - targetTimelineRatio) > 0.05)
            {
                optimized.TimelineHeight = optimized.TotalHeight * targetTimelineRatio;
                optimized.ChartAreaTop = optimized.TimelineHeight;
                optimized.ChartAreaHeight = optimized.TotalHeight - optimized.TimelineHeight;
            }

            return optimized;
        }

        /// <summary>
        /// 针对特定挑战进行优化
        /// </summary>
        private GanttLayout OptimizeForSpecificChallenges(GanttLayout layout, ProjectAnalysis analysis)
        {
            var optimized = layout.Clone();

            foreach (var challenge in analysis.LayoutChallenges)
            {
                switch (challenge)
                {
                    case LayoutChallenge.HighTaskDensity:
                        optimized = OptimizeForHighDensity(optimized, analysis);
                        break;
                    case LayoutChallenge.ComplexDependencies:
                        optimized = OptimizeForComplexDependencies(optimized, analysis);
                        break;
                    case LayoutChallenge.DeepHierarchy:
                        optimized = OptimizeForDeepHierarchy(optimized, analysis);
                        break;
                    case LayoutChallenge.LongTimeSpan:
                        optimized = OptimizeForLongTimeSpan(optimized, analysis);
                        break;
                }
            }

            return optimized;
        }

        /// <summary>
        /// 优化高密度任务布局
        /// </summary>
        private GanttLayout OptimizeForHighDensity(GanttLayout layout, ProjectAnalysis analysis)
        {
            var optimized = layout.Clone();
            
            // 减少行高和间距以容纳更多任务
            optimized.RowHeight *= 0.85f;
            optimized.RowSpacing *= 0.7f;
            optimized.TaskBarHeight *= 0.9f;

            // 增加图表区域高度
            optimized.ChartAreaHeight = optimized.TotalHeight - optimized.TimelineHeight;

            return optimized;
        }

        /// <summary>
        /// 优化复杂依赖关系布局
        /// </summary>
        private GanttLayout OptimizeForComplexDependencies(GanttLayout layout, ProjectAnalysis analysis)
        {
            var optimized = layout.Clone();
            
            // 增加行间距以便显示依赖线
            optimized.RowSpacing *= 1.3f;
            optimized.RowHeight *= 1.1f;

            return optimized;
        }

        /// <summary>
        /// 优化深层次结构布局
        /// </summary>
        private GanttLayout OptimizeForDeepHierarchy(GanttLayout layout, ProjectAnalysis analysis)
        {
            var optimized = layout.Clone();
            
            // 增加标签列宽度以显示层次结构
            optimized.LabelColumnWidth *= 1.2f;
            optimized.ChartAreaLeft = optimized.LabelColumnWidth;
            optimized.ChartAreaWidth = optimized.TotalWidth - optimized.LabelColumnWidth;

            return optimized;
        }

        /// <summary>
        /// 优化长时间跨度布局
        /// </summary>
        private GanttLayout OptimizeForLongTimeSpan(GanttLayout layout, ProjectAnalysis analysis)
        {
            var optimized = layout.Clone();
            
            // 减少日宽度以适应长时间跨度
            optimized.DayWidth *= 0.7f;

            // 增加图表区域宽度
            optimized.ChartAreaWidth = optimized.TotalWidth - optimized.LabelColumnWidth;

            return optimized;
        }

        /// <summary>
        /// 估算最优标签宽度
        /// </summary>
        private float EstimateOptimalLabelWidth(ProjectAnalysis analysis)
        {
            // 基础宽度
            float baseWidth = 120;

            // 根据层次深度调整
            baseWidth += analysis.MaxTaskDepth * 20;

            // 根据复杂度调整
            switch (analysis.ComplexityLevel)
            {
                case ComplexityLevel.VeryComplex:
                    baseWidth *= 1.3f;
                    break;
                case ComplexityLevel.Complex:
                    baseWidth *= 1.2f;
                    break;
                case ComplexityLevel.Medium:
                    baseWidth *= 1.1f;
                    break;
            }

            return Math.Min(baseWidth, 300); // 最大不超过300
        }

        /// <summary>
        /// 计算改进评分
        /// </summary>
        private double CalculateImprovementScore(GanttLayout original, GanttLayout optimized)
        {
            // 简化的改进评分计算
            var originalArea = original.TotalWidth * original.TotalHeight;
            var optimizedArea = optimized.TotalWidth * optimized.TotalHeight;
            
            var spaceImprovement = Math.Abs(originalArea - optimizedArea) / originalArea * 100;
            
            return Math.Min(spaceImprovement, 100);
        }
    }

    /// <summary>
    /// 优化配置
    /// </summary>
    public class OptimizationConfig
    {
        public bool EnableSpaceOptimization { get; set; } = true;
        public bool EnableReadabilityOptimization { get; set; } = true;
        public bool EnablePerformanceOptimization { get; set; } = true;
        public bool EnableAestheticsOptimization { get; set; } = true;

        public float MinRowHeight { get; set; } = 16;
        public float MinRowSpacing { get; set; } = 2;
        public float MinDayWidth { get; set; } = 8;
        public float MinTaskBarHeight { get; set; } = 12;
        public float MinReadableRowHeight { get; set; } = 18;
        public float MinReadableRowSpacing { get; set; } = 3;
        public double MaxChartArea { get; set; } = 1000000; // 1M像素

        public static OptimizationConfig CreateDefault() => new OptimizationConfig();
    }
}
