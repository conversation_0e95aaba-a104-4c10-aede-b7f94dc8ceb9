using System;
using System.Collections.Generic;
using PBIppt.GanttChart.Utils;

namespace PBIppt.GanttChart.v2.Rendering.SmartLayout
{
    /// <summary>
    /// 智能布局配置
    /// </summary>
    public class SmartLayoutConfig
    {
        /// <summary>
        /// 高密度任务阈值
        /// </summary>
        public double HighDensityThreshold { get; set; } = 2.0; // 每天2个任务

        /// <summary>
        /// 高依赖关系阈值
        /// </summary>
        public int HighDependencyThreshold { get; set; } = 50;

        /// <summary>
        /// 长期项目阈值（天数）
        /// </summary>
        public int LongTermProjectThreshold { get; set; } = 365;

        /// <summary>
        /// 深层次结构阈值
        /// </summary>
        public int DeepHierarchyThreshold { get; set; } = 5;

        /// <summary>
        /// 最小可读行高
        /// </summary>
        public float MinReadableRowHeight { get; set; } = 18;

        /// <summary>
        /// 最大可读行高
        /// </summary>
        public float MaxReadableRowHeight { get; set; } = 50;

        /// <summary>
        /// 最小可读字体大小
        /// </summary>
        public float MinReadableFontSize { get; set; } = 8;

        /// <summary>
        /// 最小行间距
        /// </summary>
        public float MinRowSpacing { get; set; } = 2;

        /// <summary>
        /// 最大最优图形数量
        /// </summary>
        public int MaxOptimalShapeCount { get; set; } = 1000;

        /// <summary>
        /// 最大最优内存使用（KB）
        /// </summary>
        public double MaxOptimalMemoryUsage { get; set; } = 500;

        /// <summary>
        /// 创建默认配置
        /// </summary>
        public static SmartLayoutConfig CreateDefault() => new SmartLayoutConfig();

        /// <summary>
        /// 创建高性能配置
        /// </summary>
        public static SmartLayoutConfig CreateHighPerformance()
        {
            return new SmartLayoutConfig
            {
                MaxOptimalShapeCount = 500,
                MaxOptimalMemoryUsage = 250,
                MinReadableRowHeight = 16,
                MinRowSpacing = 1
            };
        }

        /// <summary>
        /// 创建高质量配置
        /// </summary>
        public static SmartLayoutConfig CreateHighQuality()
        {
            return new SmartLayoutConfig
            {
                MaxOptimalShapeCount = 2000,
                MaxOptimalMemoryUsage = 1000,
                MinReadableRowHeight = 22,
                MinRowSpacing = 4
            };
        }
    }

    /// <summary>
    /// 项目分析结果
    /// </summary>
    public class ProjectAnalysis
    {
        /// <summary>
        /// 任务数量
        /// </summary>
        public int TaskCount { get; set; }

        /// <summary>
        /// 里程碑数量
        /// </summary>
        public int MilestoneCount { get; set; }

        /// <summary>
        /// 依赖关系数量
        /// </summary>
        public int DependencyCount { get; set; }

        /// <summary>
        /// 时间跨度（天数）
        /// </summary>
        public int TimeSpan { get; set; }

        /// <summary>
        /// 最大任务深度
        /// </summary>
        public int MaxTaskDepth { get; set; }

        /// <summary>
        /// 任务密度（任务数/天数）
        /// </summary>
        public double TaskDensity { get; set; }

        /// <summary>
        /// 关键路径长度
        /// </summary>
        public int CriticalPathLength { get; set; }

        /// <summary>
        /// 资源利用率
        /// </summary>
        public double ResourceUtilization { get; set; }

        /// <summary>
        /// 复杂度级别
        /// </summary>
        public ComplexityLevel ComplexityLevel { get; set; }

        /// <summary>
        /// 布局挑战列表
        /// </summary>
        public List<LayoutChallenge> LayoutChallenges { get; set; } = new List<LayoutChallenge>();

        /// <summary>
        /// 获取分析摘要
        /// </summary>
        public string GetAnalysisSummary()
        {
            return $"任务: {TaskCount}, 里程碑: {MilestoneCount}, 依赖: {DependencyCount}, " +
                   $"时间跨度: {TimeSpan}天, 复杂度: {ComplexityLevel}, 挑战: {LayoutChallenges.Count}个";
        }
    }

    /// <summary>
    /// 智能布局结果
    /// </summary>
    public class SmartLayoutResult
    {
        /// <summary>
        /// 优化后的布局
        /// </summary>
        public GanttLayout Layout { get; set; }

        /// <summary>
        /// 项目分析结果
        /// </summary>
        public ProjectAnalysis Analysis { get; set; }

        /// <summary>
        /// 优化评分（0-100）
        /// </summary>
        public double OptimizationScore { get; set; }

        /// <summary>
        /// 应用的优化措施
        /// </summary>
        public List<string> AppliedOptimizations { get; set; } = new List<string>();

        /// <summary>
        /// 性能指标
        /// </summary>
        public PerformanceMetrics PerformanceMetrics { get; set; }

        /// <summary>
        /// 布局建议
        /// </summary>
        public List<string> LayoutRecommendations { get; set; } = new List<string>();

        /// <summary>
        /// 是否为最优布局
        /// </summary>
        public bool IsOptimal => OptimizationScore >= 85;

        /// <summary>
        /// 获取结果摘要
        /// </summary>
        public string GetResultSummary()
        {
            return $"优化评分: {OptimizationScore:F1}, 应用优化: {AppliedOptimizations.Count}项, " +
                   $"性能: {PerformanceMetrics.ComplexityScore}分, 建议: {LayoutRecommendations.Count}条";
        }
    }

    /// <summary>
    /// 布局约束
    /// </summary>
    public class LayoutConstraints
    {
        /// <summary>
        /// 最小宽度
        /// </summary>
        public float MinWidth { get; set; } = 400;

        /// <summary>
        /// 最大宽度
        /// </summary>
        public float MaxWidth { get; set; } = 2000;

        /// <summary>
        /// 最小高度
        /// </summary>
        public float MinHeight { get; set; } = 300;

        /// <summary>
        /// 最大高度
        /// </summary>
        public float MaxHeight { get; set; } = 1500;

        /// <summary>
        /// 宽高比约束
        /// </summary>
        public List<AspectRatioConstraint> AspectRatioConstraints { get; set; } = new List<AspectRatioConstraint>();

        /// <summary>
        /// 是否强制适应容器
        /// </summary>
        public bool ForceContainerFit { get; set; } = true;

        /// <summary>
        /// 最小字体大小
        /// </summary>
        public float MinFontSize { get; set; } = 8;

        /// <summary>
        /// 最大字体大小
        /// </summary>
        public float MaxFontSize { get; set; } = 16;

        /// <summary>
        /// 性能优先级
        /// </summary>
        public PerformancePriority PerformancePriority { get; set; } = PerformancePriority.Balanced;

        /// <summary>
        /// 创建默认约束
        /// </summary>
        public static LayoutConstraints CreateDefault() => new LayoutConstraints();

        /// <summary>
        /// 创建幻灯片约束
        /// </summary>
        public static LayoutConstraints CreateForSlide(float slideWidth, float slideHeight)
        {
            return new LayoutConstraints
            {
                MinWidth = slideWidth * 0.3f,
                MaxWidth = slideWidth * 0.95f,
                MinHeight = slideHeight * 0.3f,
                MaxHeight = slideHeight * 0.95f,
                AspectRatioConstraints = new List<AspectRatioConstraint>
                {
                    new AspectRatioConstraint { Min = 0.5f, Max = 3.0f }
                }
            };
        }
    }

    /// <summary>
    /// 宽高比约束
    /// </summary>
    public class AspectRatioConstraint
    {
        /// <summary>
        /// 最小宽高比
        /// </summary>
        public float Min { get; set; }

        /// <summary>
        /// 最大宽高比
        /// </summary>
        public float Max { get; set; }
    }

    /// <summary>
    /// 性能指标
    /// </summary>
    public class PerformanceMetrics
    {
        /// <summary>
        /// 预估渲染时间（毫秒）
        /// </summary>
        public double EstimatedRenderTime { get; set; }

        /// <summary>
        /// 预估内存使用（KB）
        /// </summary>
        public double EstimatedMemoryUsage { get; set; }

        /// <summary>
        /// 复杂度评分（1-10）
        /// </summary>
        public int ComplexityScore { get; set; }

        /// <summary>
        /// 图形元素数量
        /// </summary>
        public int ShapeCount { get; set; }

        /// <summary>
        /// 性能等级
        /// </summary>
        public PerformanceLevel PerformanceLevel
        {
            get
            {
                if (ComplexityScore <= 3) return PerformanceLevel.Excellent;
                if (ComplexityScore <= 5) return PerformanceLevel.Good;
                if (ComplexityScore <= 7) return PerformanceLevel.Fair;
                return PerformanceLevel.Poor;
            }
        }
    }

    /// <summary>
    /// 复杂度级别
    /// </summary>
    public enum ComplexityLevel
    {
        Simple,
        Medium,
        Complex,
        VeryComplex
    }

    /// <summary>
    /// 布局挑战类型
    /// </summary>
    public enum LayoutChallenge
    {
        HighTaskDensity,
        ComplexDependencies,
        DeepHierarchy,
        LongTimeSpan,
        LimitedSpace,
        PerformanceConstraints
    }

    /// <summary>
    /// 性能优先级
    /// </summary>
    public enum PerformancePriority
    {
        Quality,
        Balanced,
        Performance
    }

    /// <summary>
    /// 性能等级
    /// </summary>
    public enum PerformanceLevel
    {
        Excellent,
        Good,
        Fair,
        Poor
    }
}
