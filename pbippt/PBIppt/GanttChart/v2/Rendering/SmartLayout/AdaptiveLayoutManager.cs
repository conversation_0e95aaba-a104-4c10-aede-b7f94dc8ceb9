using System;
using System.Collections.Generic;
using System.Linq;
using PBIppt.GanttChart.Utils;
using PBIppt.GanttChart.v2.Events;

namespace PBIppt.GanttChart.v2.Rendering.SmartLayout
{
    /// <summary>
    /// 自适应布局管理器 - 根据环境动态调整布局
    /// </summary>
    public class AdaptiveLayoutManager
    {
        private readonly EventBusSystem _eventBus;

        /// <summary>
        /// 自适应配置
        /// </summary>
        public AdaptiveConfig Config { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public AdaptiveLayoutManager()
        {
            _eventBus = EventBusSystem.Instance;
            Config = AdaptiveConfig.CreateDefault();
        }

        /// <summary>
        /// 应用自适应调整
        /// </summary>
        /// <param name="layout">输入布局</param>
        /// <param name="constraints">布局约束</param>
        /// <returns>自适应调整后的布局</returns>
        public GanttLayout ApplyAdaptiveAdjustments(GanttLayout layout, LayoutConstraints constraints)
        {
            _eventBus.Publish(new AdaptiveLayoutStarted
            {
                OriginalWidth = (float)layout.TotalWidth,
                OriginalHeight = (float)layout.TotalHeight
            });

            var adapted = layout.Clone();

            // 1. 容器适配
            adapted = AdaptToContainer(adapted, constraints);

            // 2. 分辨率适配
            adapted = AdaptToResolution(adapted, constraints);

            // 3. 性能适配
            adapted = AdaptToPerformance(adapted, constraints);

            // 4. 内容适配
            adapted = AdaptToContent(adapted, constraints);

            // 5. 用户偏好适配
            adapted = AdaptToUserPreferences(adapted, constraints);

            // 6. 验证和修正
            adapted = ValidateAndCorrect(adapted, constraints);

            _eventBus.Publish(new AdaptiveLayoutCompleted
            {
                FinalWidth = (float)adapted.TotalWidth,
                FinalHeight = (float)adapted.TotalHeight,
                AdaptationScore = CalculateAdaptationScore(layout, adapted, constraints)
            });

            return adapted;
        }

        /// <summary>
        /// 适配到容器
        /// </summary>
        private GanttLayout AdaptToContainer(GanttLayout layout, LayoutConstraints constraints)
        {
            var adapted = layout.Clone();

            // 强制适应容器尺寸
            if (constraints.ForceContainerFit)
            {
                // 调整总尺寸
                adapted.TotalWidth = Math.Max(constraints.MinWidth, 
                                            Math.Min(constraints.MaxWidth, adapted.TotalWidth));
                adapted.TotalHeight = Math.Max(constraints.MinHeight, 
                                             Math.Min(constraints.MaxHeight, adapted.TotalHeight));

                // 重新计算子区域
                RecalculateSubAreas(adapted);
            }

            // 宽高比约束
            if (constraints.AspectRatioConstraints.Any())
            {
                var currentRatio = adapted.TotalWidth / adapted.TotalHeight;
                var validConstraint = constraints.AspectRatioConstraints
                    .FirstOrDefault(c => currentRatio >= c.Min && currentRatio <= c.Max);

                if (validConstraint == null)
                {
                    // 调整到最接近的有效比例
                    var targetConstraint = constraints.AspectRatioConstraints
                        .OrderBy(c => Math.Min(Math.Abs(currentRatio - c.Min), Math.Abs(currentRatio - c.Max)))
                        .First();

                    var targetRatio = (targetConstraint.Min + targetConstraint.Max) / 2;
                    
                    if (currentRatio > targetRatio)
                    {
                        adapted.TotalHeight = adapted.TotalWidth / targetRatio;
                    }
                    else
                    {
                        adapted.TotalWidth = adapted.TotalHeight * targetRatio;
                    }

                    RecalculateSubAreas(adapted);
                }
            }

            return adapted;
        }

        /// <summary>
        /// 适配到分辨率
        /// </summary>
        private GanttLayout AdaptToResolution(GanttLayout layout, LayoutConstraints constraints)
        {
            var adapted = layout.Clone();

            // 获取当前显示器信息（简化实现）
            var dpi = GetCurrentDPI();
            var scaleFactor = dpi / 96.0f; // 96 DPI为标准

            if (Math.Abs(scaleFactor - 1.0f) > 0.1f)
            {
                // 调整字体相关尺寸
                adapted.RowHeight *= scaleFactor;
                adapted.TaskBarHeight *= scaleFactor;
                adapted.RowSpacing *= scaleFactor;

                // 调整最小尺寸
                var minFontSize = constraints.MinFontSize * scaleFactor;
                var minRowHeight = minFontSize * 1.5f;
                adapted.RowHeight = Math.Max(adapted.RowHeight, minRowHeight);

                // 重新计算布局
                RecalculateSubAreas(adapted);
            }

            return adapted;
        }

        /// <summary>
        /// 适配到性能要求
        /// </summary>
        private GanttLayout AdaptToPerformance(GanttLayout layout, LayoutConstraints constraints)
        {
            var adapted = layout.Clone();

            switch (constraints.PerformancePriority)
            {
                case PerformancePriority.Performance:
                    // 优化性能，可能牺牲一些视觉效果
                    adapted.RowHeight = Math.Max(Config.MinPerformanceRowHeight, adapted.RowHeight * 0.9f);
                    adapted.RowSpacing = Math.Max(Config.MinPerformanceRowSpacing, adapted.RowSpacing * 0.8f);
                    adapted.DayWidth = Math.Max(Config.MinPerformanceDayWidth, adapted.DayWidth * 0.9f);
                    break;

                case PerformancePriority.Quality:
                    // 优化质量，可能影响性能
                    adapted.RowHeight *= 1.1f;
                    adapted.RowSpacing *= 1.2f;
                    adapted.TaskBarHeight *= 1.1f;
                    break;

                case PerformancePriority.Balanced:
                    // 保持平衡，不做大幅调整
                    break;
            }

            return adapted;
        }

        /// <summary>
        /// 适配到内容
        /// </summary>
        private GanttLayout AdaptToContent(GanttLayout layout, LayoutConstraints constraints)
        {
            var adapted = layout.Clone();

            // 根据内容密度调整
            var contentDensity = EstimateContentDensity(adapted);

            if (contentDensity > Config.HighDensityThreshold)
            {
                // 高密度内容，增加空间
                adapted.RowHeight *= 1.1f;
                adapted.RowSpacing *= 1.2f;
                adapted.LabelColumnWidth *= 1.1f;
            }
            else if (contentDensity < Config.LowDensityThreshold)
            {
                // 低密度内容，压缩空间
                adapted.RowHeight *= 0.9f;
                adapted.RowSpacing *= 0.8f;
            }

            // 重新计算子区域
            RecalculateSubAreas(adapted);

            return adapted;
        }

        /// <summary>
        /// 适配到用户偏好
        /// </summary>
        private GanttLayout AdaptToUserPreferences(GanttLayout layout, LayoutConstraints constraints)
        {
            var adapted = layout.Clone();

            // 应用用户偏好设置（从配置中读取）
            if (Config.UserPreferences.PreferLargerText)
            {
                adapted.RowHeight *= 1.15f;
                adapted.TaskBarHeight *= 1.1f;
            }

            if (Config.UserPreferences.PreferMoreSpacing)
            {
                adapted.RowSpacing *= 1.3f;
            }

            if (Config.UserPreferences.PreferWiderLabels)
            {
                adapted.LabelColumnWidth *= 1.2f;
                RecalculateSubAreas(adapted);
            }

            return adapted;
        }

        /// <summary>
        /// 验证和修正布局
        /// </summary>
        private GanttLayout ValidateAndCorrect(GanttLayout layout, LayoutConstraints constraints)
        {
            var corrected = layout.Clone();

            // 确保最小尺寸
            corrected.RowHeight = Math.Max(Config.AbsoluteMinRowHeight, corrected.RowHeight);
            corrected.RowSpacing = Math.Max(Config.AbsoluteMinRowSpacing, corrected.RowSpacing);
            corrected.TaskBarHeight = Math.Max(Config.AbsoluteMinTaskBarHeight, corrected.TaskBarHeight);
            corrected.DayWidth = Math.Max(Config.AbsoluteMinDayWidth, corrected.DayWidth);

            // 确保最大尺寸
            corrected.RowHeight = Math.Min(Config.AbsoluteMaxRowHeight, corrected.RowHeight);
            corrected.LabelColumnWidth = Math.Min(Config.AbsoluteMaxLabelWidth, corrected.LabelColumnWidth);

            // 确保布局有效性
            if (!corrected.IsValid())
            {
                // 重置为安全的默认值
                corrected = new GanttLayout();
                
                // 应用约束
                corrected.TotalWidth = Math.Max(constraints.MinWidth, 
                                              Math.Min(constraints.MaxWidth, corrected.TotalWidth));
                corrected.TotalHeight = Math.Max(constraints.MinHeight, 
                                               Math.Min(constraints.MaxHeight, corrected.TotalHeight));
                
                RecalculateSubAreas(corrected);
            }

            return corrected;
        }

        /// <summary>
        /// 重新计算子区域
        /// </summary>
        private void RecalculateSubAreas(GanttLayout layout)
        {
            // 重新计算图表区域
            layout.ChartAreaLeft = layout.LabelColumnWidth;
            layout.ChartAreaTop = layout.TimelineHeight;
            layout.ChartAreaWidth = layout.TotalWidth - layout.LabelColumnWidth;
            layout.ChartAreaHeight = layout.TotalHeight - layout.TimelineHeight;

            // 确保图表区域有效
            layout.ChartAreaWidth = Math.Max(100, layout.ChartAreaWidth);
            layout.ChartAreaHeight = Math.Max(100, layout.ChartAreaHeight);
        }

        /// <summary>
        /// 估算内容密度
        /// </summary>
        private double EstimateContentDensity(GanttLayout layout)
        {
            // 简化的内容密度计算
            var rowCount = layout.ChartAreaHeight / (layout.RowHeight + layout.RowSpacing);
            var dayCount = layout.ChartAreaWidth / layout.DayWidth;
            
            return rowCount * dayCount / (layout.ChartAreaWidth * layout.ChartAreaHeight / 10000);
        }

        /// <summary>
        /// 获取当前DPI
        /// </summary>
        private float GetCurrentDPI()
        {
            // 简化实现，实际应该从系统获取
            return 96.0f;
        }

        /// <summary>
        /// 计算适配评分
        /// </summary>
        private double CalculateAdaptationScore(GanttLayout original, GanttLayout adapted, LayoutConstraints constraints)
        {
            double score = 100;

            // 尺寸适配评分
            if (adapted.TotalWidth < constraints.MinWidth || adapted.TotalWidth > constraints.MaxWidth)
                score -= 20;
            if (adapted.TotalHeight < constraints.MinHeight || adapted.TotalHeight > constraints.MaxHeight)
                score -= 20;

            // 比例保持评分
            var originalRatio = original.TotalWidth / original.TotalHeight;
            var adaptedRatio = adapted.TotalWidth / adapted.TotalHeight;
            var ratioChange = Math.Abs(originalRatio - adaptedRatio) / originalRatio;
            if (ratioChange > 0.2)
                score -= 15;

            // 可读性保持评分
            if (adapted.RowHeight < Config.AbsoluteMinRowHeight)
                score -= 25;

            return Math.Max(0, score);
        }
    }

    /// <summary>
    /// 自适应配置
    /// </summary>
    public class AdaptiveConfig
    {
        public double HighDensityThreshold { get; set; } = 0.8;
        public double LowDensityThreshold { get; set; } = 0.3;

        public float AbsoluteMinRowHeight { get; set; } = 12;
        public float AbsoluteMaxRowHeight { get; set; } = 60;
        public float AbsoluteMinRowSpacing { get; set; } = 1;
        public float AbsoluteMinTaskBarHeight { get; set; } = 8;
        public float AbsoluteMinDayWidth { get; set; } = 6;
        public float AbsoluteMaxLabelWidth { get; set; } = 400;

        public float MinPerformanceRowHeight { get; set; } = 14;
        public float MinPerformanceRowSpacing { get; set; } = 2;
        public float MinPerformanceDayWidth { get; set; } = 8;

        public UserPreferences UserPreferences { get; set; } = new UserPreferences();

        public static AdaptiveConfig CreateDefault() => new AdaptiveConfig();
    }

    /// <summary>
    /// 用户偏好设置
    /// </summary>
    public class UserPreferences
    {
        public bool PreferLargerText { get; set; } = false;
        public bool PreferMoreSpacing { get; set; } = false;
        public bool PreferWiderLabels { get; set; } = false;
        public bool PreferCompactLayout { get; set; } = false;
    }
}
