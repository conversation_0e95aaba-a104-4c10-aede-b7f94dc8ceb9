using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using Microsoft.Office.Interop.PowerPoint;
using PBIppt.GanttChart.Models;
using PBIppt.Utils;
using Newtonsoft.Json;

namespace PBIppt.GanttChart.v2.Rendering
{
    /// <summary>
    /// 统一甘特图渲染引擎 v2.0
    /// 创建单一交互式Shape对象，而不是多个分散的Shape
    /// </summary>
    public class UnifiedGanttRenderer
    {
        #region 常量定义
        
        private const float TIMELINE_HEIGHT = 60f;
        private const float TASK_ROW_HEIGHT = 40f;
        private const float TASK_BAR_HEIGHT = 24f;
        private const float MILESTONE_SIZE = 16f;
        private const float MARGIN = 20f;
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 创建统一的甘特图组件
        /// </summary>
        /// <param name="slide">目标幻灯片</param>
        /// <param name="project">项目数据</param>
        /// <param name="bounds">组件边界</param>
        /// <param name="componentId">组件ID</param>
        /// <returns>统一的甘特图Shape</returns>
        public Shape CreateUnifiedGanttChart(Slide slide, GanttProject project, Rectangle bounds, string componentId)
        {
            try
            {
                Logger.Info($"开始创建统一甘特图组件: {componentId}");
                
                // 创建主容器Shape - 使用自由形状以支持自定义绘制
                var containerShape = slide.Shapes.AddShape(
                    Microsoft.Office.Core.MsoAutoShapeType.msoShapeRectangle,
                    bounds.X, bounds.Y, bounds.Width, bounds.Height);
                
                // 设置容器基本属性
                SetupContainerShape(containerShape, componentId);
                
                // 在容器内绘制甘特图内容
                DrawGanttContent(containerShape, project, bounds);
                
                // 设置交互数据
                SetupInteractiveData(containerShape, project, componentId);
                
                Logger.Info($"统一甘特图组件创建完成: {componentId}");
                return containerShape;
            }
            catch (Exception ex)
            {
                Logger.Error($"创建统一甘特图组件失败: {ex.Message}");
                throw;
            }
        }
        
        #endregion
        
        #region 私有方法 - 容器设置
        
        /// <summary>
        /// 设置容器Shape的基本属性
        /// </summary>
        private void SetupContainerShape(Shape containerShape, string componentId)
        {
            // 设置形状名称和标识
            containerShape.Name = $"UnifiedGanttChart_{componentId}";
            
            // 设置边框和填充
            containerShape.Line.Visible = Microsoft.Office.Core.MsoTriState.msoTrue;
            containerShape.Line.ForeColor.RGB = System.Drawing.ColorTranslator.ToOle(Color.FromArgb(200, 200, 200));
            containerShape.Line.Weight = 1.0f;

            containerShape.Fill.Visible = Microsoft.Office.Core.MsoTriState.msoTrue;
            containerShape.Fill.ForeColor.RGB = System.Drawing.ColorTranslator.ToOle(Color.White);
            
            // 设置阴影效果
            containerShape.Shadow.Visible = Microsoft.Office.Core.MsoTriState.msoTrue;
            containerShape.Shadow.Style = Microsoft.Office.Core.MsoShadowStyle.msoShadowStyleOuterShadow;
            containerShape.Shadow.OffsetX = 2;
            containerShape.Shadow.OffsetY = 2;
            containerShape.Shadow.ForeColor.RGB = System.Drawing.ColorTranslator.ToOle(Color.FromArgb(100, 100, 100));
            
            Logger.Debug($"容器Shape设置完成: {containerShape.Name}");
        }
        
        /// <summary>
        /// 设置交互数据
        /// </summary>
        private void SetupInteractiveData(Shape containerShape, GanttProject project, string componentId)
        {
            // 添加组件标识标签
            containerShape.Tags.Add("ComponentType", "UnifiedGanttChart");
            containerShape.Tags.Add("ComponentId", componentId);
            containerShape.Tags.Add("Version", "2.0");
            containerShape.Tags.Add("IsInteractive", "true");
            
            // 序列化项目数据到标签（用于交互时数据恢复）
            var projectJson = JsonConvert.SerializeObject(project, Formatting.None);
            containerShape.Tags.Add("ProjectData", projectJson);

            // 添加交互区域映射数据
            var interactionMap = CreateInteractionMap(project);
            var mapJson = JsonConvert.SerializeObject(interactionMap);
            containerShape.Tags.Add("InteractionMap", mapJson);
            
            Logger.Debug($"交互数据设置完成: {componentId}");
        }
        
        #endregion
        
        #region 私有方法 - 内容绘制
        
        /// <summary>
        /// 在容器内绘制甘特图内容
        /// </summary>
        private void DrawGanttContent(Shape containerShape, GanttProject project, Rectangle bounds)
        {
            try
            {
                // 计算布局参数
                var layout = CalculateLayout(project, bounds);
                
                // 使用文本框方式在容器内添加甘特图可视化内容
                // 注意：这是一个简化实现，实际应该使用更复杂的绘制方法
                
                var textFrame = containerShape.TextFrame;
                var ganttText = GenerateGanttVisualization(project, layout);
                textFrame.TextRange.Text = ganttText;

                // 设置文本格式 - 增大字体确保可见
                textFrame.TextRange.Font.Name = "Arial";
                textFrame.TextRange.Font.Size = 12;
                textFrame.TextRange.Font.Color.RGB = System.Drawing.ColorTranslator.ToOle(Color.Black);

                // 设置边距
                textFrame.MarginLeft = MARGIN;
                textFrame.MarginTop = MARGIN;
                textFrame.MarginRight = MARGIN;
                textFrame.MarginBottom = MARGIN;

                Logger.Debug($"甘特图文本内容: {ganttText.Substring(0, Math.Min(100, ganttText.Length))}...");
                
                Logger.Debug("甘特图内容绘制完成");
            }
            catch (Exception ex)
            {
                Logger.Error($"绘制甘特图内容失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 计算布局参数
        /// </summary>
        private UnifiedGanttLayout CalculateLayout(GanttProject project, Rectangle bounds)
        {
            var layout = new UnifiedGanttLayout
            {
                TotalWidth = bounds.Width - 2 * MARGIN,
                TotalHeight = bounds.Height - 2 * MARGIN,
                TimelineHeight = TIMELINE_HEIGHT,
                TaskRowHeight = TASK_ROW_HEIGHT,
                StartDate = project.ProjectStartDate,
                EndDate = project.ProjectEndDate
            };

            // 计算时间轴相关参数
            var totalDays = (project.ProjectEndDate - project.ProjectStartDate).Days;
            layout.PixelsPerDay = layout.TotalWidth / totalDays;
            
            return layout;
        }
        
        /// <summary>
        /// 生成甘特图的文本可视化（临时实现）
        /// </summary>
        private string GenerateGanttVisualization(GanttProject project, UnifiedGanttLayout layout)
        {
            var lines = new List<string>();
            
            // 添加项目标题
            lines.Add($"=== {project.Name} ===");
            lines.Add($"时间范围: {project.ProjectStartDate:yyyy-MM-dd} ~ {project.ProjectEndDate:yyyy-MM-dd}");
            lines.Add($"任务数量: {project.Tasks?.Count ?? 0}");
            lines.Add($"里程碑: {project.Milestones?.Count ?? 0}");
            lines.Add("");
            
            // 添加时间轴
            lines.Add("--- 时间轴 ---");
            var timelineBar = GenerateTimelineBar(project.ProjectStartDate, project.ProjectEndDate, 40);
            lines.Add(timelineBar);
            lines.Add("");

            // 添加任务列表
            lines.Add("--- 任务列表 ---");
            if (project.Tasks != null && project.Tasks.Any())
            {
                foreach (var task in project.Tasks)
                {
                    var taskBar = GenerateTaskBar(task, project.ProjectStartDate, project.ProjectEndDate, 40);
                    lines.Add($"{task.Name}");
                    lines.Add($"  {task.StartDate:MM-dd} ~ {task.EndDate:MM-dd} [{task.Progress:F0}%]");
                    lines.Add($"  {taskBar}");
                    lines.Add("");
                }
            }
            else
            {
                lines.Add("  (无任务)");
            }
            
            // 添加里程碑
            if (project.Milestones != null && project.Milestones.Any())
            {
                lines.Add("--- 里程碑 ---");
                foreach (var milestone in project.Milestones)
                {
                    lines.Add($"* {milestone.Name} ({milestone.Date:MM-dd})");
                }
            }
            else
            {
                lines.Add("--- 里程碑 ---");
                lines.Add("  (无里程碑)");
            }
            
            return string.Join("\n", lines);
        }
        
        /// <summary>
        /// 生成时间轴条
        /// </summary>
        private string GenerateTimelineBar(DateTime startDate, DateTime endDate, int width)
        {
            var totalDays = (endDate - startDate).Days;
            var bar = new char[width];
            
            for (int i = 0; i < width; i++)
            {
                bar[i] = '─';
            }
            
            return $"├{new string(bar)}┤";
        }
        
        /// <summary>
        /// 生成任务条
        /// </summary>
        private string GenerateTaskBar(GanttTask task, DateTime projectStart, DateTime projectEnd, int width)
        {

            var totalDays = Math.Max(1, (projectEnd - projectStart).Days);
            if (totalDays <= 0) return new string(' ', width);

            var taskStartDays = Math.Max(0, (task.StartDate - projectStart).Days);
            var taskDurationDays = Math.Max(1, (task.EndDate - task.StartDate).Days);

            var startPos = Math.Max(0, (int)(taskStartDays * width / totalDays));
            var taskWidth = Math.Max(1, (int)(taskDurationDays * width / totalDays));

            // 确保不超出边界
            startPos = Math.Min(startPos, width - 1);
            taskWidth = Math.Min(taskWidth, width - startPos);

            var bar = new char[width];
            for (int i = 0; i < width; i++)
            {
                if (i >= startPos && i < startPos + taskWidth)
                {
                    // 根据进度显示不同字符
                    if (task.Progress >= 100)
                        bar[i] = '█'; // 已完成
                    else if (task.Progress > 0)
                        bar[i] = '▓'; // 进行中
                    else
                        bar[i] = '░'; // 未开始
                }
                else
                {
                    bar[i] = '·';
                }
            }

            return $"|{new string(bar)}|";
        }
        
        #endregion
        
        #region 私有方法 - 交互映射
        
        /// <summary>
        /// 创建交互区域映射
        /// </summary>
        private InteractionMap CreateInteractionMap(GanttProject project)
        {
            var map = new InteractionMap
            {
                TaskAreas = new List<TaskArea>(),
                MilestoneAreas = new List<MilestoneArea>(),
                DependencyAreas = new List<DependencyArea>()
            };
            
            // 为每个任务创建交互区域
            float currentY = TIMELINE_HEIGHT + MARGIN;
            foreach (var task in project.Tasks)
            {
                map.TaskAreas.Add(new TaskArea
                {
                    TaskId = task.Id,
                    Bounds = new RectangleF(MARGIN, currentY, 200, TASK_ROW_HEIGHT),
                    TaskData = task
                });
                currentY += TASK_ROW_HEIGHT;
            }
            
            // 为里程碑创建交互区域
            foreach (var milestone in project.Milestones)
            {
                map.MilestoneAreas.Add(new MilestoneArea
                {
                    MilestoneId = milestone.Id,
                    Position = new PointF(100, 100), // 临时位置
                    MilestoneData = milestone
                });
            }
            
            return map;
        }
        
        #endregion
    }
    
    #region 辅助类定义
    
    /// <summary>
    /// 统一甘特图布局参数
    /// </summary>
    public class UnifiedGanttLayout
    {
        public float TotalWidth { get; set; }
        public float TotalHeight { get; set; }
        public float TimelineHeight { get; set; }
        public float TaskRowHeight { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public float PixelsPerDay { get; set; }
    }
    
    /// <summary>
    /// 交互区域映射
    /// </summary>
    public class InteractionMap
    {
        public List<TaskArea> TaskAreas { get; set; }
        public List<MilestoneArea> MilestoneAreas { get; set; }
        public List<DependencyArea> DependencyAreas { get; set; }
    }
    
    /// <summary>
    /// 任务交互区域
    /// </summary>
    public class TaskArea
    {
        public string TaskId { get; set; }
        public RectangleF Bounds { get; set; }
        public GanttTask TaskData { get; set; }
    }
    
    /// <summary>
    /// 里程碑交互区域
    /// </summary>
    public class MilestoneArea
    {
        public string MilestoneId { get; set; }
        public PointF Position { get; set; }
        public GanttMilestone MilestoneData { get; set; }
    }
    
    /// <summary>
    /// 依赖关系交互区域
    /// </summary>
    public class DependencyArea
    {
        public string DependencyId { get; set; }
        public PointF[] Path { get; set; }
        public TaskDependency DependencyData { get; set; }
    }
    
    #endregion
}
