using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PBIppt.GanttChart.Models;
using PBIppt.GanttChart.v2.Components;
using PBIppt.GanttChart.v2.Events;
using PBIppt.GanttChart.v2.Rendering.SmartLayout;
using PBIppt.GanttChart.v2.Integration;
using PBIppt.GanttChart.v2.Tests;

namespace PBIppt.GanttChart.v2.Examples
{
    /// <summary>
    /// 甘特图v2.0功能演示
    /// 展示新版本的核心功能和使用方法
    /// </summary>
    public class GanttV2Demo
    {
        private readonly EventBusSystem _eventBus;

        /// <summary>
        /// 构造函数
        /// </summary>
        public GanttV2Demo()
        {
            _eventBus = EventBusSystem.Instance;
            
            // 订阅演示相关事件
            SubscribeToEvents();
        }

        /// <summary>
        /// 运行完整演示
        /// </summary>
        public async Task RunFullDemoAsync()
        {
            Console.WriteLine("🎯 甘特图v2.0功能演示");
            Console.WriteLine("===================");

            try
            {
                // 1. 智能布局演示
                await DemoSmartLayoutAsync();

                // 2. 组件系统演示
                await DemoComponentSystemAsync();

                // 3. 事件系统演示
                await DemoEventSystemAsync();

                // 4. 交互功能演示
                await DemoInteractionFeaturesAsync();

                // 5. PowerPoint集成演示
                await DemoPowerPointIntegrationAsync();

                // 6. 测试框架演示
                await DemoTestFrameworkAsync();

                Console.WriteLine("\n🎉 演示完成！甘特图v2.0功能展示结束。");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ 演示过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 智能布局演示
        /// </summary>
        private async Task DemoSmartLayoutAsync()
        {
            Console.WriteLine("\n📐 智能布局算法演示");
            Console.WriteLine("-------------------");

            // 创建测试项目
            var project = CreateDemoProject();
            
            // 创建智能布局算法
            var smartLayout = new SmartLayoutAlgorithm();
            
            // 创建布局约束
            var constraints = LayoutConstraints.CreateForSlide(800, 600);
            
            Console.WriteLine("🔍 分析项目特征...");
            
            // 计算智能布局
            var layoutResult = smartLayout.CalculateOptimalLayout(project, constraints);
            
            Console.WriteLine($"✅ 布局计算完成！");
            Console.WriteLine($"   优化评分: {layoutResult.OptimizationScore:F1}/100");
            Console.WriteLine($"   应用优化: {layoutResult.AppliedOptimizations.Count}项");
            Console.WriteLine($"   布局建议: {layoutResult.LayoutRecommendations.Count}条");
            Console.WriteLine($"   性能评级: {layoutResult.PerformanceMetrics.PerformanceLevel}");

            // 演示不同布局配置
            await DemoDifferentLayoutConfigsAsync(project);
        }

        /// <summary>
        /// 组件系统演示
        /// </summary>
        private async Task DemoComponentSystemAsync()
        {
            Console.WriteLine("\n🧩 组件系统演示");
            Console.WriteLine("---------------");

            // 创建甘特图组件
            var component = new GanttChartComponent();
            var project = CreateDemoProject();

            Console.WriteLine("🔧 初始化组件...");
            
            // 模拟组件初始化（实际需要PowerPoint幻灯片）
            // component.Initialize(slide, project);
            
            Console.WriteLine("📊 组件状态管理演示:");
            var state = component.GetState();
            Console.WriteLine($"   当前状态: {state}");
            Console.WriteLine($"   可撤销: {component.CanUndo}");
            Console.WriteLine($"   可重做: {component.CanRedo}");

            // 演示组件功能
            Console.WriteLine("\n🎮 组件功能演示:");
            Console.WriteLine("   ✅ 生命周期管理");
            Console.WriteLine("   ✅ 状态管理");
            Console.WriteLine("   ✅ 事件处理");
            Console.WriteLine("   ✅ 交互支持");

            await Task.Delay(500); // 模拟处理时间
        }

        /// <summary>
        /// 事件系统演示
        /// </summary>
        private async Task DemoEventSystemAsync()
        {
            Console.WriteLine("\n📡 事件系统演示");
            Console.WriteLine("---------------");

            Console.WriteLine("🔔 发布测试事件...");

            // 发布各种事件
            _eventBus.Publish(new ComponentStateChanged("demo", ComponentState.Active, ComponentState.Inactive));
            _eventBus.Publish(new LayoutCalculationStarted { ProjectId = "demo-project" });
            _eventBus.Publish(new TaskSelectedEvent("task-1", true));

            Console.WriteLine("✅ 事件发布完成");
            Console.WriteLine("📊 事件系统特性:");
            Console.WriteLine("   ✅ 类型安全的事件");
            Console.WriteLine("   ✅ 异步事件处理");
            Console.WriteLine("   ✅ 松耦合通信");
            Console.WriteLine("   ✅ 错误处理机制");

            await Task.Delay(300);
        }

        /// <summary>
        /// 交互功能演示
        /// </summary>
        private async Task DemoInteractionFeaturesAsync()
        {
            Console.WriteLine("\n🖱️ 交互功能演示");
            Console.WriteLine("----------------");

            Console.WriteLine("🎯 高级交互功能:");
            Console.WriteLine("   ✅ 智能拖拽系统");
            Console.WriteLine("   ✅ 右键菜单框架");
            Console.WriteLine("   ✅ 键盘快捷键 (15个)");
            Console.WriteLine("   ✅ 直接编辑功能");
            Console.WriteLine("   ✅ 多选和框选");
            Console.WriteLine("   ✅ 视觉效果系统");

            Console.WriteLine("\n🎨 视觉效果演示:");
            Console.WriteLine("   ✨ 选择高亮效果");
            Console.WriteLine("   ✨ 拖拽预览效果");
            Console.WriteLine("   ✨ 动画过渡效果");
            Console.WriteLine("   ✨ 状态反馈效果");

            await Task.Delay(400);
        }

        /// <summary>
        /// PowerPoint集成演示
        /// </summary>
        private async Task DemoPowerPointIntegrationAsync()
        {
            Console.WriteLine("\n🔗 PowerPoint集成演示");
            Console.WriteLine("---------------------");

            var integration = new PowerPointIntegration();
            
            Console.WriteLine("🔧 PowerPoint集成功能:");
            Console.WriteLine("   ✅ 智能组件创建");
            Console.WriteLine("   ✅ 幻灯片环境分析");
            Console.WriteLine("   ✅ 主题自动适配");
            Console.WriteLine("   ✅ 事件集成");
            Console.WriteLine("   ✅ 生命周期管理");

            Console.WriteLine("\n📊 集成特性:");
            Console.WriteLine("   🎯 ThinkCell级别体验");
            Console.WriteLine("   🔄 实时环境适配");
            Console.WriteLine("   ⚡ 原生性能优化");
            Console.WriteLine("   🎨 无缝用户体验");

            await Task.Delay(300);
        }

        /// <summary>
        /// 测试框架演示
        /// </summary>
        private async Task DemoTestFrameworkAsync()
        {
            Console.WriteLine("\n🧪 测试框架演示");
            Console.WriteLine("---------------");

            Console.WriteLine("🚀 运行快速测试演示...");
            
            var testRunner = new TestRunner();
            
            // 验证测试环境
            var isValid = testRunner.ValidateTestEnvironment();
            Console.WriteLine($"🔍 测试环境验证: {(isValid ? "✅ 通过" : "❌ 失败")}");

            if (isValid)
            {
                Console.WriteLine("\n📊 测试框架功能:");
                Console.WriteLine("   ✅ 5种测试类型");
                Console.WriteLine("   ✅ 多种测试配置");
                Console.WriteLine("   ✅ 自动化测试执行");
                Console.WriteLine("   ✅ 详细测试报告");
                Console.WriteLine("   ✅ 性能基准测试");
                Console.WriteLine("   ✅ HTML报告生成");

                // 运行一个简化的测试演示
                await RunSimplifiedTestDemo();
            }
        }

        /// <summary>
        /// 演示不同布局配置
        /// </summary>
        private async Task DemoDifferentLayoutConfigsAsync(GanttProject project)
        {
            Console.WriteLine("\n🎨 不同布局配置演示:");

            var smartLayout = new SmartLayoutAlgorithm();

            // 高性能配置
            smartLayout.Config = SmartLayoutConfig.CreateHighPerformance();
            var perfResult = smartLayout.CalculateOptimalLayout(project, new LayoutConstraints());
            Console.WriteLine($"   ⚡ 高性能配置: {perfResult.OptimizationScore:F1}分");

            // 高质量配置
            smartLayout.Config = SmartLayoutConfig.CreateHighQuality();
            var qualityResult = smartLayout.CalculateOptimalLayout(project, new LayoutConstraints());
            Console.WriteLine($"   🎨 高质量配置: {qualityResult.OptimizationScore:F1}分");

            await Task.Delay(200);
        }

        /// <summary>
        /// 运行简化测试演示
        /// </summary>
        private async Task RunSimplifiedTestDemo()
        {
            Console.WriteLine("\n🔬 运行测试演示...");

            var config = TestConfig.CreateQuick();
            var framework = new GanttV2TestFramework(config);

            // 模拟测试执行
            await Task.Delay(1000);

            Console.WriteLine("✅ 测试演示完成");
            Console.WriteLine("📊 模拟测试结果:");
            Console.WriteLine("   • 组件测试: 5/5 通过 ✅");
            Console.WriteLine("   • 布局测试: 5/5 通过 ✅");
            Console.WriteLine("   • 交互测试: 7/7 通过 ✅");
            Console.WriteLine("   • 总成功率: 100% 🎉");
        }

        /// <summary>
        /// 创建演示项目
        /// </summary>
        private GanttProject CreateDemoProject()
        {
            return new GanttProject
            {
                Id = "demo-project",
                Name = "v2.0功能演示项目",
                Description = "展示甘特图v2.0的各项新功能",
                Tasks = CreateDemoTasks(),
                Milestones = CreateDemoMilestones(),
                Dependencies = CreateDemoDependencies(),
                TimelineSettings = new TimelineSettings
                {
                    StartDate = DateTime.Today,
                    EndDate = DateTime.Today.AddDays(60),
                    ShowTodayLine = true,
                    ShadeWeekends = true
                }
            };
        }

        /// <summary>
        /// 创建演示任务
        /// </summary>
        private List<GanttTask> CreateDemoTasks()
        {
            return new List<GanttTask>
            {
                new GanttTask
                {
                    Id = "task-1",
                    Name = "需求分析",
                    StartDate = DateTime.Today,
                    EndDate = DateTime.Today.AddDays(7),
                    Progress = 100,
                    Level = 0,
                    Color = "#3498db"
                },
                new GanttTask
                {
                    Id = "task-2",
                    Name = "架构设计",
                    StartDate = DateTime.Today.AddDays(5),
                    EndDate = DateTime.Today.AddDays(14),
                    Progress = 80,
                    Level = 0,
                    Color = "#e74c3c"
                },
                new GanttTask
                {
                    Id = "task-3",
                    Name = "核心开发",
                    StartDate = DateTime.Today.AddDays(12),
                    EndDate = DateTime.Today.AddDays(35),
                    Progress = 60,
                    Level = 0,
                    Color = "#2ecc71"
                },
                new GanttTask
                {
                    Id = "task-4",
                    Name = "测试验证",
                    StartDate = DateTime.Today.AddDays(30),
                    EndDate = DateTime.Today.AddDays(45),
                    Progress = 20,
                    Level = 0,
                    Color = "#f39c12"
                },
                new GanttTask
                {
                    Id = "task-5",
                    Name = "部署上线",
                    StartDate = DateTime.Today.AddDays(42),
                    EndDate = DateTime.Today.AddDays(50),
                    Progress = 0,
                    Level = 0,
                    Color = "#9b59b6"
                }
            };
        }

        /// <summary>
        /// 创建演示里程碑
        /// </summary>
        private List<GanttMilestone> CreateDemoMilestones()
        {
            return new List<GanttMilestone>
            {
                new GanttMilestone
                {
                    Id = "milestone-1",
                    Name = "需求确认",
                    Date = DateTime.Today.AddDays(7),
                    Color = "#e67e22"
                },
                new GanttMilestone
                {
                    Id = "milestone-2",
                    Name = "设计评审",
                    Date = DateTime.Today.AddDays(14),
                    Color = "#e67e22"
                },
                new GanttMilestone
                {
                    Id = "milestone-3",
                    Name = "功能完成",
                    Date = DateTime.Today.AddDays(35),
                    Color = "#e67e22"
                },
                new GanttMilestone
                {
                    Id = "milestone-4",
                    Name = "项目交付",
                    Date = DateTime.Today.AddDays(50),
                    Color = "#e67e22"
                }
            };
        }

        /// <summary>
        /// 创建演示依赖关系
        /// </summary>
        private List<TaskDependency> CreateDemoDependencies()
        {
            return new List<TaskDependency>
            {
                new TaskDependency { PredecessorId = "task-1", SuccessorId = "task-2", Type = DependencyType.FinishToStart },
                new TaskDependency { PredecessorId = "task-2", SuccessorId = "task-3", Type = DependencyType.FinishToStart },
                new TaskDependency { PredecessorId = "task-3", SuccessorId = "task-4", Type = DependencyType.FinishToStart },
                new TaskDependency { PredecessorId = "task-4", SuccessorId = "task-5", Type = DependencyType.FinishToStart }
            };
        }

        /// <summary>
        /// 订阅演示事件
        /// </summary>
        private void SubscribeToEvents()
        {
            _eventBus.Subscribe<ComponentStateChangedEvent>(OnComponentStateChanged);
            _eventBus.Subscribe<LayoutCalculationStarted>(OnLayoutCalculationStarted);
            _eventBus.Subscribe<SelectionChangedEvent>(OnTaskSelected);
        }

        /// <summary>
        /// 处理组件状态变化事件
        /// </summary>
        private void OnComponentStateChanged(ComponentStateChangedEvent e)
        {
            Console.WriteLine($"📡 事件: 组件状态变化 {e.OldState} → {e.NewState}");
        }

        /// <summary>
        /// 处理布局计算开始事件
        /// </summary>
        private void OnLayoutCalculationStarted(LayoutCalculationStarted e)
        {
            Console.WriteLine($"📡 事件: 布局计算开始 (项目: {e.ProjectId})");
        }

        /// <summary>
        /// 处理任务选择事件
        /// </summary>
        private void OnTaskSelected(SelectionChangedEvent e)
        {
            Console.WriteLine($"📡 事件: 选择变化 (多选: {e.IsMultiSelect})");
        }

        /// <summary>
        /// 运行交互式演示
        /// </summary>
        public async Task RunInteractiveDemoAsync()
        {
            Console.WriteLine("🎮 甘特图v2.0交互式演示");
            Console.WriteLine("========================");

            while (true)
            {
                Console.WriteLine("\n请选择演示内容:");
                Console.WriteLine("1. 智能布局算法");
                Console.WriteLine("2. 组件系统");
                Console.WriteLine("3. 事件系统");
                Console.WriteLine("4. 交互功能");
                Console.WriteLine("5. PowerPoint集成");
                Console.WriteLine("6. 测试框架");
                Console.WriteLine("7. 完整演示");
                Console.WriteLine("0. 退出");
                Console.Write("\n请输入选择 (0-7): ");

                var choice = Console.ReadLine();

                switch (choice)
                {
                    case "1":
                        await DemoSmartLayoutAsync();
                        break;
                    case "2":
                        await DemoComponentSystemAsync();
                        break;
                    case "3":
                        await DemoEventSystemAsync();
                        break;
                    case "4":
                        await DemoInteractionFeaturesAsync();
                        break;
                    case "5":
                        await DemoPowerPointIntegrationAsync();
                        break;
                    case "6":
                        await DemoTestFrameworkAsync();
                        break;
                    case "7":
                        await RunFullDemoAsync();
                        break;
                    case "0":
                        Console.WriteLine("👋 演示结束，感谢体验甘特图v2.0！");
                        return;
                    default:
                        Console.WriteLine("❌ 无效选择，请重新输入");
                        continue;
                }

                Console.WriteLine("\n按任意键继续...");
                Console.ReadKey();
            }
        }
    }
}
