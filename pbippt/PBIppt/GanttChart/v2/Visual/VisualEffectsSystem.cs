using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using Microsoft.Office.Interop.PowerPoint;
using PBIppt.GanttChart.v2.Components;
using PBIppt.GanttChart.v2.Events;
using PBIppt.Utils;
using DrawingPoint = System.Drawing.Point;
using PowerPointPoint = Microsoft.Office.Interop.PowerPoint.Point;

namespace PBIppt.GanttChart.v2.Visual
{
    /// <summary>
    /// 视觉效果系统
    /// 提供选择高亮、焦点指示、状态反馈等视觉效果
    /// </summary>
    public class VisualEffectsSystem : IDisposable
    {
        #region 私有字段
        
        private readonly string _componentId;
        private readonly IGanttChartComponent _component;
        private readonly Dictionary<object, VisualEffect> _activeEffects;
        private readonly EffectRenderer _effectRenderer;
        private readonly AnimationManager _animationManager;
        private bool _disposed = false;
        
        #endregion
        
        #region 属性
        
        /// <summary>
        /// 组件ID
        /// </summary>
        public string ComponentId => _componentId;
        
        /// <summary>
        /// 活动效果数量
        /// </summary>
        public int ActiveEffectCount => _activeEffects.Count;
        
        /// <summary>
        /// 效果渲染器
        /// </summary>
        public EffectRenderer EffectRenderer => _effectRenderer;
        
        /// <summary>
        /// 动画管理器
        /// </summary>
        public AnimationManager AnimationManager => _animationManager;
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 效果应用事件
        /// </summary>
        public event EventHandler<EffectAppliedEventArgs> EffectApplied;
        
        /// <summary>
        /// 效果移除事件
        /// </summary>
        public event EventHandler<EffectRemovedEventArgs> EffectRemoved;
        
        /// <summary>
        /// 动画完成事件
        /// </summary>
        public event EventHandler<AnimationCompletedEventArgs> AnimationCompleted;
        
        #endregion
        
        #region 构造函数
        
        /// <summary>
        /// 初始化视觉效果系统
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <param name="component">甘特图组件</param>
        public VisualEffectsSystem(string componentId, IGanttChartComponent component)
        {
            _componentId = componentId ?? throw new ArgumentNullException(nameof(componentId));
            _component = component ?? throw new ArgumentNullException(nameof(component));
            
            _activeEffects = new Dictionary<object, VisualEffect>();
            _effectRenderer = new EffectRenderer();
            _animationManager = new AnimationManager();
            
            // 订阅事件
            SubscribeToEvents();
            
            Logger.Info($"视觉效果系统已初始化: {componentId}");
        }
        
        #endregion
        
        #region 选择效果
        
        /// <summary>
        /// 应用选择高亮效果
        /// </summary>
        /// <param name="targets">目标对象</param>
        public void ApplySelectionHighlight(params object[] targets)
        {
            if (targets == null || targets.Length == 0) return;
            
            try
            {
                foreach (var target in targets)
                {
                    var effect = new SelectionHighlightEffect
                    {
                        Target = target,
                        Color = Color.FromArgb(100, Color.Blue),
                        BorderColor = Color.Blue,
                        BorderWidth = 2,
                        IsActive = true
                    };
                    
                    ApplyEffect(target, effect);
                }
                
                Logger.Debug($"应用选择高亮: {targets.Length} 个对象");
            }
            catch (Exception ex)
            {
                Logger.Error($"应用选择高亮失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 移除选择高亮效果
        /// </summary>
        /// <param name="targets">目标对象</param>
        public void RemoveSelectionHighlight(params object[] targets)
        {
            if (targets == null || targets.Length == 0) return;
            
            try
            {
                foreach (var target in targets)
                {
                    RemoveEffect(target, EffectType.SelectionHighlight);
                }
                
                Logger.Debug($"移除选择高亮: {targets.Length} 个对象");
            }
            catch (Exception ex)
            {
                Logger.Error($"移除选择高亮失败: {ex.Message}");
            }
        }
        
        #endregion
        
        #region 焦点效果
        
        /// <summary>
        /// 应用焦点指示效果
        /// </summary>
        /// <param name="target">目标对象</param>
        public void ApplyFocusIndicator(object target)
        {
            if (target == null) return;
            
            try
            {
                // 先移除其他对象的焦点效果
                RemoveAllEffects(EffectType.FocusIndicator);
                
                var effect = new FocusIndicatorEffect
                {
                    Target = target,
                    Color = Color.FromArgb(150, Color.Orange),
                    BorderColor = Color.Orange,
                    BorderWidth = 3,
                    IsActive = true,
                    IsAnimated = true
                };
                
                ApplyEffect(target, effect);
                
                // 启动闪烁动画
                _animationManager.StartBlinkAnimation(target, 1000, 3);
                
                Logger.Debug($"应用焦点指示: {target.GetType().Name}");
            }
            catch (Exception ex)
            {
                Logger.Error($"应用焦点指示失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 移除焦点指示效果
        /// </summary>
        /// <param name="target">目标对象</param>
        public void RemoveFocusIndicator(object target = null)
        {
            try
            {
                if (target != null)
                {
                    RemoveEffect(target, EffectType.FocusIndicator);
                    _animationManager.StopAnimation(target);
                }
                else
                {
                    RemoveAllEffects(EffectType.FocusIndicator);
                    _animationManager.StopAllAnimations();
                }
                
                Logger.Debug($"移除焦点指示: {target?.GetType().Name ?? "全部"}");
            }
            catch (Exception ex)
            {
                Logger.Error($"移除焦点指示失败: {ex.Message}");
            }
        }
        
        #endregion
        
        #region 编辑状态效果
        
        /// <summary>
        /// 应用编辑状态指示
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="editType">编辑类型</param>
        public void ApplyEditingIndicator(object target, EditType editType)
        {
            if (target == null) return;
            
            try
            {
                var effect = new EditingIndicatorEffect
                {
                    Target = target,
                    EditType = editType,
                    Color = Color.FromArgb(120, Color.Green),
                    BorderColor = Color.Green,
                    BorderWidth = 2,
                    IsActive = true,
                    ShowEditIcon = true
                };
                
                ApplyEffect(target, effect);
                
                Logger.Debug($"应用编辑指示: {target.GetType().Name}, 类型: {editType}");
            }
            catch (Exception ex)
            {
                Logger.Error($"应用编辑指示失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 移除编辑状态指示
        /// </summary>
        /// <param name="target">目标对象</param>
        public void RemoveEditingIndicator(object target)
        {
            if (target == null) return;
            
            try
            {
                RemoveEffect(target, EffectType.EditingIndicator);
                
                Logger.Debug($"移除编辑指示: {target.GetType().Name}");
            }
            catch (Exception ex)
            {
                Logger.Error($"移除编辑指示失败: {ex.Message}");
            }
        }
        
        #endregion
        
        #region 拖拽效果
        
        /// <summary>
        /// 应用拖拽预览效果
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="previewLocation">预览位置</param>
        public void ApplyDragPreview(object target, DrawingPoint previewLocation)
        {
            if (target == null) return;
            
            try
            {
                var effect = new DragPreviewEffect
                {
                    Target = target,
                    PreviewLocation = previewLocation,
                    Color = Color.FromArgb(80, Color.Gray),
                    BorderColor = Color.DarkGray,
                    BorderWidth = 1,
                    IsActive = true,
                    Opacity = 0.6f
                };
                
                ApplyEffect(target, effect);
                
                Logger.Debug($"应用拖拽预览: {target.GetType().Name} at ({previewLocation.X}, {previewLocation.Y})");
            }
            catch (Exception ex)
            {
                Logger.Error($"应用拖拽预览失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 更新拖拽预览位置
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="newLocation">新位置</param>
        public void UpdateDragPreview(object target, DrawingPoint newLocation)
        {
            if (target == null || !_activeEffects.ContainsKey(target)) return;
            
            try
            {
                var effect = _activeEffects[target];
                if (effect is DragPreviewEffect dragEffect)
                {
                    dragEffect.PreviewLocation = newLocation;
                    _effectRenderer.UpdateEffect(dragEffect);
                }
                
                Logger.Debug($"更新拖拽预览: {target.GetType().Name} to ({newLocation.X}, {newLocation.Y})");
            }
            catch (Exception ex)
            {
                Logger.Error($"更新拖拽预览失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 移除拖拽预览效果
        /// </summary>
        /// <param name="target">目标对象</param>
        public void RemoveDragPreview(object target)
        {
            if (target == null) return;
            
            try
            {
                RemoveEffect(target, EffectType.DragPreview);
                
                Logger.Debug($"移除拖拽预览: {target.GetType().Name}");
            }
            catch (Exception ex)
            {
                Logger.Error($"移除拖拽预览失败: {ex.Message}");
            }
        }
        
        #endregion
        
        #region 状态反馈效果
        
        /// <summary>
        /// 显示成功反馈
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="message">消息</param>
        public void ShowSuccessFeedback(object target, string message = "操作成功")
        {
            ShowFeedback(target, message, FeedbackType.Success);
        }
        
        /// <summary>
        /// 显示错误反馈
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="message">消息</param>
        public void ShowErrorFeedback(object target, string message = "操作失败")
        {
            ShowFeedback(target, message, FeedbackType.Error);
        }
        
        /// <summary>
        /// 显示警告反馈
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="message">消息</param>
        public void ShowWarningFeedback(object target, string message = "注意")
        {
            ShowFeedback(target, message, FeedbackType.Warning);
        }
        
        /// <summary>
        /// 显示反馈效果
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="message">消息</param>
        /// <param name="feedbackType">反馈类型</param>
        private void ShowFeedback(object target, string message, FeedbackType feedbackType)
        {
            if (target == null) return;
            
            try
            {
                var color = GetFeedbackColor(feedbackType);
                
                var effect = new FeedbackEffect
                {
                    Target = target,
                    Message = message,
                    FeedbackType = feedbackType,
                    Color = Color.FromArgb(120, color),
                    BorderColor = color,
                    BorderWidth = 2,
                    IsActive = true,
                    Duration = 2000 // 2秒后自动消失
                };
                
                ApplyEffect(target, effect);
                
                // 启动淡出动画
                _animationManager.StartFadeOutAnimation(target, effect.Duration);
                
                Logger.Debug($"显示反馈: {feedbackType} - {message}");
            }
            catch (Exception ex)
            {
                Logger.Error($"显示反馈失败: {ex.Message}");
            }
        }
        
        private Color GetFeedbackColor(FeedbackType feedbackType)
        {
            switch (feedbackType)
            {
                case FeedbackType.Success:
                    return Color.Green;
                case FeedbackType.Error:
                    return Color.Red;
                case FeedbackType.Warning:
                    return Color.Orange;
                case FeedbackType.Info:
                    return Color.Blue;
                default:
                    return Color.Gray;
            }
        }
        
        #endregion

        #region 效果管理

        /// <summary>
        /// 应用效果
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="effect">视觉效果</param>
        private void ApplyEffect(object target, VisualEffect effect)
        {
            try
            {
                // 移除同类型的现有效果
                RemoveEffect(target, effect.EffectType);

                // 应用新效果
                _activeEffects[target] = effect;
                _effectRenderer.RenderEffect(effect);

                // 触发效果应用事件
                OnEffectApplied(target, effect);

                // 发布到事件总线
                EventBusSystem.Instance.Publish(new VisualEffectAppliedEvent(
                    target, effect.EffectType, _componentId));
            }
            catch (Exception ex)
            {
                Logger.Error($"应用效果失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 移除效果
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="effectType">效果类型</param>
        private void RemoveEffect(object target, EffectType effectType)
        {
            try
            {
                if (_activeEffects.TryGetValue(target, out var effect) && effect.EffectType == effectType)
                {
                    _activeEffects.Remove(target);
                    _effectRenderer.RemoveEffect(effect);

                    // 触发效果移除事件
                    OnEffectRemoved(target, effect);

                    // 发布到事件总线
                    EventBusSystem.Instance.Publish(new VisualEffectRemovedEvent(
                        target, effectType, _componentId));
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"移除效果失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 移除所有指定类型的效果
        /// </summary>
        /// <param name="effectType">效果类型</param>
        private void RemoveAllEffects(EffectType effectType)
        {
            try
            {
                var targetsToRemove = _activeEffects
                    .Where(kvp => kvp.Value.EffectType == effectType)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var target in targetsToRemove)
                {
                    RemoveEffect(target, effectType);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"移除所有效果失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除所有效果
        /// </summary>
        public void ClearAllEffects()
        {
            try
            {
                var allTargets = _activeEffects.Keys.ToList();

                foreach (var target in allTargets)
                {
                    var effect = _activeEffects[target];
                    _activeEffects.Remove(target);
                    _effectRenderer.RemoveEffect(effect);

                    OnEffectRemoved(target, effect);
                }

                _animationManager.StopAllAnimations();

                Logger.Debug("已清除所有视觉效果");
            }
            catch (Exception ex)
            {
                Logger.Error($"清除所有效果失败: {ex.Message}");
            }
        }

        #endregion

        #region 事件处理

        private void SubscribeToEvents()
        {
            // 订阅相关事件
            EventBusSystem.Instance.Subscribe<SelectionChangedEvent>(OnSelectionChangedEvent, $"{_componentId}_VisualSelectionHandler");
            EventBusSystem.Instance.Subscribe<EditStartEvent>(OnEditStartEvent, $"{_componentId}_VisualEditHandler");
            EventBusSystem.Instance.Subscribe<EditCompleteEvent>(OnEditCompleteEvent, $"{_componentId}_VisualEditCompleteHandler");
            EventBusSystem.Instance.Subscribe<DragStartEvent>(OnDragStartEvent, $"{_componentId}_VisualDragHandler");
            EventBusSystem.Instance.Subscribe<DragCompleteEvent>(OnDragCompleteEvent, $"{_componentId}_VisualDragCompleteHandler");
            EventBusSystem.Instance.Subscribe<ErrorEvent>(OnErrorEvent, $"{_componentId}_VisualErrorHandler");

            // 订阅动画管理器事件
            _animationManager.AnimationCompleted += OnAnimationManagerCompleted;
        }

        private void UnsubscribeFromEvents()
        {
            // 取消订阅事件
            EventBusSystem.Instance.Unsubscribe($"{_componentId}_VisualSelectionHandler");
            EventBusSystem.Instance.Unsubscribe($"{_componentId}_VisualEditHandler");
            EventBusSystem.Instance.Unsubscribe($"{_componentId}_VisualEditCompleteHandler");
            EventBusSystem.Instance.Unsubscribe($"{_componentId}_VisualDragHandler");
            EventBusSystem.Instance.Unsubscribe($"{_componentId}_VisualDragCompleteHandler");
            EventBusSystem.Instance.Unsubscribe($"{_componentId}_VisualErrorHandler");

            // 取消订阅动画管理器事件
            if (_animationManager != null)
            {
                _animationManager.AnimationCompleted -= OnAnimationManagerCompleted;
            }
        }

        private void OnSelectionChangedEvent(SelectionChangedEvent selectionEvent)
        {
            try
            {
                // 移除旧选择的高亮
                if (selectionEvent.OldSelection != null)
                {
                    RemoveSelectionHighlight(selectionEvent.OldSelection);
                }

                // 应用新选择的高亮
                if (selectionEvent.NewSelection != null && selectionEvent.NewSelection.Length > 0)
                {
                    ApplySelectionHighlight(selectionEvent.NewSelection);

                    // 如果只选择了一个对象，应用焦点指示
                    if (selectionEvent.NewSelection.Length == 1)
                    {
                        ApplyFocusIndicator(selectionEvent.NewSelection[0]);
                    }
                    else
                    {
                        RemoveFocusIndicator();
                    }
                }
                else
                {
                    RemoveFocusIndicator();
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"处理选择改变视觉效果失败: {ex.Message}");
            }
        }

        private void OnEditStartEvent(EditStartEvent editEvent)
        {
            try
            {
                ApplyEditingIndicator(editEvent.Target, ConvertEditType(editEvent.EditType));
            }
            catch (Exception ex)
            {
                Logger.Error($"处理编辑开始视觉效果失败: {ex.Message}");
            }
        }

        private void OnEditCompleteEvent(EditCompleteEvent editEvent)
        {
            try
            {
                RemoveEditingIndicator(editEvent.Target);

                if (editEvent.Success)
                {
                    ShowSuccessFeedback(editEvent.Target, "编辑成功");
                }
                else
                {
                    ShowErrorFeedback(editEvent.Target, "编辑失败");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"处理编辑完成视觉效果失败: {ex.Message}");
            }
        }

        private void OnDragStartEvent(DragStartEvent dragEvent)
        {
            try
            {
                ApplyDragPreview(dragEvent.Target, dragEvent.StartPoint);
            }
            catch (Exception ex)
            {
                Logger.Error($"处理拖拽开始视觉效果失败: {ex.Message}");
            }
        }

        private void OnDragCompleteEvent(DragCompleteEvent dragEvent)
        {
            try
            {
                RemoveDragPreview(dragEvent.Target);

                if (dragEvent.Success)
                {
                    ShowSuccessFeedback(dragEvent.Target, "拖拽成功");
                }
                else
                {
                    ShowErrorFeedback(dragEvent.Target, "拖拽失败");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"处理拖拽完成视觉效果失败: {ex.Message}");
            }
        }

        private void OnAnimationManagerCompleted(object sender, AnimationCompletedEventArgs e)
        {
            AnimationCompleted?.Invoke(this, e);
        }

        private void OnEffectApplied(object target, VisualEffect effect)
        {
            EffectApplied?.Invoke(this, new EffectAppliedEventArgs
            {
                Target = target,
                Effect = effect,
                Timestamp = DateTime.Now
            });
        }

        private void OnEffectRemoved(object target, VisualEffect effect)
        {
            EffectRemoved?.Invoke(this, new EffectRemovedEventArgs
            {
                Target = target,
                Effect = effect,
                Timestamp = DateTime.Now
            });
        }

        private void OnErrorEvent(ErrorEvent errorEvent)
        {
            Logger.Error($"视觉效果系统错误: {errorEvent.ErrorMessage}");
        }

        #endregion

        #region IDisposable实现

        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    UnsubscribeFromEvents();
                    ClearAllEffects();
                    _effectRenderer?.Dispose();
                    _animationManager?.Dispose();

                    _disposed = true;
                    Logger.Info($"视觉效果系统已销毁: {_componentId}");
                }
                catch (Exception ex)
                {
                    Logger.Error($"视觉效果系统销毁失败: {ex.Message}");
                }
            }
        }

        #endregion

        #region 类型转换辅助方法

        /// <summary>
        /// 转换编辑类型从Components命名空间到Visual命名空间
        /// </summary>
        /// <param name="componentEditType">组件编辑类型</param>
        /// <returns>视觉编辑类型</returns>
        private EditType ConvertEditType(Components.EditType componentEditType)
        {
            return componentEditType switch
            {
                Components.EditType.Text => EditType.Text,
                Components.EditType.Date => EditType.Date,
                Components.EditType.Number => EditType.Number,
                Components.EditType.Color => EditType.Color,
                Components.EditType.Status => EditType.Status,
                _ => EditType.Text
            };
        }

        #endregion
    }
}
