using System;
using System.Collections.Generic;
using System.Linq;
using PBIppt.GanttChart.v2.Events;
using PBIppt.Utils;

namespace PBIppt.GanttChart.v2.Visual
{
    #region 动画管理器
    
    /// <summary>
    /// 动画管理器
    /// 管理视觉效果的动画
    /// </summary>
    public class AnimationManager : IDisposable
    {
        #region 私有字段
        
        private readonly Dictionary<object, AnimationInfo> _activeAnimations;
        private readonly System.Threading.Timer _animationTimer;
        private bool _disposed = false;
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 动画完成事件
        /// </summary>
        public event EventHandler<AnimationCompletedEventArgs> AnimationCompleted;
        
        #endregion
        
        #region 构造函数
        
        public AnimationManager()
        {
            _activeAnimations = new Dictionary<object, AnimationInfo>();
            _animationTimer = new System.Threading.Timer(OnAnimationTick, null, 50, 50); // 20 FPS
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 开始闪烁动画
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="duration">持续时间（毫秒）</param>
        /// <param name="blinkCount">闪烁次数</param>
        public void StartBlinkAnimation(object target, int duration, int blinkCount = 3)
        {
            if (target == null) return;
            
            try
            {
                var animation = new BlinkAnimation
                {
                    Target = target,
                    Duration = duration,
                    BlinkCount = blinkCount,
                    StartTime = DateTime.Now,
                    IsActive = true
                };
                
                _activeAnimations[target] = animation;
                
                Logger.Debug($"开始闪烁动画: {target.GetType().Name}, 持续时间: {duration}ms, 次数: {blinkCount}");
            }
            catch (Exception ex)
            {
                Logger.Error($"开始闪烁动画失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 开始淡出动画
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="duration">持续时间（毫秒）</param>
        public void StartFadeOutAnimation(object target, int duration)
        {
            if (target == null) return;
            
            try
            {
                var animation = new FadeOutAnimation
                {
                    Target = target,
                    Duration = duration,
                    StartTime = DateTime.Now,
                    IsActive = true
                };
                
                _activeAnimations[target] = animation;
                
                Logger.Debug($"开始淡出动画: {target.GetType().Name}, 持续时间: {duration}ms");
            }
            catch (Exception ex)
            {
                Logger.Error($"开始淡出动画失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 停止动画
        /// </summary>
        /// <param name="target">目标对象</param>
        public void StopAnimation(object target)
        {
            if (target == null) return;
            
            try
            {
                if (_activeAnimations.Remove(target))
                {
                    Logger.Debug($"停止动画: {target.GetType().Name}");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"停止动画失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 停止所有动画
        /// </summary>
        public void StopAllAnimations()
        {
            try
            {
                var count = _activeAnimations.Count;
                _activeAnimations.Clear();
                
                Logger.Debug($"停止所有动画: {count} 个");
            }
            catch (Exception ex)
            {
                Logger.Error($"停止所有动画失败: {ex.Message}");
            }
        }
        
        #endregion
        
        #region 私有方法
        
        private void OnAnimationTick(object state)
        {
            try
            {
                var now = DateTime.Now;
                var completedAnimations = new List<object>();
                
                foreach (var kvp in _activeAnimations)
                {
                    var target = kvp.Key;
                    var animation = kvp.Value;
                    
                    if (!animation.IsActive)
                    {
                        completedAnimations.Add(target);
                        continue;
                    }
                    
                    var elapsed = (now - animation.StartTime).TotalMilliseconds;
                    var progress = Math.Min(1.0, elapsed / animation.Duration);
                    
                    // 更新动画
                    UpdateAnimation(animation, progress);
                    
                    // 检查是否完成
                    if (progress >= 1.0)
                    {
                        animation.IsActive = false;
                        completedAnimations.Add(target);
                        
                        // 触发完成事件
                        OnAnimationCompleted(target, animation);
                    }
                }
                
                // 移除已完成的动画
                foreach (var target in completedAnimations)
                {
                    _activeAnimations.Remove(target);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"动画更新失败: {ex.Message}");
            }
        }
        
        private void UpdateAnimation(AnimationInfo animation, double progress)
        {
            switch (animation)
            {
                case BlinkAnimation blinkAnimation:
                    UpdateBlinkAnimation(blinkAnimation, progress);
                    break;
                case FadeOutAnimation fadeAnimation:
                    UpdateFadeOutAnimation(fadeAnimation, progress);
                    break;
            }
        }
        
        private void UpdateBlinkAnimation(BlinkAnimation animation, double progress)
        {
            // 计算闪烁状态
            var blinkProgress = progress * animation.BlinkCount;
            var isVisible = (int)blinkProgress % 2 == 0;
            
            // 这里需要更新目标对象的可见性
            // 暂时使用日志记录
            Logger.Debug($"闪烁动画更新: {animation.Target.GetType().Name}, 可见: {isVisible}, 进度: {progress:F2}");
        }
        
        private void UpdateFadeOutAnimation(FadeOutAnimation animation, double progress)
        {
            // 计算透明度
            var opacity = 1.0 - progress;
            
            // 这里需要更新目标对象的透明度
            // 暂时使用日志记录
            Logger.Debug($"淡出动画更新: {animation.Target.GetType().Name}, 透明度: {opacity:F2}, 进度: {progress:F2}");
        }
        
        private void OnAnimationCompleted(object target, AnimationInfo animation)
        {
            AnimationCompleted?.Invoke(this, new AnimationCompletedEventArgs
            {
                Target = target,
                AnimationType = animation.GetType().Name,
                Duration = animation.Duration,
                Timestamp = DateTime.Now
            });
        }
        
        #endregion
        
        #region IDisposable实现
        
        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    _animationTimer?.Dispose();
                    StopAllAnimations();
                    
                    _disposed = true;
                    Logger.Debug("动画管理器已销毁");
                }
                catch (Exception ex)
                {
                    Logger.Error($"动画管理器销毁失败: {ex.Message}");
                }
            }
        }
        
        #endregion
    }
    
    #endregion
    
    #region 动画信息类
    
    /// <summary>
    /// 动画信息基类
    /// </summary>
    public abstract class AnimationInfo
    {
        /// <summary>目标对象</summary>
        public object Target { get; set; }
        
        /// <summary>持续时间（毫秒）</summary>
        public int Duration { get; set; }
        
        /// <summary>开始时间</summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>是否激活</summary>
        public bool IsActive { get; set; }
    }
    
    /// <summary>
    /// 闪烁动画信息
    /// </summary>
    public class BlinkAnimation : AnimationInfo
    {
        /// <summary>闪烁次数</summary>
        public int BlinkCount { get; set; }
    }
    
    /// <summary>
    /// 淡出动画信息
    /// </summary>
    public class FadeOutAnimation : AnimationInfo
    {
        // 淡出动画特有属性可以在这里添加
    }
    
    #endregion
    
    #region 事件参数类
    
    /// <summary>
    /// 效果应用事件参数
    /// </summary>
    public class EffectAppliedEventArgs : EventArgs
    {
        public object Target { get; set; }
        public VisualEffect Effect { get; set; }
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 效果移除事件参数
    /// </summary>
    public class EffectRemovedEventArgs : EventArgs
    {
        public object Target { get; set; }
        public VisualEffect Effect { get; set; }
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 动画完成事件参数
    /// </summary>
    public class AnimationCompletedEventArgs : EventArgs
    {
        public object Target { get; set; }
        public string AnimationType { get; set; }
        public int Duration { get; set; }
        public DateTime Timestamp { get; set; }
    }
    
    #endregion
    
    #region 事件总线事件
    
    /// <summary>
    /// 视觉效果应用事件
    /// </summary>
    public class VisualEffectAppliedEvent : GanttEventBase
    {
        public object Target { get; }
        public EffectType EffectType { get; }
        
        public VisualEffectAppliedEvent(object target, EffectType effectType, string source = null) 
            : base(source)
        {
            Target = target;
            EffectType = effectType;
        }
    }
    
    /// <summary>
    /// 视觉效果移除事件
    /// </summary>
    public class VisualEffectRemovedEvent : GanttEventBase
    {
        public object Target { get; }
        public EffectType EffectType { get; }
        
        public VisualEffectRemovedEvent(object target, EffectType effectType, string source = null) 
            : base(source)
        {
            Target = target;
            EffectType = effectType;
        }
    }
    
    #endregion
}
