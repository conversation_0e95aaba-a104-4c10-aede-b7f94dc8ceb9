using System;
using System.Linq;
using System.Windows.Forms;
using PBIppt.GanttChart.Models;
using PBIppt.GanttChart.v2.Components;
using PBIppt.GanttChart.v2.Events;
using PBIppt.Utils;

namespace PBIppt.GanttChart.v2.Interaction.Keyboard
{
    /// <summary>
    /// 键盘导航器
    /// 提供键盘导航功能，支持方向键、Tab键等导航操作
    /// </summary>
    public class KeyboardNavigator : IDisposable
    {
        #region 私有字段
        
        private readonly string _componentId;
        private readonly IGanttChartComponent _component;
        private object _currentFocus;
        private int _currentRowIndex;
        private int _currentColumnIndex;
        private bool _disposed = false;
        
        #endregion
        
        #region 属性
        
        /// <summary>
        /// 当前焦点对象
        /// </summary>
        public object CurrentFocus => _currentFocus;
        
        /// <summary>
        /// 当前行索引
        /// </summary>
        public int CurrentRowIndex => _currentRowIndex;
        
        /// <summary>
        /// 当前列索引
        /// </summary>
        public int CurrentColumnIndex => _currentColumnIndex;
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 导航执行事件
        /// </summary>
        public event EventHandler<KeyboardNavigationEventArgs> NavigationPerformed;
        
        /// <summary>
        /// 焦点改变事件
        /// </summary>
        public event EventHandler<FocusChangedEventArgs> FocusChanged;
        
        #endregion
        
        #region 构造函数
        
        /// <summary>
        /// 初始化键盘导航器
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <param name="component">甘特图组件</param>
        public KeyboardNavigator(string componentId, IGanttChartComponent component)
        {
            _componentId = componentId ?? throw new ArgumentNullException(nameof(componentId));
            _component = component ?? throw new ArgumentNullException(nameof(component));
            
            _currentRowIndex = 0;
            _currentColumnIndex = 0;
            
            // 订阅事件
            SubscribeToEvents();
            
            Logger.Info($"键盘导航器已初始化: {componentId}");
        }
        
        #endregion
        
        #region 导航处理
        
        /// <summary>
        /// 处理导航键
        /// </summary>
        /// <param name="e">键盘事件参数</param>
        /// <returns>是否处理了该事件</returns>
        public bool HandleNavigationKey(KeyboardEventArgs e)
        {
            try
            {
                var key = (Keys)e.KeyCode;
                var handled = false;
                
                switch (key)
                {
                    case Keys.Up:
                        handled = NavigateUp(e.Shift);
                        break;
                    case Keys.Down:
                        handled = NavigateDown(e.Shift);
                        break;
                    case Keys.Left:
                        handled = NavigateLeft(e.Shift);
                        break;
                    case Keys.Right:
                        handled = NavigateRight(e.Shift);
                        break;
                    case Keys.Home:
                        handled = NavigateHome(e.Ctrl, e.Shift);
                        break;
                    case Keys.End:
                        handled = NavigateEnd(e.Ctrl, e.Shift);
                        break;
                    case Keys.PageUp:
                        handled = NavigatePageUp(e.Shift);
                        break;
                    case Keys.PageDown:
                        handled = NavigatePageDown(e.Shift);
                        break;
                    case Keys.Tab:
                        handled = NavigateTab(e.Shift);
                        break;
                }
                
                if (handled)
                {
                    OnNavigationPerformed(key, _currentFocus);
                    
                    // 发布到事件总线
                    EventBusSystem.Instance.Publish(new KeyboardNavigationEvent(
                        key, _currentFocus, _currentRowIndex, _currentColumnIndex, _componentId));
                }
                
                return handled;
            }
            catch (Exception ex)
            {
                Logger.Error($"处理导航键失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 设置焦点
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="updateSelection">是否更新选择</param>
        public void SetFocus(object target, bool updateSelection = true)
        {
            try
            {
                var oldFocus = _currentFocus;
                _currentFocus = target;
                
                // 更新位置索引
                UpdatePositionIndices(target);
                
                // 更新选择
                if (updateSelection && target != null)
                {
                    _component.SelectObject(target);
                }
                
                // 触发焦点改变事件
                OnFocusChanged(oldFocus, target);
                
                Logger.Debug($"设置焦点: {target?.GetType().Name}");
            }
            catch (Exception ex)
            {
                Logger.Error($"设置焦点失败: {ex.Message}");
            }
        }
        
        #endregion
        
        #region 导航方法
        
        private bool NavigateUp(bool extend)
        {
            var target = GetObjectAt(_currentRowIndex - 1, _currentColumnIndex);
            if (target != null)
            {
                SetFocus(target, !extend);
                if (extend)
                {
                    _component.SelectObject(target, true);
                }
                return true;
            }
            return false;
        }
        
        private bool NavigateDown(bool extend)
        {
            var target = GetObjectAt(_currentRowIndex + 1, _currentColumnIndex);
            if (target != null)
            {
                SetFocus(target, !extend);
                if (extend)
                {
                    _component.SelectObject(target, true);
                }
                return true;
            }
            return false;
        }
        
        private bool NavigateLeft(bool extend)
        {
            var target = GetObjectAt(_currentRowIndex, _currentColumnIndex - 1);
            if (target != null)
            {
                SetFocus(target, !extend);
                if (extend)
                {
                    _component.SelectObject(target, true);
                }
                return true;
            }
            return false;
        }
        
        private bool NavigateRight(bool extend)
        {
            var target = GetObjectAt(_currentRowIndex, _currentColumnIndex + 1);
            if (target != null)
            {
                SetFocus(target, !extend);
                if (extend)
                {
                    _component.SelectObject(target, true);
                }
                return true;
            }
            return false;
        }
        
        private bool NavigateHome(bool ctrl, bool extend)
        {
            object target;
            if (ctrl)
            {
                // Ctrl+Home: 移动到第一个对象
                target = GetFirstObject();
            }
            else
            {
                // Home: 移动到当前行的第一列
                target = GetObjectAt(_currentRowIndex, 0);
            }
            
            if (target != null)
            {
                SetFocus(target, !extend);
                if (extend)
                {
                    SelectRange(_currentFocus, target);
                }
                return true;
            }
            return false;
        }
        
        private bool NavigateEnd(bool ctrl, bool extend)
        {
            object target;
            if (ctrl)
            {
                // Ctrl+End: 移动到最后一个对象
                target = GetLastObject();
            }
            else
            {
                // End: 移动到当前行的最后一列
                target = GetObjectAt(_currentRowIndex, GetMaxColumnIndex());
            }
            
            if (target != null)
            {
                SetFocus(target, !extend);
                if (extend)
                {
                    SelectRange(_currentFocus, target);
                }
                return true;
            }
            return false;
        }
        
        private bool NavigatePageUp(bool extend)
        {
            var pageSize = 10; // 可配置的页面大小
            var target = GetObjectAt(Math.Max(0, _currentRowIndex - pageSize), _currentColumnIndex);
            if (target != null)
            {
                SetFocus(target, !extend);
                if (extend)
                {
                    SelectRange(_currentFocus, target);
                }
                return true;
            }
            return false;
        }
        
        private bool NavigatePageDown(bool extend)
        {
            var pageSize = 10; // 可配置的页面大小
            var maxRow = GetMaxRowIndex();
            var target = GetObjectAt(Math.Min(maxRow, _currentRowIndex + pageSize), _currentColumnIndex);
            if (target != null)
            {
                SetFocus(target, !extend);
                if (extend)
                {
                    SelectRange(_currentFocus, target);
                }
                return true;
            }
            return false;
        }
        
        private bool NavigateTab(bool reverse)
        {
            var allObjects = GetAllNavigableObjects();
            if (allObjects.Length == 0) return false;
            
            var currentIndex = Array.IndexOf(allObjects, _currentFocus);
            int nextIndex;
            
            if (reverse)
            {
                nextIndex = currentIndex <= 0 ? allObjects.Length - 1 : currentIndex - 1;
            }
            else
            {
                nextIndex = currentIndex >= allObjects.Length - 1 ? 0 : currentIndex + 1;
            }
            
            var target = allObjects[nextIndex];
            SetFocus(target);
            return true;
        }
        
        #endregion
        
        #region 辅助方法
        
        private void UpdatePositionIndices(object target)
        {
            if (target == null) return;
            
            switch (target)
            {
                case GanttTask task:
                    _currentRowIndex = task.RowIndex;
                    _currentColumnIndex = 0; // 任务在第0列
                    break;
                case GanttMilestone milestone:
                    _currentRowIndex = milestone.RowIndex;
                    _currentColumnIndex = 1; // 里程碑在第1列
                    break;
            }
        }
        
        private object GetObjectAt(int row, int column)
        {
            if (row < 0 || column < 0) return null;
            
            var project = _component.Project;
            if (project == null) return null;
            
            switch (column)
            {
                case 0: // 任务列
                    return project.Tasks?.FirstOrDefault(t => t.RowIndex == row);
                case 1: // 里程碑列
                    return project.Milestones?.FirstOrDefault(m => m.RowIndex == row);
                default:
                    return null;
            }
        }
        
        private object GetFirstObject()
        {
            var project = _component.Project;
            if (project == null) return null;
            
            var firstTask = project.Tasks?.OrderBy(t => t.RowIndex).FirstOrDefault();
            var firstMilestone = project.Milestones?.OrderBy(m => m.RowIndex).FirstOrDefault();
            
            if (firstTask == null) return firstMilestone;
            if (firstMilestone == null) return firstTask;
            
            return firstTask.RowIndex <= firstMilestone.RowIndex ? (object)firstTask : (object)firstMilestone;
        }
        
        private object GetLastObject()
        {
            var project = _component.Project;
            if (project == null) return null;
            
            var lastTask = project.Tasks?.OrderByDescending(t => t.RowIndex).FirstOrDefault();
            var lastMilestone = project.Milestones?.OrderByDescending(m => m.RowIndex).FirstOrDefault();
            
            if (lastTask == null) return lastMilestone;
            if (lastMilestone == null) return lastTask;
            
            return lastTask.RowIndex >= lastMilestone.RowIndex ? (object)lastTask : (object)lastMilestone;
        }
        
        private int GetMaxRowIndex()
        {
            var project = _component.Project;
            if (project == null) return 0;
            
            var maxTaskRow = project.Tasks?.Max(t => t.RowIndex) ?? 0;
            var maxMilestoneRow = project.Milestones?.Max(m => m.RowIndex) ?? 0;
            
            return Math.Max(maxTaskRow, maxMilestoneRow);
        }
        
        private int GetMaxColumnIndex()
        {
            return 1; // 目前只有任务(0)和里程碑(1)两列
        }
        
        private object[] GetAllNavigableObjects()
        {
            var project = _component.Project;
            if (project == null) return new object[0];
            
            return project.Tasks.Cast<object>()
                .Concat(project.Milestones.Cast<object>())
                .OrderBy(GetObjectRowIndex)
                .ToArray();
        }
        
        private int GetObjectRowIndex(object obj)
        {
            switch (obj)
            {
                case GanttTask task:
                    return task.RowIndex;
                case GanttMilestone milestone:
                    return milestone.RowIndex;
                default:
                    return int.MaxValue;
            }
        }
        
        private void SelectRange(object start, object end)
        {
            // 实现范围选择逻辑
            // 暂时使用简化实现
            if (start != null && end != null)
            {
                _component.SelectObject(start, true);
                _component.SelectObject(end, true);
            }
        }

        #endregion

        #region 事件处理

        private void SubscribeToEvents()
        {
            // 订阅相关事件
            EventBusSystem.Instance.Subscribe<SelectionChangedEvent>(OnSelectionChangedEvent, $"{_componentId}_NavigatorSelectionHandler");
            EventBusSystem.Instance.Subscribe<ErrorEvent>(OnErrorEvent, $"{_componentId}_NavigatorErrorHandler");
        }

        private void UnsubscribeFromEvents()
        {
            // 取消订阅事件
            EventBusSystem.Instance.Unsubscribe($"{_componentId}_NavigatorSelectionHandler");
            EventBusSystem.Instance.Unsubscribe($"{_componentId}_NavigatorErrorHandler");
        }

        private void OnSelectionChangedEvent(SelectionChangedEvent selectionEvent)
        {
            try
            {
                // 当选择改变时，更新焦点
                if (selectionEvent.NewSelection?.Length == 1)
                {
                    SetFocus(selectionEvent.NewSelection[0], false);
                }
                else if (selectionEvent.NewSelection?.Length == 0)
                {
                    SetFocus(null, false);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"处理选择改变事件失败: {ex.Message}");
            }
        }

        private void OnNavigationPerformed(Keys key, object target)
        {
            NavigationPerformed?.Invoke(this, new KeyboardNavigationEventArgs
            {
                NavigationKey = key,
                Target = target,
                RowIndex = _currentRowIndex,
                ColumnIndex = _currentColumnIndex,
                Timestamp = DateTime.Now
            });
        }

        private void OnFocusChanged(object oldFocus, object newFocus)
        {
            FocusChanged?.Invoke(this, new FocusChangedEventArgs
            {
                OldFocus = oldFocus,
                NewFocus = newFocus,
                RowIndex = _currentRowIndex,
                ColumnIndex = _currentColumnIndex,
                Timestamp = DateTime.Now
            });
        }

        private void OnErrorEvent(ErrorEvent errorEvent)
        {
            Logger.Error($"键盘导航器错误: {errorEvent.ErrorMessage}");
        }

        #endregion

        #region IDisposable实现

        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    UnsubscribeFromEvents();
                    _currentFocus = null;

                    _disposed = true;
                    Logger.Info($"键盘导航器已销毁: {_componentId}");
                }
                catch (Exception ex)
                {
                    Logger.Error($"键盘导航器销毁失败: {ex.Message}");
                }
            }
        }

        #endregion
    }

    #region 事件参数类

    /// <summary>
    /// 键盘导航事件参数
    /// </summary>
    public class KeyboardNavigationEventArgs : EventArgs
    {
        public Keys NavigationKey { get; set; }
        public object Target { get; set; }
        public int RowIndex { get; set; }
        public int ColumnIndex { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 焦点改变事件参数
    /// </summary>
    public class FocusChangedEventArgs : EventArgs
    {
        public object OldFocus { get; set; }
        public object NewFocus { get; set; }
        public int RowIndex { get; set; }
        public int ColumnIndex { get; set; }
        public DateTime Timestamp { get; set; }
    }

    #endregion

    #region 事件总线事件

    /// <summary>
    /// 键盘导航事件
    /// </summary>
    public class KeyboardNavigationEvent : GanttEventBase
    {
        public Keys NavigationKey { get; }
        public object Target { get; }
        public int RowIndex { get; }
        public int ColumnIndex { get; }

        public KeyboardNavigationEvent(Keys navigationKey, object target, int rowIndex, int columnIndex, string source = null)
            : base(source)
        {
            NavigationKey = navigationKey;
            Target = target;
            RowIndex = rowIndex;
            ColumnIndex = columnIndex;
        }
    }

    /// <summary>
    /// 键盘快捷键事件
    /// </summary>
    public class KeyboardShortcutEvent : GanttEventBase
    {
        public ShortcutKey Shortcut { get; }
        public IKeyboardAction Action { get; }
        public ActionResult Result { get; }

        public KeyboardShortcutEvent(ShortcutKey shortcut, IKeyboardAction action, ActionResult result, string source = null)
            : base(source)
        {
            Shortcut = shortcut;
            Action = action;
            Result = result;
        }
    }

    #endregion
}
