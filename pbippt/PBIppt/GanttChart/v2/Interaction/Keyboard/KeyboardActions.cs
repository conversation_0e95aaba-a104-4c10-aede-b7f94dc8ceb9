using System;
using System.Linq;
using PBIppt.GanttChart.Models;
using PBIppt.GanttChart.v2.Components;
using PBIppt.Utils;

namespace PBIppt.GanttChart.v2.Interaction.Keyboard
{
    #region 键盘操作接口
    
    /// <summary>
    /// 键盘操作接口
    /// </summary>
    public interface IKeyboardAction
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        string Name { get; }
        
        /// <summary>
        /// 操作描述
        /// </summary>
        string Description { get; }
        
        /// <summary>
        /// 执行操作
        /// </summary>
        /// <param name="component">甘特图组件</param>
        /// <returns>操作结果</returns>
        ActionResult Execute(IGanttChartComponent component);
    }
    
    /// <summary>
    /// 操作结果
    /// </summary>
    public class ActionResult
    {
        /// <summary>是否成功</summary>
        public bool Success { get; set; }
        
        /// <summary>结果消息</summary>
        public string Message { get; set; }
        
        /// <summary>错误代码</summary>
        public string ErrorCode { get; set; }
        
        /// <summary>附加数据</summary>
        public object Data { get; set; }
        
        /// <summary>创建成功结果</summary>
        public static ActionResult CreateSuccess(string message = "操作成功", object data = null)
        {
            return new ActionResult { Success = true, Message = message, Data = data };
        }
        
        /// <summary>创建失败结果</summary>
        public static ActionResult CreateFailure(string message, string errorCode = null)
        {
            return new ActionResult { Success = false, Message = message, ErrorCode = errorCode };
        }
    }
    
    #endregion
    
    #region 基础操作类
    
    /// <summary>
    /// 键盘操作基类
    /// </summary>
    public abstract class KeyboardActionBase : IKeyboardAction
    {
        public abstract string Name { get; }
        public abstract string Description { get; }
        
        public abstract ActionResult Execute(IGanttChartComponent component);
        
        protected void LogAction(string action, bool success, string details = null)
        {
            var message = $"键盘操作 {Name}: {action} - {(success ? "成功" : "失败")}";
            if (!string.IsNullOrEmpty(details))
                message += $" - {details}";
            
            if (success)
                Logger.Debug(message);
            else
                Logger.Warn(message);
        }
    }
    
    #endregion
    
    #region 编辑操作
    
    /// <summary>
    /// 重命名操作 (F2)
    /// </summary>
    public class RenameAction : KeyboardActionBase
    {
        public override string Name => "重命名";
        public override string Description => "重命名选中的对象";
        
        public override ActionResult Execute(IGanttChartComponent component)
        {
            try
            {
                var selectedObjects = component.GetSelectedObjects();
                if (selectedObjects?.Length != 1)
                {
                    LogAction("重命名", false, "需要选择一个对象");
                    return ActionResult.CreateFailure("请选择一个对象进行重命名");
                }
                
                var target = selectedObjects[0];
                var success = component.StartDirectEdit(target, EditType.Text);
                
                LogAction("重命名", success, target.GetType().Name);
                return success 
                    ? ActionResult.CreateSuccess("开始重命名") 
                    : ActionResult.CreateFailure("无法开始重命名");
            }
            catch (Exception ex)
            {
                LogAction("重命名", false, ex.Message);
                return ActionResult.CreateFailure($"重命名失败: {ex.Message}");
            }
        }
    }
    
    /// <summary>
    /// 删除操作 (Delete)
    /// </summary>
    public class DeleteAction : KeyboardActionBase
    {
        public override string Name => "删除";
        public override string Description => "删除选中的对象";
        
        public override ActionResult Execute(IGanttChartComponent component)
        {
            try
            {
                var selectedObjects = component.GetSelectedObjects();
                if (selectedObjects?.Length == 0)
                {
                    LogAction("删除", false, "没有选中的对象");
                    return ActionResult.CreateFailure("没有选中的对象");
                }
                
                // 这里应该实现实际的删除逻辑
                // 暂时使用占位符实现
                component.ClearSelection();
                
                LogAction("删除", true, $"删除了 {selectedObjects.Length} 个对象");
                return ActionResult.CreateSuccess($"已删除 {selectedObjects.Length} 个对象");
            }
            catch (Exception ex)
            {
                LogAction("删除", false, ex.Message);
                return ActionResult.CreateFailure($"删除失败: {ex.Message}");
            }
        }
    }
    
    /// <summary>
    /// 取消操作 (Escape)
    /// </summary>
    public class CancelAction : KeyboardActionBase
    {
        public override string Name => "取消";
        public override string Description => "取消当前操作";
        
        public override ActionResult Execute(IGanttChartComponent component)
        {
            try
            {
                // 取消当前编辑
                component.CancelDirectEdit();
                
                // 清除选择
                component.ClearSelection();
                
                LogAction("取消", true);
                return ActionResult.CreateSuccess("已取消当前操作");
            }
            catch (Exception ex)
            {
                LogAction("取消", false, ex.Message);
                return ActionResult.CreateFailure($"取消操作失败: {ex.Message}");
            }
        }
    }
    
    #endregion
    
    #region 撤销重做操作
    
    /// <summary>
    /// 撤销操作 (Ctrl+Z)
    /// </summary>
    public class UndoAction : KeyboardActionBase
    {
        public override string Name => "撤销";
        public override string Description => "撤销上一个操作";
        
        public override ActionResult Execute(IGanttChartComponent component)
        {
            try
            {
                if (!component.CanUndo)
                {
                    LogAction("撤销", false, "没有可撤销的操作");
                    return ActionResult.CreateFailure("没有可撤销的操作");
                }
                
                var success = component.Undo();
                LogAction("撤销", success);
                return success 
                    ? ActionResult.CreateSuccess("撤销成功") 
                    : ActionResult.CreateFailure("撤销失败");
            }
            catch (Exception ex)
            {
                LogAction("撤销", false, ex.Message);
                return ActionResult.CreateFailure($"撤销失败: {ex.Message}");
            }
        }
    }
    
    /// <summary>
    /// 重做操作 (Ctrl+Y)
    /// </summary>
    public class RedoAction : KeyboardActionBase
    {
        public override string Name => "重做";
        public override string Description => "重做上一个撤销的操作";
        
        public override ActionResult Execute(IGanttChartComponent component)
        {
            try
            {
                if (!component.CanRedo)
                {
                    LogAction("重做", false, "没有可重做的操作");
                    return ActionResult.CreateFailure("没有可重做的操作");
                }
                
                var success = component.Redo();
                LogAction("重做", success);
                return success 
                    ? ActionResult.CreateSuccess("重做成功") 
                    : ActionResult.CreateFailure("重做失败");
            }
            catch (Exception ex)
            {
                LogAction("重做", false, ex.Message);
                return ActionResult.CreateFailure($"重做失败: {ex.Message}");
            }
        }
    }
    
    #endregion
    
    #region 剪贴板操作
    
    /// <summary>
    /// 复制操作 (Ctrl+C)
    /// </summary>
    public class CopyAction : KeyboardActionBase
    {
        public override string Name => "复制";
        public override string Description => "复制选中的对象";
        
        public override ActionResult Execute(IGanttChartComponent component)
        {
            try
            {
                var selectedObjects = component.GetSelectedObjects();
                if (selectedObjects?.Length == 0)
                {
                    LogAction("复制", false, "没有选中的对象");
                    return ActionResult.CreateFailure("没有选中的对象");
                }
                
                // 这里应该实现实际的复制逻辑
                // 暂时使用占位符实现
                
                LogAction("复制", true, $"复制了 {selectedObjects.Length} 个对象");
                return ActionResult.CreateSuccess($"已复制 {selectedObjects.Length} 个对象");
            }
            catch (Exception ex)
            {
                LogAction("复制", false, ex.Message);
                return ActionResult.CreateFailure($"复制失败: {ex.Message}");
            }
        }
    }
    
    /// <summary>
    /// 剪切操作 (Ctrl+X)
    /// </summary>
    public class CutAction : KeyboardActionBase
    {
        public override string Name => "剪切";
        public override string Description => "剪切选中的对象";
        
        public override ActionResult Execute(IGanttChartComponent component)
        {
            try
            {
                var selectedObjects = component.GetSelectedObjects();
                if (selectedObjects?.Length == 0)
                {
                    LogAction("剪切", false, "没有选中的对象");
                    return ActionResult.CreateFailure("没有选中的对象");
                }
                
                // 这里应该实现实际的剪切逻辑
                // 暂时使用占位符实现
                component.ClearSelection();
                
                LogAction("剪切", true, $"剪切了 {selectedObjects.Length} 个对象");
                return ActionResult.CreateSuccess($"已剪切 {selectedObjects.Length} 个对象");
            }
            catch (Exception ex)
            {
                LogAction("剪切", false, ex.Message);
                return ActionResult.CreateFailure($"剪切失败: {ex.Message}");
            }
        }
    }
    
    /// <summary>
    /// 粘贴操作 (Ctrl+V)
    /// </summary>
    public class PasteAction : KeyboardActionBase
    {
        public override string Name => "粘贴";
        public override string Description => "粘贴剪贴板中的对象";
        
        public override ActionResult Execute(IGanttChartComponent component)
        {
            try
            {
                // 这里应该实现实际的粘贴逻辑
                // 暂时使用占位符实现
                
                LogAction("粘贴", true);
                return ActionResult.CreateSuccess("粘贴成功");
            }
            catch (Exception ex)
            {
                LogAction("粘贴", false, ex.Message);
                return ActionResult.CreateFailure($"粘贴失败: {ex.Message}");
            }
        }
    }
    
    #endregion
    
    #region 选择操作
    
    /// <summary>
    /// 全选操作 (Ctrl+A)
    /// </summary>
    public class SelectAllAction : KeyboardActionBase
    {
        public override string Name => "全选";
        public override string Description => "选择所有对象";
        
        public override ActionResult Execute(IGanttChartComponent component)
        {
            try
            {
                // 这里应该实现实际的全选逻辑
                // 需要从项目中获取所有任务和里程碑
                var project = component.Project;
                if (project == null)
                {
                    LogAction("全选", false, "没有项目数据");
                    return ActionResult.CreateFailure("没有项目数据");
                }
                
                var allObjects = project.Tasks.Cast<object>()
                    .Concat(project.Milestones.Cast<object>())
                    .ToArray();
                
                foreach (var obj in allObjects)
                {
                    component.SelectObject(obj, true);
                }
                
                LogAction("全选", true, $"选择了 {allObjects.Length} 个对象");
                return ActionResult.CreateSuccess($"已选择 {allObjects.Length} 个对象");
            }
            catch (Exception ex)
            {
                LogAction("全选", false, ex.Message);
                return ActionResult.CreateFailure($"全选失败: {ex.Message}");
            }
        }
    }
    
    #endregion
    
    #region 新建操作
    
    /// <summary>
    /// 新建任务操作 (Ctrl+N)
    /// </summary>
    public class NewTaskAction : KeyboardActionBase
    {
        public override string Name => "新建任务";
        public override string Description => "创建新任务";
        
        public override ActionResult Execute(IGanttChartComponent component)
        {
            try
            {
                var newTask = new GanttTask
                {
                    Id = Guid.NewGuid().ToString("N"),
                    Name = "新任务",
                    StartDate = DateTime.Today,
                    EndDate = DateTime.Today.AddDays(1),
                    RowIndex = component.Project?.Tasks?.Count ?? 0
                };
                
                // 这里应该将新任务添加到项目中
                // 暂时使用占位符实现
                
                LogAction("新建任务", true, newTask.Name);
                return ActionResult.CreateSuccess("新建任务成功", newTask);
            }
            catch (Exception ex)
            {
                LogAction("新建任务", false, ex.Message);
                return ActionResult.CreateFailure($"新建任务失败: {ex.Message}");
            }
        }
    }
    
    /// <summary>
    /// 新建里程碑操作 (Ctrl+M)
    /// </summary>
    public class NewMilestoneAction : KeyboardActionBase
    {
        public override string Name => "新建里程碑";
        public override string Description => "创建新里程碑";
        
        public override ActionResult Execute(IGanttChartComponent component)
        {
            try
            {
                var newMilestone = new GanttMilestone
                {
                    Id = Guid.NewGuid().ToString("N"),
                    Name = "新里程碑",
                    Date = DateTime.Today,
                    RowIndex = component.Project?.Milestones?.Count ?? 0
                };
                
                // 这里应该将新里程碑添加到项目中
                // 暂时使用占位符实现
                
                LogAction("新建里程碑", true, newMilestone.Name);
                return ActionResult.CreateSuccess("新建里程碑成功", newMilestone);
            }
            catch (Exception ex)
            {
                LogAction("新建里程碑", false, ex.Message);
                return ActionResult.CreateFailure($"新建里程碑失败: {ex.Message}");
            }
        }
    }
    
    #endregion
    
    #region 保存操作
    
    /// <summary>
    /// 保存操作 (Ctrl+S)
    /// </summary>
    public class SaveAction : KeyboardActionBase
    {
        public override string Name => "保存";
        public override string Description => "保存甘特图";
        
        public override ActionResult Execute(IGanttChartComponent component)
        {
            try
            {
                // 这里应该实现实际的保存逻辑
                // 暂时使用占位符实现
                
                LogAction("保存", true);
                return ActionResult.CreateSuccess("保存成功");
            }
            catch (Exception ex)
            {
                LogAction("保存", false, ex.Message);
                return ActionResult.CreateFailure($"保存失败: {ex.Message}");
            }
        }
    }
    
    #endregion
}
