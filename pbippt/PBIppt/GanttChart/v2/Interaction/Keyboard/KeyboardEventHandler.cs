using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using PBIppt.GanttChart.v2.Components;
using PBIppt.GanttChart.v2.Events;
using PBIppt.Utils;

namespace PBIppt.GanttChart.v2.Interaction.Keyboard
{
    /// <summary>
    /// 键盘事件处理器
    /// 提供完整的键盘快捷键支持和键盘导航功能
    /// </summary>
    public class KeyboardEventHandler : IDisposable
    {
        #region 私有字段
        
        private readonly string _componentId;
        private readonly IGanttChartComponent _component;
        private readonly Dictionary<ShortcutKey, IKeyboardAction> _shortcuts;
        private readonly KeyboardNavigator _navigator;
        private bool _disposed = false;
        
        #endregion
        
        #region 属性
        
        /// <summary>
        /// 组件ID
        /// </summary>
        public string ComponentId => _componentId;
        
        /// <summary>
        /// 快捷键数量
        /// </summary>
        public int ShortcutCount => _shortcuts.Count;
        
        /// <summary>
        /// 键盘导航器
        /// </summary>
        public KeyboardNavigator Navigator => _navigator;
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 快捷键执行事件
        /// </summary>
        public event EventHandler<ShortcutExecutedEventArgs> ShortcutExecuted;
        
        /// <summary>
        /// 键盘导航事件
        /// </summary>
        public event EventHandler<KeyboardNavigationEventArgs> NavigationPerformed;
        
        #endregion
        
        #region 构造函数
        
        /// <summary>
        /// 初始化键盘事件处理器
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <param name="component">甘特图组件</param>
        public KeyboardEventHandler(string componentId, IGanttChartComponent component)
        {
            _componentId = componentId ?? throw new ArgumentNullException(nameof(componentId));
            _component = component ?? throw new ArgumentNullException(nameof(component));
            
            _shortcuts = new Dictionary<ShortcutKey, IKeyboardAction>();
            _navigator = new KeyboardNavigator(componentId, component);
            
            // 注册默认快捷键
            RegisterDefaultShortcuts();
            
            // 订阅事件
            SubscribeToEvents();
            
            Logger.Info($"键盘事件处理器已初始化: {componentId}");
        }
        
        #endregion
        
        #region 快捷键管理
        
        /// <summary>
        /// 注册快捷键
        /// </summary>
        /// <param name="shortcut">快捷键</param>
        /// <param name="action">操作</param>
        public void RegisterShortcut(ShortcutKey shortcut, IKeyboardAction action)
        {
            if (shortcut == null || action == null)
                throw new ArgumentNullException();
            
            _shortcuts[shortcut] = action;
            Logger.Debug($"注册快捷键: {shortcut} -> {action.GetType().Name}");
        }
        
        /// <summary>
        /// 取消注册快捷键
        /// </summary>
        /// <param name="shortcut">快捷键</param>
        public void UnregisterShortcut(ShortcutKey shortcut)
        {
            if (shortcut != null && _shortcuts.Remove(shortcut))
            {
                Logger.Debug($"取消注册快捷键: {shortcut}");
            }
        }
        
        /// <summary>
        /// 获取所有快捷键
        /// </summary>
        /// <returns>快捷键列表</returns>
        public ShortcutKey[] GetAllShortcuts()
        {
            return _shortcuts.Keys.ToArray();
        }
        
        /// <summary>
        /// 清除所有快捷键
        /// </summary>
        public void ClearShortcuts()
        {
            _shortcuts.Clear();
            Logger.Debug("已清除所有快捷键");
        }
        
        #endregion
        
        #region 键盘事件处理
        
        /// <summary>
        /// 处理键盘事件
        /// </summary>
        /// <param name="e">键盘事件参数</param>
        /// <returns>是否处理了该事件</returns>
        public bool HandleKeyboardEvent(KeyboardEventArgs e)
        {
            if (e == null) return false;
            
            try
            {
                // 创建快捷键对象
                var shortcut = new ShortcutKey(e.KeyCode, e.Ctrl, e.Shift, e.Alt);
                
                // 查找匹配的快捷键
                if (_shortcuts.TryGetValue(shortcut, out var action))
                {
                    // 执行快捷键操作
                    var result = action.Execute(_component);
                    
                    // 触发快捷键执行事件
                    OnShortcutExecuted(shortcut, action, result);
                    
                    // 发布到事件总线
                    EventBusSystem.Instance.Publish(new KeyboardShortcutEvent(
                        shortcut, action, result, _componentId));
                    
                    Logger.Debug($"执行快捷键: {shortcut} - {(result.Success ? "成功" : "失败")}");
                    return result.Success;
                }
                
                // 处理导航键
                if (IsNavigationKey(e))
                {
                    return _navigator.HandleNavigationKey(e);
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error($"处理键盘事件失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 检查是否为导航键
        /// </summary>
        /// <param name="e">键盘事件参数</param>
        /// <returns>是否为导航键</returns>
        private bool IsNavigationKey(KeyboardEventArgs e)
        {
            var navigationKeys = new[]
            {
                Keys.Up, Keys.Down, Keys.Left, Keys.Right,
                Keys.Home, Keys.End, Keys.PageUp, Keys.PageDown,
                Keys.Tab
            };
            
            return navigationKeys.Contains((Keys)e.KeyCode);
        }
        
        #endregion
        
        #region 默认快捷键注册
        
        private void RegisterDefaultShortcuts()
        {
            // 编辑操作
            RegisterShortcut(new ShortcutKey(Keys.F2), new RenameAction());
            RegisterShortcut(new ShortcutKey(Keys.Delete), new DeleteAction());
            RegisterShortcut(new ShortcutKey(Keys.Escape), new CancelAction());
            
            // 撤销重做
            RegisterShortcut(new ShortcutKey(Keys.Z, ctrl: true), new UndoAction());
            RegisterShortcut(new ShortcutKey(Keys.Y, ctrl: true), new RedoAction());
            
            // 剪贴板操作
            RegisterShortcut(new ShortcutKey(Keys.C, ctrl: true), new CopyAction());
            RegisterShortcut(new ShortcutKey(Keys.X, ctrl: true), new CutAction());
            RegisterShortcut(new ShortcutKey(Keys.V, ctrl: true), new PasteAction());
            
            // 选择操作
            RegisterShortcut(new ShortcutKey(Keys.A, ctrl: true), new SelectAllAction());
            
            // 新建操作
            RegisterShortcut(new ShortcutKey(Keys.N, ctrl: true), new NewTaskAction());
            RegisterShortcut(new ShortcutKey(Keys.M, ctrl: true), new NewMilestoneAction());
            
            // 保存操作
            RegisterShortcut(new ShortcutKey(Keys.S, ctrl: true), new SaveAction());
            
            Logger.Debug("已注册默认快捷键");
        }
        
        #endregion
        
        #region 事件处理
        
        private void SubscribeToEvents()
        {
            // 订阅相关事件
            EventBusSystem.Instance.Subscribe<ErrorEvent>(OnErrorEvent, $"{_componentId}_KeyboardErrorHandler");
            
            // 订阅导航器事件
            _navigator.NavigationPerformed += OnNavigatorNavigationPerformed;
        }
        
        private void UnsubscribeFromEvents()
        {
            // 取消订阅事件
            EventBusSystem.Instance.Unsubscribe($"{_componentId}_KeyboardErrorHandler");
            
            // 取消订阅导航器事件
            if (_navigator != null)
            {
                _navigator.NavigationPerformed -= OnNavigatorNavigationPerformed;
            }
        }
        
        private void OnShortcutExecuted(ShortcutKey shortcut, IKeyboardAction action, ActionResult result)
        {
            ShortcutExecuted?.Invoke(this, new ShortcutExecutedEventArgs
            {
                Shortcut = shortcut,
                Action = action,
                Result = result,
                Timestamp = DateTime.Now
            });
        }
        
        private void OnNavigatorNavigationPerformed(object sender, KeyboardNavigationEventArgs e)
        {
            NavigationPerformed?.Invoke(this, e);
        }
        
        private void OnErrorEvent(ErrorEvent errorEvent)
        {
            Logger.Error($"键盘事件处理器错误: {errorEvent.ErrorMessage}");
        }
        
        #endregion
        
        #region IDisposable实现
        
        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    UnsubscribeFromEvents();
                    _navigator?.Dispose();
                    _shortcuts.Clear();
                    
                    _disposed = true;
                    Logger.Info($"键盘事件处理器已销毁: {_componentId}");
                }
                catch (Exception ex)
                {
                    Logger.Error($"键盘事件处理器销毁失败: {ex.Message}");
                }
            }
        }
        
        #endregion

    #region 事件参数类

    /// <summary>
    /// 快捷键执行事件参数
    /// </summary>
    public class ShortcutExecutedEventArgs : EventArgs
    {
        public ShortcutKey Shortcut { get; set; }
        public IKeyboardAction Action { get; set; }
        public ActionResult Result { get; set; }
        public DateTime Timestamp { get; set; }
    }

    #endregion
    }
    
    #region 快捷键类
    
    /// <summary>
    /// 快捷键定义
    /// </summary>
    public class ShortcutKey : IEquatable<ShortcutKey>
    {
        public Keys Key { get; }
        public bool Ctrl { get; }
        public bool Shift { get; }
        public bool Alt { get; }
        
        public ShortcutKey(Keys key, bool ctrl = false, bool shift = false, bool alt = false)
        {
            Key = key;
            Ctrl = ctrl;
            Shift = shift;
            Alt = alt;
        }
        
        public ShortcutKey(int keyCode, bool ctrl = false, bool shift = false, bool alt = false)
            : this((Keys)keyCode, ctrl, shift, alt)
        {
        }
        
        public bool Equals(ShortcutKey other)
        {
            if (other == null) return false;
            return Key == other.Key && Ctrl == other.Ctrl && Shift == other.Shift && Alt == other.Alt;
        }
        
        public override bool Equals(object obj)
        {
            return Equals(obj as ShortcutKey);
        }
        
        public override int GetHashCode()
        {
            unchecked
            {
                int hash = 17;
                hash = hash * 23 + Key.GetHashCode();
                hash = hash * 23 + Ctrl.GetHashCode();
                hash = hash * 23 + Shift.GetHashCode();
                hash = hash * 23 + Alt.GetHashCode();
                return hash;
            }
        }
        
        public override string ToString()
        {
            var parts = new List<string>();
            if (Ctrl) parts.Add("Ctrl");
            if (Shift) parts.Add("Shift");
            if (Alt) parts.Add("Alt");
            parts.Add(Key.ToString());
            return string.Join("+", parts);
        }
        
        public static bool operator ==(ShortcutKey left, ShortcutKey right)
        {
            return Equals(left, right);
        }
        
        public static bool operator !=(ShortcutKey left, ShortcutKey right)
        {
            return !Equals(left, right);
        }
    }

    #endregion
}
