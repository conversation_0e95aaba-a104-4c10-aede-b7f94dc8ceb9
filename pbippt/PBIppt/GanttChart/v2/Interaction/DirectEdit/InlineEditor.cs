using System;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Office.Interop.PowerPoint;
using PBIppt.GanttChart.Models;
using PBIppt.GanttChart.v2.Components;
using PBIppt.GanttChart.v2.Events;
using PBIppt.Utils;

namespace PBIppt.GanttChart.v2.Interaction.DirectEdit
{
    /// <summary>
    /// 内联编辑器
    /// 提供在甘特图内直接编辑文本、日期、数值等功能
    /// </summary>
    public class InlineEditor : IDisposable
    {
        #region 私有字段
        
        private readonly string _componentId;
        private readonly IGanttChartComponent _component;
        private Control _editControl;
        private object _editTarget;
        private EditType _editType;
        private object _originalValue;
        private Rectangle _editBounds;
        private bool _isEditing;
        private bool _disposed = false;
        
        // 编辑控件
        private TextBox _textBox;
        private DateTimePicker _datePicker;
        private NumericUpDown _numericUpDown;
        private ComboBox _comboBox;
        
        #endregion
        
        #region 属性
        
        /// <summary>
        /// 是否正在编辑
        /// </summary>
        public bool IsEditing => _isEditing;
        
        /// <summary>
        /// 当前编辑目标
        /// </summary>
        public object EditTarget => _editTarget;
        
        /// <summary>
        /// 当前编辑类型
        /// </summary>
        public EditType EditType => _editType;
        
        /// <summary>
        /// 编辑区域
        /// </summary>
        public Rectangle EditBounds => _editBounds;
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 编辑开始事件
        /// </summary>
        public event EventHandler<InlineEditStartEventArgs> EditStarted;
        
        /// <summary>
        /// 编辑完成事件
        /// </summary>
        public event EventHandler<InlineEditCompleteEventArgs> EditCompleted;
        
        /// <summary>
        /// 编辑取消事件
        /// </summary>
        public event EventHandler<InlineEditCancelEventArgs> EditCanceled;
        
        /// <summary>
        /// 编辑值改变事件
        /// </summary>
        public event EventHandler<InlineEditValueChangedEventArgs> ValueChanged;
        
        #endregion
        
        #region 构造函数
        
        /// <summary>
        /// 初始化内联编辑器
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <param name="component">甘特图组件</param>
        public InlineEditor(string componentId, IGanttChartComponent component)
        {
            _componentId = componentId ?? throw new ArgumentNullException(nameof(componentId));
            _component = component ?? throw new ArgumentNullException(nameof(component));
            
            InitializeEditControls();
            SubscribeToEvents();
            
            Logger.Info($"内联编辑器已初始化: {componentId}");
        }
        
        #endregion
        
        #region 编辑操作
        
        /// <summary>
        /// 开始内联编辑
        /// </summary>
        /// <param name="target">编辑目标</param>
        /// <param name="editType">编辑类型</param>
        /// <param name="bounds">编辑区域</param>
        /// <returns>是否成功开始编辑</returns>
        public bool StartEdit(object target, EditType editType, Rectangle bounds)
        {
            if (_isEditing || target == null || bounds.IsEmpty)
                return false;
            
            try
            {
                _editTarget = target;
                _editType = editType;
                _editBounds = bounds;
                _originalValue = GetCurrentValue(target, editType);
                
                // 选择合适的编辑控件
                _editControl = SelectEditControl(editType);
                if (_editControl == null)
                    return false;
                
                // 配置编辑控件
                ConfigureEditControl(_editControl, _originalValue, bounds);
                
                // 显示编辑控件
                ShowEditControl(_editControl);
                
                _isEditing = true;
                
                // 触发编辑开始事件
                OnEditStarted(target, editType, _originalValue);
                
                // 发布到事件总线
                EventBusSystem.Instance.Publish(new EditStartEvent(
                    target, editType, _originalValue, _componentId));
                
                Logger.Debug($"开始内联编辑: {editType}, 目标: {target.GetType().Name}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"开始内联编辑失败: {ex.Message}");
                CleanupEdit();
                return false;
            }
        }
        
        /// <summary>
        /// 完成编辑
        /// </summary>
        /// <returns>是否成功完成编辑</returns>
        public bool CompleteEdit()
        {
            if (!_isEditing) return false;
            
            try
            {
                var newValue = GetEditControlValue(_editControl, _editType);
                var success = ValidateAndApplyValue(newValue);
                
                if (success)
                {
                    // 触发编辑完成事件
                    OnEditCompleted(_editTarget, _editType, _originalValue, newValue, true);
                    
                    // 发布到事件总线
                    EventBusSystem.Instance.Publish(new EditCompleteEvent(
                        _editTarget, _editType, _originalValue, newValue, true, _componentId));
                }
                else
                {
                    // 编辑失败，恢复原值
                    OnEditCompleted(_editTarget, _editType, _originalValue, newValue, false);
                }
                
                CleanupEdit();
                
                Logger.Debug($"完成内联编辑: {(success ? "成功" : "失败")}");
                return success;
            }
            catch (Exception ex)
            {
                Logger.Error($"完成内联编辑失败: {ex.Message}");
                CleanupEdit();
                return false;
            }
        }
        
        /// <summary>
        /// 取消编辑
        /// </summary>
        public void CancelEdit()
        {
            if (!_isEditing) return;
            
            try
            {
                // 触发编辑取消事件
                OnEditCanceled(_editTarget, _editType, "用户取消");
                
                // 发布到事件总线
                EventBusSystem.Instance.Publish(new EditCancelEvent(
                    _editTarget, _editType, "用户取消", _componentId));
                
                CleanupEdit();
                
                Logger.Debug("取消内联编辑");
            }
            catch (Exception ex)
            {
                Logger.Error($"取消内联编辑失败: {ex.Message}");
                CleanupEdit();
            }
        }
        
        #endregion
        
        #region 私有方法
        
        private void InitializeEditControls()
        {
            // 初始化各种编辑控件
            _textBox = new TextBox
            {
                BorderStyle = BorderStyle.FixedSingle,
                Font = new System.Drawing.Font("Microsoft YaHei", 9F),
                Visible = false
            };
            _textBox.KeyDown += OnEditControlKeyDown;
            _textBox.LostFocus += OnEditControlLostFocus;
            _textBox.TextChanged += OnTextBoxTextChanged;

            _datePicker = new DateTimePicker
            {
                Format = DateTimePickerFormat.Short,
                Font = new System.Drawing.Font("Microsoft YaHei", 9F),
                Visible = false
            };
            _datePicker.KeyDown += OnEditControlKeyDown;
            _datePicker.LostFocus += OnEditControlLostFocus;
            _datePicker.ValueChanged += OnDatePickerValueChanged;
            
            _numericUpDown = new NumericUpDown
            {
                BorderStyle = BorderStyle.FixedSingle,
                Font = new System.Drawing.Font("Microsoft YaHei", 9F),
                DecimalPlaces = 2,
                Minimum = 0,
                Maximum = 100,
                Visible = false
            };
            _numericUpDown.KeyDown += OnEditControlKeyDown;
            _numericUpDown.LostFocus += OnEditControlLostFocus;
            _numericUpDown.ValueChanged += OnNumericUpDownValueChanged;

            _comboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new System.Drawing.Font("Microsoft YaHei", 9F),
                Visible = false
            };
            _comboBox.KeyDown += OnEditControlKeyDown;
            _comboBox.LostFocus += OnEditControlLostFocus;
            _comboBox.SelectedIndexChanged += OnComboBoxSelectedIndexChanged;
        }
        
        private void SubscribeToEvents()
        {
            // 订阅相关事件
            EventBusSystem.Instance.Subscribe<ErrorEvent>(OnErrorEvent, $"{_componentId}_EditErrorHandler");
        }
        
        private void UnsubscribeFromEvents()
        {
            // 取消订阅事件
            EventBusSystem.Instance.Unsubscribe($"{_componentId}_EditErrorHandler");
        }
        
        private Control SelectEditControl(EditType editType)
        {
            switch (editType)
            {
                case EditType.Text:
                    return _textBox;
                case EditType.Date:
                    return _datePicker;
                case EditType.Number:
                    return _numericUpDown;
                case EditType.Status:
                case EditType.Color:
                    return _comboBox;
                default:
                    return _textBox;
            }
        }
        
        private void ConfigureEditControl(Control control, object value, Rectangle bounds)
        {
            // 设置控件位置和大小
            control.Bounds = bounds;
            
            // 根据控件类型设置值
            switch (control)
            {
                case TextBox textBox:
                    textBox.Text = value?.ToString() ?? "";
                    textBox.SelectAll();
                    break;
                    
                case DateTimePicker datePicker:
                    if (value is DateTime dateValue)
                        datePicker.Value = dateValue;
                    break;
                    
                case NumericUpDown numericUpDown:
                    if (value is decimal decimalValue)
                        numericUpDown.Value = decimalValue;
                    else if (value is double doubleValue)
                        numericUpDown.Value = (decimal)doubleValue;
                    else if (value is int intValue)
                        numericUpDown.Value = intValue;
                    break;
                    
                case ComboBox comboBox:
                    ConfigureComboBox(comboBox, _editType, value);
                    break;
            }
        }
        
        private void ConfigureComboBox(ComboBox comboBox, EditType editType, object value)
        {
            comboBox.Items.Clear();
            
            switch (editType)
            {
                case EditType.Status:
                    comboBox.Items.AddRange(new object[]
                    {
                        TaskStatus.NotStarted,
                        TaskStatus.InProgress,
                        TaskStatus.Completed,
                        TaskStatus.Delayed,
                        TaskStatus.OnHold
                    });
                    if (value is TaskStatus status)
                        comboBox.SelectedItem = status;
                    break;
                    
                case EditType.Color:
                    comboBox.Items.AddRange(new object[]
                    {
                        "蓝色", "绿色", "红色", "橙色", "紫色", "黄色", "灰色"
                    });
                    comboBox.SelectedIndex = 0;
                    break;
            }
        }
        
        private void ShowEditControl(Control control)
        {
            // 显示编辑控件
            // 这里需要将控件添加到PowerPoint的容器中
            // 暂时使用占位符实现
            control.Visible = true;
            control.Focus();
            
            Logger.Debug($"显示编辑控件: {control.GetType().Name}");
        }
        
        private void HideEditControl(Control control)
        {
            if (control != null)
            {
                control.Visible = false;
                Logger.Debug($"隐藏编辑控件: {control.GetType().Name}");
            }
        }
        
        private object GetCurrentValue(object target, EditType editType)
        {
            try
            {
                switch (target)
                {
                    case GanttTask task:
                        return GetTaskValue(task, editType);
                    case GanttMilestone milestone:
                        return GetMilestoneValue(milestone, editType);
                    default:
                        return null;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"获取当前值失败: {ex.Message}");
                return null;
            }
        }
        
        private object GetTaskValue(GanttTask task, EditType editType)
        {
            switch (editType)
            {
                case EditType.Text:
                    return task.Name;
                case EditType.Date:
                    return task.StartDate;
                case EditType.Number:
                    return task.Progress;
                case EditType.Status:
                    return task.Status;
                case EditType.Color:
                    return task.Color;
                default:
                    return task.Name;
            }
        }
        
        private object GetMilestoneValue(GanttMilestone milestone, EditType editType)
        {
            switch (editType)
            {
                case EditType.Text:
                    return milestone.Name;
                case EditType.Date:
                    return milestone.Date;
                case EditType.Color:
                    return milestone.Color;
                default:
                    return milestone.Name;
            }
        }
        
        private object GetEditControlValue(Control control, EditType editType)
        {
            switch (control)
            {
                case TextBox textBox:
                    return textBox.Text;
                case DateTimePicker datePicker:
                    return datePicker.Value;
                case NumericUpDown numericUpDown:
                    return numericUpDown.Value;
                case ComboBox comboBox:
                    return comboBox.SelectedItem;
                default:
                    return null;
            }
        }
        
        private bool ValidateAndApplyValue(object newValue)
        {
            try
            {
                // 验证新值
                if (!ValidateValue(newValue))
                    return false;
                
                // 应用新值
                return ApplyValue(_editTarget, _editType, newValue);
            }
            catch (Exception ex)
            {
                Logger.Error($"验证和应用值失败: {ex.Message}");
                return false;
            }
        }
        
        private bool ValidateValue(object value)
        {
            if (value == null) return false;
            
            switch (_editType)
            {
                case EditType.Text:
                    return !string.IsNullOrWhiteSpace(value.ToString());
                case EditType.Date:
                    return value is DateTime;
                case EditType.Number:
                    return value is decimal || value is double || value is int;
                default:
                    return true;
            }
        }
        
        private bool ApplyValue(object target, EditType editType, object newValue)
        {
            try
            {
                switch (target)
                {
                    case GanttTask task:
                        return ApplyTaskValue(task, editType, newValue);
                    case GanttMilestone milestone:
                        return ApplyMilestoneValue(milestone, editType, newValue);
                    default:
                        return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"应用值失败: {ex.Message}");
                return false;
            }
        }
        
        private bool ApplyTaskValue(GanttTask task, EditType editType, object newValue)
        {
            switch (editType)
            {
                case EditType.Text:
                    task.Name = newValue.ToString();
                    return true;
                case EditType.Date:
                    if (newValue is DateTime dateValue)
                    {
                        var duration = task.EndDate - task.StartDate;
                        task.StartDate = dateValue;
                        task.EndDate = dateValue.Add(duration);
                        return true;
                    }
                    break;
                case EditType.Number:
                    if (newValue is decimal decimalValue)
                    {
                        task.Progress = (double)decimalValue;
                        return true;
                    }
                    break;
                case EditType.Status:
                    if (newValue is TaskStatus status)
                    {
                        task.Status = status;
                        return true;
                    }
                    break;
            }
            return false;
        }
        
        private bool ApplyMilestoneValue(GanttMilestone milestone, EditType editType, object newValue)
        {
            switch (editType)
            {
                case EditType.Text:
                    milestone.Name = newValue.ToString();
                    return true;
                case EditType.Date:
                    if (newValue is DateTime dateValue)
                    {
                        milestone.Date = dateValue;
                        return true;
                    }
                    break;
            }
            return false;
        }
        
        private void CleanupEdit()
        {
            if (_editControl != null)
            {
                HideEditControl(_editControl);
                _editControl = null;
            }

            _editTarget = null;
            _originalValue = null;
            _editBounds = Rectangle.Empty;
            _isEditing = false;
        }

        #endregion

        #region 事件处理

        private void OnEditControlKeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                switch (e.KeyCode)
                {
                    case Keys.Enter:
                        e.Handled = true;
                        CompleteEdit();
                        break;

                    case Keys.Escape:
                        e.Handled = true;
                        CancelEdit();
                        break;

                    case Keys.Tab:
                        e.Handled = true;
                        // 可以实现Tab键切换到下一个可编辑项
                        CompleteEdit();
                        break;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"编辑控件键盘事件处理失败: {ex.Message}");
            }
        }

        private void OnEditControlLostFocus(object sender, EventArgs e)
        {
            try
            {
                // 失去焦点时完成编辑
                if (_isEditing)
                {
                    CompleteEdit();
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"编辑控件失去焦点事件处理失败: {ex.Message}");
            }
        }

        private void OnTextBoxTextChanged(object sender, EventArgs e)
        {
            try
            {
                if (_isEditing && sender is TextBox textBox)
                {
                    OnValueChanged(_editTarget, _editType, _originalValue, textBox.Text);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"文本框值改变事件处理失败: {ex.Message}");
            }
        }

        private void OnDatePickerValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (_isEditing && sender is DateTimePicker datePicker)
                {
                    OnValueChanged(_editTarget, _editType, _originalValue, datePicker.Value);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"日期选择器值改变事件处理失败: {ex.Message}");
            }
        }

        private void OnNumericUpDownValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (_isEditing && sender is NumericUpDown numericUpDown)
                {
                    OnValueChanged(_editTarget, _editType, _originalValue, numericUpDown.Value);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"数值控件值改变事件处理失败: {ex.Message}");
            }
        }

        private void OnComboBoxSelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (_isEditing && sender is ComboBox comboBox && comboBox.SelectedItem != null)
                {
                    OnValueChanged(_editTarget, _editType, _originalValue, comboBox.SelectedItem);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"下拉框值改变事件处理失败: {ex.Message}");
            }
        }

        private void OnEditStarted(object target, EditType editType, object originalValue)
        {
            EditStarted?.Invoke(this, new InlineEditStartEventArgs
            {
                Target = target,
                EditType = editType,
                OriginalValue = originalValue,
                Timestamp = DateTime.Now
            });
        }

        private void OnEditCompleted(object target, EditType editType, object oldValue, object newValue, bool success)
        {
            EditCompleted?.Invoke(this, new InlineEditCompleteEventArgs
            {
                Target = target,
                EditType = editType,
                OldValue = oldValue,
                NewValue = newValue,
                Success = success,
                Timestamp = DateTime.Now
            });
        }

        private void OnEditCanceled(object target, EditType editType, string reason)
        {
            EditCanceled?.Invoke(this, new InlineEditCancelEventArgs
            {
                Target = target,
                EditType = editType,
                Reason = reason,
                Timestamp = DateTime.Now
            });
        }

        private void OnValueChanged(object target, EditType editType, object oldValue, object newValue)
        {
            ValueChanged?.Invoke(this, new InlineEditValueChangedEventArgs
            {
                Target = target,
                EditType = editType,
                OldValue = oldValue,
                NewValue = newValue,
                Timestamp = DateTime.Now
            });
        }

        private void OnErrorEvent(ErrorEvent errorEvent)
        {
            Logger.Error($"内联编辑器错误: {errorEvent.ErrorMessage}");
        }

        #endregion

        #region IDisposable实现

        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    // 取消当前编辑
                    if (_isEditing)
                    {
                        CancelEdit();
                    }

                    // 清理资源
                    UnsubscribeFromEvents();

                    _textBox?.Dispose();
                    _datePicker?.Dispose();
                    _numericUpDown?.Dispose();
                    _comboBox?.Dispose();

                    _disposed = true;
                    Logger.Info($"内联编辑器已销毁: {_componentId}");
                }
                catch (Exception ex)
                {
                    Logger.Error($"内联编辑器销毁失败: {ex.Message}");
                }
            }
        }

        #endregion
    }

    #region 事件参数类

    /// <summary>
    /// 内联编辑开始事件参数
    /// </summary>
    public class InlineEditStartEventArgs : EventArgs
    {
        public object Target { get; set; }
        public EditType EditType { get; set; }
        public object OriginalValue { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 内联编辑完成事件参数
    /// </summary>
    public class InlineEditCompleteEventArgs : EventArgs
    {
        public object Target { get; set; }
        public EditType EditType { get; set; }
        public object OldValue { get; set; }
        public object NewValue { get; set; }
        public bool Success { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 内联编辑取消事件参数
    /// </summary>
    public class InlineEditCancelEventArgs : EventArgs
    {
        public object Target { get; set; }
        public EditType EditType { get; set; }
        public string Reason { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 内联编辑值改变事件参数
    /// </summary>
    public class InlineEditValueChangedEventArgs : EventArgs
    {
        public object Target { get; set; }
        public EditType EditType { get; set; }
        public object OldValue { get; set; }
        public object NewValue { get; set; }
        public DateTime Timestamp { get; set; }
    }

    #endregion
}
