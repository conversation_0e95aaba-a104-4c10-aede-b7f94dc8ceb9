using System;
using System.Drawing;
using PBIppt.GanttChart.Models;
using PBIppt.GanttChart.v2.Components;
using PBIppt.GanttChart.v2.Events;
using PBIppt.Utils;
using DrawingPoint = System.Drawing.Point;

namespace PBIppt.GanttChart.v2.Interaction.DirectEdit
{
    /// <summary>
    /// 双击编辑管理器
    /// 处理双击事件并启动相应的编辑操作
    /// </summary>
    public class DoubleClickEditor : IDisposable
    {
        #region 私有字段
        
        private readonly string _componentId;
        private readonly IGanttChartComponent _component;
        private readonly InlineEditor _inlineEditor;
        private DateTime _lastClickTime;
        private Point _lastClickLocation;
        private object _lastClickTarget;
        private readonly int _doubleClickThreshold = 500; // 双击时间阈值（毫秒）
        private readonly int _doubleClickDistance = 5; // 双击距离阈值（像素）
        private bool _disposed = false;
        
        #endregion
        
        #region 属性
        
        /// <summary>
        /// 双击时间阈值（毫秒）
        /// </summary>
        public int DoubleClickThreshold => _doubleClickThreshold;
        
        /// <summary>
        /// 双击距离阈值（像素）
        /// </summary>
        public int DoubleClickDistance => _doubleClickDistance;
        
        /// <summary>
        /// 内联编辑器
        /// </summary>
        public InlineEditor InlineEditor => _inlineEditor;
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 双击检测事件
        /// </summary>
        public event EventHandler<DoubleClickDetectedEventArgs> DoubleClickDetected;
        
        /// <summary>
        /// 编辑启动事件
        /// </summary>
        public event EventHandler<EditLaunchedEventArgs> EditLaunched;
        
        #endregion
        
        #region 构造函数
        
        /// <summary>
        /// 初始化双击编辑管理器
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <param name="component">甘特图组件</param>
        public DoubleClickEditor(string componentId, IGanttChartComponent component)
        {
            _componentId = componentId ?? throw new ArgumentNullException(nameof(componentId));
            _component = component ?? throw new ArgumentNullException(nameof(component));
            
            // 创建内联编辑器
            _inlineEditor = new InlineEditor(componentId, component);
            
            // 订阅事件
            SubscribeToEvents();
            
            Logger.Info($"双击编辑管理器已初始化: {componentId}");
        }
        
        #endregion
        
        #region 双击检测
        
        /// <summary>
        /// 处理鼠标点击事件
        /// </summary>
        /// <param name="location">点击位置</param>
        /// <param name="target">点击目标</param>
        /// <param name="clickTime">点击时间</param>
        /// <returns>是否检测到双击</returns>
        public bool HandleMouseClick(Point location, object target, DateTime clickTime)
        {
            try
            {
                // 检查是否为双击
                if (IsDoubleClick(location, target, clickTime))
                {
                    // 触发双击检测事件
                    OnDoubleClickDetected(location, target, clickTime);
                    
                    // 启动编辑
                    var editType = DetermineEditType(target, location);
                    var editBounds = CalculateEditBounds(target, location, editType);
                    
                    if (editBounds != Rectangle.Empty)
                    {
                        var success = _inlineEditor.StartEdit(target, editType, editBounds);
                        
                        if (success)
                        {
                            OnEditLaunched(target, editType, location);
                            
                            // 发布到事件总线
                            EventBusSystem.Instance.Publish(new DoubleClickEditEvent(
                                target, editType, location, _componentId));
                        }
                        
                        Logger.Debug($"双击编辑启动: {(success ? "成功" : "失败")} - {editType}");
                        return success;
                    }
                }
                else
                {
                    // 记录单击信息
                    _lastClickTime = clickTime;
                    _lastClickLocation = location;
                    _lastClickTarget = target;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error($"处理鼠标点击失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 检查是否为双击
        /// </summary>
        /// <param name="location">当前点击位置</param>
        /// <param name="target">当前点击目标</param>
        /// <param name="clickTime">当前点击时间</param>
        /// <returns>是否为双击</returns>
        private bool IsDoubleClick(Point location, object target, DateTime clickTime)
        {
            // 检查时间间隔
            var timeDiff = (clickTime - _lastClickTime).TotalMilliseconds;
            if (timeDiff > _doubleClickThreshold)
                return false;
            
            // 检查距离
            var distance = Math.Sqrt(Math.Pow(location.X - _lastClickLocation.X, 2) + 
                                   Math.Pow(location.Y - _lastClickLocation.Y, 2));
            if (distance > _doubleClickDistance)
                return false;
            
            // 检查目标对象
            if (!ReferenceEquals(target, _lastClickTarget))
                return false;
            
            return true;
        }
        
        #endregion
        
        #region 编辑类型确定
        
        /// <summary>
        /// 确定编辑类型
        /// </summary>
        /// <param name="target">编辑目标</param>
        /// <param name="location">点击位置</param>
        /// <returns>编辑类型</returns>
        private EditType DetermineEditType(object target, Point location)
        {
            try
            {
                switch (target)
                {
                    case GanttTask task:
                        return DetermineTaskEditType(task, location);
                    case GanttMilestone milestone:
                        return DetermineMilestoneEditType(milestone, location);
                    default:
                        return EditType.Text;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"确定编辑类型失败: {ex.Message}");
                return EditType.Text;
            }
        }
        
        /// <summary>
        /// 确定任务编辑类型
        /// </summary>
        /// <param name="task">任务对象</param>
        /// <param name="location">点击位置</param>
        /// <returns>编辑类型</returns>
        private EditType DetermineTaskEditType(GanttTask task, Point location)
        {
            // 根据点击位置确定编辑类型
            // 这里需要根据任务在甘特图中的布局来判断
            // 暂时使用简化逻辑
            
            var taskBounds = GetTaskBounds(task);
            if (taskBounds.IsEmpty)
                return EditType.Text;
            
            // 计算相对位置
            var relativeX = (double)(location.X - taskBounds.X) / taskBounds.Width;
            var relativeY = (double)(location.Y - taskBounds.Y) / taskBounds.Height;
            
            // 根据点击区域确定编辑类型
            if (relativeX < 0.3) // 左侧区域 - 任务名称
                return EditType.Text;
            else if (relativeX > 0.7) // 右侧区域 - 进度
                return EditType.Number;
            else if (relativeY < 0.5) // 上半部分 - 开始日期
                return EditType.Date;
            else // 下半部分 - 状态
                return EditType.Status;
        }
        
        /// <summary>
        /// 确定里程碑编辑类型
        /// </summary>
        /// <param name="milestone">里程碑对象</param>
        /// <param name="location">点击位置</param>
        /// <returns>编辑类型</returns>
        private EditType DetermineMilestoneEditType(GanttMilestone milestone, Point location)
        {
            // 里程碑主要编辑名称和日期
            var milestoneBounds = GetMilestoneBounds(milestone);
            if (milestoneBounds.IsEmpty)
                return EditType.Text;
            
            var relativeX = (double)(location.X - milestoneBounds.X) / milestoneBounds.Width;
            
            // 左侧编辑名称，右侧编辑日期
            return relativeX < 0.5 ? EditType.Text : EditType.Date;
        }
        
        #endregion
        
        #region 编辑区域计算
        
        /// <summary>
        /// 计算编辑区域
        /// </summary>
        /// <param name="target">编辑目标</param>
        /// <param name="location">点击位置</param>
        /// <param name="editType">编辑类型</param>
        /// <returns>编辑区域</returns>
        private Rectangle CalculateEditBounds(object target, Point location, EditType editType)
        {
            try
            {
                switch (target)
                {
                    case GanttTask task:
                        return CalculateTaskEditBounds(task, location, editType);
                    case GanttMilestone milestone:
                        return CalculateMilestoneEditBounds(milestone, location, editType);
                    default:
                        return Rectangle.Empty;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"计算编辑区域失败: {ex.Message}");
                return Rectangle.Empty;
            }
        }
        
        /// <summary>
        /// 计算任务编辑区域
        /// </summary>
        /// <param name="task">任务对象</param>
        /// <param name="location">点击位置</param>
        /// <param name="editType">编辑类型</param>
        /// <returns>编辑区域</returns>
        private Rectangle CalculateTaskEditBounds(GanttTask task, Point location, EditType editType)
        {
            var taskBounds = GetTaskBounds(task);
            if (taskBounds.IsEmpty)
                return Rectangle.Empty;
            
            // 根据编辑类型调整编辑区域
            switch (editType)
            {
                case EditType.Text:
                    // 任务名称区域
                    return new Rectangle(taskBounds.X, taskBounds.Y, 
                        taskBounds.Width / 3, taskBounds.Height);
                    
                case EditType.Date:
                    // 日期区域
                    return new Rectangle(taskBounds.X + taskBounds.Width / 3, taskBounds.Y,
                        taskBounds.Width / 3, taskBounds.Height / 2);
                    
                case EditType.Number:
                    // 进度区域
                    return new Rectangle(taskBounds.X + 2 * taskBounds.Width / 3, taskBounds.Y,
                        taskBounds.Width / 3, taskBounds.Height);
                    
                case EditType.Status:
                    // 状态区域
                    return new Rectangle(taskBounds.X + taskBounds.Width / 3, 
                        taskBounds.Y + taskBounds.Height / 2,
                        taskBounds.Width / 3, taskBounds.Height / 2);
                    
                default:
                    return taskBounds;
            }
        }
        
        /// <summary>
        /// 计算里程碑编辑区域
        /// </summary>
        /// <param name="milestone">里程碑对象</param>
        /// <param name="location">点击位置</param>
        /// <param name="editType">编辑类型</param>
        /// <returns>编辑区域</returns>
        private Rectangle CalculateMilestoneEditBounds(GanttMilestone milestone, Point location, EditType editType)
        {
            var milestoneBounds = GetMilestoneBounds(milestone);
            if (milestoneBounds.IsEmpty)
                return Rectangle.Empty;
            
            // 根据编辑类型调整编辑区域
            switch (editType)
            {
                case EditType.Text:
                    // 名称区域
                    return new Rectangle(milestoneBounds.X, milestoneBounds.Y,
                        milestoneBounds.Width / 2, milestoneBounds.Height);
                    
                case EditType.Date:
                    // 日期区域
                    return new Rectangle(milestoneBounds.X + milestoneBounds.Width / 2, milestoneBounds.Y,
                        milestoneBounds.Width / 2, milestoneBounds.Height);
                    
                default:
                    return milestoneBounds;
            }
        }
        
        #endregion
        
        #region 辅助方法
        
        /// <summary>
        /// 获取任务边界
        /// </summary>
        /// <param name="task">任务对象</param>
        /// <returns>任务边界</returns>
        private Rectangle GetTaskBounds(GanttTask task)
        {
            // 这里需要从布局管理器获取任务的实际边界
            // 暂时使用占位符实现
            return new Rectangle(100, 50 + task.RowIndex * 30, 200, 25);
        }
        
        /// <summary>
        /// 获取里程碑边界
        /// </summary>
        /// <param name="milestone">里程碑对象</param>
        /// <returns>里程碑边界</returns>
        private Rectangle GetMilestoneBounds(GanttMilestone milestone)
        {
            // 这里需要从布局管理器获取里程碑的实际边界
            // 暂时使用占位符实现
            return new Rectangle(150, 50 + milestone.RowIndex * 30, 100, 25);
        }

        #endregion

        #region 事件处理

        private void SubscribeToEvents()
        {
            // 订阅相关事件
            EventBusSystem.Instance.Subscribe<MouseClickEvent>(OnMouseClickEvent, $"{_componentId}_DoubleClickHandler");
            EventBusSystem.Instance.Subscribe<ErrorEvent>(OnErrorEvent, $"{_componentId}_DoubleClickErrorHandler");

            // 订阅内联编辑器事件
            _inlineEditor.EditStarted += OnInlineEditStarted;
            _inlineEditor.EditCompleted += OnInlineEditCompleted;
            _inlineEditor.EditCanceled += OnInlineEditCanceled;
        }

        private void UnsubscribeFromEvents()
        {
            // 取消订阅事件
            EventBusSystem.Instance.Unsubscribe($"{_componentId}_DoubleClickHandler");
            EventBusSystem.Instance.Unsubscribe($"{_componentId}_DoubleClickErrorHandler");

            // 取消订阅内联编辑器事件
            if (_inlineEditor != null)
            {
                _inlineEditor.EditStarted -= OnInlineEditStarted;
                _inlineEditor.EditCompleted -= OnInlineEditCompleted;
                _inlineEditor.EditCanceled -= OnInlineEditCanceled;
            }
        }

        private void OnMouseClickEvent(MouseClickEvent mouseClickEvent)
        {
            try
            {
                // 处理鼠标点击事件
                if (mouseClickEvent.ClickCount == 1 && mouseClickEvent.Button == MouseButtons.Left)
                {
                    HandleMouseClick(mouseClickEvent.Location, mouseClickEvent.Target, mouseClickEvent.Timestamp);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"处理鼠标点击事件失败: {ex.Message}");
            }
        }

        private void OnInlineEditStarted(object sender, InlineEditStartEventArgs e)
        {
            Logger.Debug($"内联编辑开始: {e.EditType} - {e.Target?.GetType().Name}");
        }

        private void OnInlineEditCompleted(object sender, InlineEditCompleteEventArgs e)
        {
            Logger.Debug($"内联编辑完成: {(e.Success ? "成功" : "失败")} - {e.EditType}");
        }

        private void OnInlineEditCanceled(object sender, InlineEditCancelEventArgs e)
        {
            Logger.Debug($"内联编辑取消: {e.Reason} - {e.EditType}");
        }

        private void OnDoubleClickDetected(Point location, object target, DateTime clickTime)
        {
            DoubleClickDetected?.Invoke(this, new DoubleClickDetectedEventArgs
            {
                Location = location,
                Target = target,
                ClickTime = clickTime,
                Timestamp = DateTime.Now
            });
        }

        private void OnEditLaunched(object target, EditType editType, Point location)
        {
            EditLaunched?.Invoke(this, new EditLaunchedEventArgs
            {
                Target = target,
                EditType = editType,
                Location = location,
                Timestamp = DateTime.Now
            });
        }

        private void OnErrorEvent(ErrorEvent errorEvent)
        {
            Logger.Error($"双击编辑管理器错误: {errorEvent.ErrorMessage}");
        }

        #endregion

        #region IDisposable实现

        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    UnsubscribeFromEvents();
                    _inlineEditor?.Dispose();

                    _disposed = true;
                    Logger.Info($"双击编辑管理器已销毁: {_componentId}");
                }
                catch (Exception ex)
                {
                    Logger.Error($"双击编辑管理器销毁失败: {ex.Message}");
                }
            }
        }

        #endregion
    }

    #region 事件参数类

    /// <summary>
    /// 双击检测事件参数
    /// </summary>
    public class DoubleClickDetectedEventArgs : EventArgs
    {
        public DrawingPoint Location { get; set; }
        public object Target { get; set; }
        public DateTime ClickTime { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 编辑启动事件参数
    /// </summary>
    public class EditLaunchedEventArgs : EventArgs
    {
        public object Target { get; set; }
        public EditType EditType { get; set; }
        public DrawingPoint Location { get; set; }
        public DateTime Timestamp { get; set; }
    }

    #endregion

    #region 事件总线事件

    /// <summary>
    /// 双击编辑事件
    /// </summary>
    public class DoubleClickEditEvent : GanttEventBase
    {
        public object Target { get; }
        public EditType EditType { get; }
        public Point Location { get; }

        public DoubleClickEditEvent(object target, EditType editType, Point location, string source = null)
            : base(source)
        {
            Target = target;
            EditType = editType;
            Location = location;
        }
    }

    #endregion
}
