using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PBIppt.GanttChart.Models;
using PBIppt.GanttChart.v2.Events;
using PBIppt.GanttChart.v2.Components;
using PBIppt.Utils;

namespace PBIppt.GanttChart.v2.Interaction.ContextMenu
{
    /// <summary>
    /// 上下文菜单框架 v2.0
    /// 提供丰富的右键菜单功能和快速操作
    /// </summary>
    public class ContextMenuFramework : IDisposable
    {
        #region 私有字段
        
        private readonly string _componentId;
        private readonly IGanttChartComponent _component;
        private readonly Dictionary<Type, IContextMenuProvider> _menuProviders;
        private readonly ContextMenuStrip _contextMenu;
        private object _currentTarget;
        private Point _currentLocation;
        private bool _disposed = false;
        
        #endregion
        
        #region 属性
        
        /// <summary>
        /// 组件ID
        /// </summary>
        public string ComponentId => _componentId;
        
        /// <summary>
        /// 当前目标对象
        /// </summary>
        public object CurrentTarget => _currentTarget;
        
        /// <summary>
        /// 菜单提供者数量
        /// </summary>
        public int ProviderCount => _menuProviders.Count;
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 菜单显示事件
        /// </summary>
        public event EventHandler<ContextMenuShowEventArgs> MenuShowing;
        
        /// <summary>
        /// 菜单项点击事件
        /// </summary>
        public event EventHandler<ContextMenuItemClickEventArgs> MenuItemClicked;
        
        /// <summary>
        /// 菜单关闭事件
        /// </summary>
        public event EventHandler<ContextMenuCloseEventArgs> MenuClosed;
        
        #endregion
        
        #region 构造函数
        
        /// <summary>
        /// 初始化上下文菜单框架
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <param name="component">甘特图组件</param>
        public ContextMenuFramework(string componentId, IGanttChartComponent component)
        {
            _componentId = componentId ?? throw new ArgumentNullException(nameof(componentId));
            _component = component ?? throw new ArgumentNullException(nameof(component));
            
            _menuProviders = new Dictionary<Type, IContextMenuProvider>();
            _contextMenu = new ContextMenuStrip();
            
            // 配置上下文菜单
            ConfigureContextMenu();
            
            // 注册默认菜单提供者
            RegisterDefaultProviders();
            
            // 订阅事件
            SubscribeToEvents();
            
            Logger.Info($"上下文菜单框架已初始化: {componentId}");
        }
        
        #endregion
        
        #region 菜单提供者管理
        
        /// <summary>
        /// 注册菜单提供者
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="provider">菜单提供者</param>
        public void RegisterProvider<T>(IContextMenuProvider provider)
        {
            if (provider == null)
                throw new ArgumentNullException(nameof(provider));
            
            var targetType = typeof(T);
            _menuProviders[targetType] = provider;
            
            Logger.Debug($"注册菜单提供者: {targetType.Name} -> {provider.GetType().Name}");
        }
        
        /// <summary>
        /// 取消注册菜单提供者
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        public void UnregisterProvider<T>()
        {
            var targetType = typeof(T);
            if (_menuProviders.Remove(targetType))
            {
                Logger.Debug($"取消注册菜单提供者: {targetType.Name}");
            }
        }
        
        /// <summary>
        /// 获取菜单提供者
        /// </summary>
        /// <param name="targetType">目标类型</param>
        /// <returns>菜单提供者</returns>
        public IContextMenuProvider GetProvider(Type targetType)
        {
            if (targetType == null) return null;
            
            // 直接匹配
            if (_menuProviders.TryGetValue(targetType, out var provider))
                return provider;
            
            // 基类匹配
            foreach (var kvp in _menuProviders)
            {
                if (kvp.Key.IsAssignableFrom(targetType))
                    return kvp.Value;
            }
            
            return null;
        }
        
        #endregion
        
        #region 菜单显示
        
        /// <summary>
        /// 显示上下文菜单
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="location">显示位置</param>
        public void ShowContextMenu(object target, Point location)
        {
            try
            {
                _currentTarget = target;
                _currentLocation = location;
                
                // 清除现有菜单项
                _contextMenu.Items.Clear();
                
                // 获取菜单提供者
                var provider = GetProvider(target?.GetType());
                if (provider == null)
                {
                    // 使用默认菜单
                    provider = GetProvider(typeof(object));
                }
                
                if (provider != null)
                {
                    // 构建菜单项
                    var menuItems = provider.GetMenuItems(target, _component);
                    BuildMenuItems(menuItems);
                    
                    // 触发菜单显示事件
                    OnMenuShowing(target, location);
                    
                    // 显示菜单
                    if (_contextMenu.Items.Count > 0)
                    {
                        _contextMenu.Show(location);
                        
                        // 发布到事件总线
                        EventBusSystem.Instance.Publish(new ContextMenuShowEvent(
                            target, location, _componentId));
                    }
                }
                
                Logger.Debug($"显示上下文菜单: {target?.GetType().Name} at ({location.X}, {location.Y})");
            }
            catch (Exception ex)
            {
                Logger.Error($"显示上下文菜单失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 隐藏上下文菜单
        /// </summary>
        public void HideContextMenu()
        {
            try
            {
                if (_contextMenu.Visible)
                {
                    _contextMenu.Hide();
                    OnMenuClosed(_currentTarget);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"隐藏上下文菜单失败: {ex.Message}");
            }
        }
        
        #endregion
        
        #region 私有方法
        
        private void ConfigureContextMenu()
        {
            _contextMenu.RenderMode = ToolStripRenderMode.System;
            _contextMenu.ShowImageMargin = true;
            _contextMenu.ShowCheckMargin = false;
            
            // 订阅菜单事件
            _contextMenu.ItemClicked += OnContextMenuItemClicked;
            _contextMenu.Closed += OnContextMenuClosed;
        }
        
        private void RegisterDefaultProviders()
        {
            // 注册默认菜单提供者
            RegisterProvider<GanttTask>(new TaskContextMenuProvider());
            RegisterProvider<GanttMilestone>(new MilestoneContextMenuProvider());
            RegisterProvider<object>(new DefaultContextMenuProvider());
        }
        
        private void SubscribeToEvents()
        {
            // 订阅相关事件
            EventBusSystem.Instance.Subscribe<ErrorEvent>(OnErrorEvent, $"{_componentId}_MenuErrorHandler");
        }
        
        private void UnsubscribeFromEvents()
        {
            // 取消订阅事件
            EventBusSystem.Instance.Unsubscribe($"{_componentId}_MenuErrorHandler");
        }
        
        private void BuildMenuItems(IEnumerable<ContextMenuItem> menuItems)
        {
            if (menuItems == null) return;
            
            foreach (var item in menuItems)
            {
                var toolStripItem = CreateToolStripItem(item);
                if (toolStripItem != null)
                {
                    _contextMenu.Items.Add(toolStripItem);
                }
            }
        }
        
        private ToolStripItem CreateToolStripItem(ContextMenuItem item)
        {
            if (item == null) return null;
            
            switch (item.Type)
            {
                case ContextMenuItemType.Action:
                    var menuItem = new ToolStripMenuItem(item.Text)
                    {
                        Tag = item,
                        Enabled = item.Enabled,
                        Checked = item.Checked,
                        Image = item.Icon
                    };
                    
                    // 添加子菜单
                    if (item.SubItems?.Any() == true)
                    {
                        foreach (var subItem in item.SubItems)
                        {
                            var subToolStripItem = CreateToolStripItem(subItem);
                            if (subToolStripItem != null)
                            {
                                menuItem.DropDownItems.Add(subToolStripItem);
                            }
                        }
                    }
                    
                    return menuItem;
                
                case ContextMenuItemType.Separator:
                    return new ToolStripSeparator();
                
                case ContextMenuItemType.Label:
                    return new ToolStripLabel(item.Text)
                    {
                        Tag = item,
                        Enabled = false
                    };
                
                default:
                    return null;
            }
        }
        
        #endregion
        
        #region 事件处理
        
        private void OnContextMenuItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            try
            {
                if (e.ClickedItem?.Tag is ContextMenuItem menuItem)
                {
                    // 执行菜单项操作
                    var result = menuItem.Action?.Invoke(_currentTarget, _component);
                    
                    // 触发菜单项点击事件
                    OnMenuItemClicked(menuItem, _currentTarget, result);
                    
                    // 发布到事件总线
                    EventBusSystem.Instance.Publish(new ContextMenuItemClickEvent(
                        menuItem, _currentTarget, result, _componentId));
                    
                    Logger.Debug($"菜单项点击: {menuItem.Text}");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"菜单项点击处理失败: {ex.Message}");
            }
        }
        
        private void OnContextMenuClosed(object sender, ToolStripDropDownClosedEventArgs e)
        {
            OnMenuClosed(_currentTarget);
        }
        
        private void OnMenuShowing(object target, Point location)
        {
            MenuShowing?.Invoke(this, new ContextMenuShowEventArgs
            {
                Target = target,
                Location = location,
                Timestamp = DateTime.Now
            });
        }
        
        private void OnMenuItemClicked(ContextMenuItem menuItem, object target, object result)
        {
            MenuItemClicked?.Invoke(this, new ContextMenuItemClickEventArgs
            {
                MenuItem = menuItem,
                Target = target,
                Result = result,
                Timestamp = DateTime.Now
            });
        }
        
        private void OnMenuClosed(object target)
        {
            MenuClosed?.Invoke(this, new ContextMenuCloseEventArgs
            {
                Target = target,
                Timestamp = DateTime.Now
            });
            
            _currentTarget = null;
        }
        
        private void OnErrorEvent(ErrorEvent errorEvent)
        {
            Logger.Error($"菜单框架错误: {errorEvent.ErrorMessage}");
        }
        
        #endregion
        
        #region IDisposable实现
        
        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    HideContextMenu();
                    UnsubscribeFromEvents();
                    
                    _contextMenu?.Dispose();
                    _menuProviders.Clear();
                    
                    _disposed = true;
                    Logger.Info($"上下文菜单框架已销毁: {_componentId}");
                }
                catch (Exception ex)
                {
                    Logger.Error($"菜单框架销毁失败: {ex.Message}");
                }
            }
        }
        
        #endregion
    }

    #region 菜单相关接口和类

    /// <summary>
    /// 上下文菜单提供者接口
    /// </summary>
    public interface IContextMenuProvider
    {
        /// <summary>
        /// 获取菜单项
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="component">甘特图组件</param>
        /// <returns>菜单项列表</returns>
        IEnumerable<ContextMenuItem> GetMenuItems(object target, IGanttChartComponent component);
    }

    /// <summary>
    /// 上下文菜单项
    /// </summary>
    public class ContextMenuItem
    {
        /// <summary>菜单项文本</summary>
        public string Text { get; set; }

        /// <summary>菜单项类型</summary>
        public ContextMenuItemType Type { get; set; } = ContextMenuItemType.Action;

        /// <summary>是否启用</summary>
        public bool Enabled { get; set; } = true;

        /// <summary>是否选中</summary>
        public bool Checked { get; set; }

        /// <summary>图标</summary>
        public Image Icon { get; set; }

        /// <summary>快捷键</summary>
        public string ShortcutKey { get; set; }

        /// <summary>工具提示</summary>
        public string ToolTip { get; set; }

        /// <summary>菜单项操作</summary>
        public Func<object, IGanttChartComponent, object> Action { get; set; }

        /// <summary>子菜单项</summary>
        public List<ContextMenuItem> SubItems { get; set; }

        /// <summary>附加数据</summary>
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 创建操作菜单项
        /// </summary>
        public static ContextMenuItem CreateAction(string text, Func<object, IGanttChartComponent, object> action,
            bool enabled = true, Image icon = null, string shortcut = null)
        {
            return new ContextMenuItem
            {
                Text = text,
                Type = ContextMenuItemType.Action,
                Enabled = enabled,
                Icon = icon,
                ShortcutKey = shortcut,
                Action = action
            };
        }

        /// <summary>
        /// 创建分隔符
        /// </summary>
        public static ContextMenuItem CreateSeparator()
        {
            return new ContextMenuItem
            {
                Type = ContextMenuItemType.Separator
            };
        }

        /// <summary>
        /// 创建标签
        /// </summary>
        public static ContextMenuItem CreateLabel(string text)
        {
            return new ContextMenuItem
            {
                Text = text,
                Type = ContextMenuItemType.Label,
                Enabled = false
            };
        }
    }

    /// <summary>
    /// 上下文菜单项类型
    /// </summary>
    public enum ContextMenuItemType
    {
        /// <summary>操作项</summary>
        Action,
        /// <summary>分隔符</summary>
        Separator,
        /// <summary>标签</summary>
        Label
    }

    #endregion

    #region 事件参数类

    /// <summary>
    /// 上下文菜单显示事件参数
    /// </summary>
    public class ContextMenuShowEventArgs : EventArgs
    {
        public object Target { get; set; }
        public Point Location { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 上下文菜单项点击事件参数
    /// </summary>
    public class ContextMenuItemClickEventArgs : EventArgs
    {
        public ContextMenuItem MenuItem { get; set; }
        public object Target { get; set; }
        public object Result { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 上下文菜单关闭事件参数
    /// </summary>
    public class ContextMenuCloseEventArgs : EventArgs
    {
        public object Target { get; set; }
        public DateTime Timestamp { get; set; }
    }

    #endregion

    #region 事件总线事件

    /// <summary>
    /// 上下文菜单显示事件
    /// </summary>
    public class ContextMenuShowEvent : GanttEventBase
    {
        public object Target { get; }
        public Point Location { get; }

        public ContextMenuShowEvent(object target, Point location, string source = null) : base(source)
        {
            Target = target;
            Location = location;
        }
    }

    /// <summary>
    /// 上下文菜单项点击事件
    /// </summary>
    public class ContextMenuItemClickEvent : GanttEventBase
    {
        public ContextMenuItem MenuItem { get; }
        public object Target { get; }
        public object Result { get; }

        public ContextMenuItemClickEvent(ContextMenuItem menuItem, object target, object result, string source = null)
            : base(source)
        {
            MenuItem = menuItem;
            Target = target;
            Result = result;
        }
    }

    #endregion
}
