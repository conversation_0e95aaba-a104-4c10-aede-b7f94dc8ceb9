using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using PBIppt.GanttChart.Models;
using PBIppt.GanttChart.v2.Components;
using PBIppt.Utils;

namespace PBIppt.GanttChart.v2.Interaction.ContextMenu
{
    #region 任务菜单提供者
    
    /// <summary>
    /// 任务上下文菜单提供者
    /// </summary>
    public class TaskContextMenuProvider : IContextMenuProvider
    {
        public IEnumerable<ContextMenuItem> GetMenuItems(object target, IGanttChartComponent component)
        {
            if (!(target is GanttTask task)) return Enumerable.Empty<ContextMenuItem>();
            
            var menuItems = new List<ContextMenuItem>();
            
            // 编辑任务
            menuItems.Add(ContextMenuItem.CreateAction("编辑任务...", (t, c) =>
            {
                return c.StartDirectEdit(t, EditType.Text);
            }, true, null, "F2"));
            
            menuItems.Add(ContextMenuItem.CreateSeparator());
            
            // 设置颜色子菜单
            var colorMenu = ContextMenuItem.CreateAction("设置颜色", null);
            colorMenu.SubItems = new List<ContextMenuItem>
            {
                ContextMenuItem.CreateAction("蓝色", (t, c) => SetTaskColor(t as GanttTask, Color.Blue)),
                ContextMenuItem.CreateAction("绿色", (t, c) => SetTaskColor(t as GanttTask, Color.Green)),
                ContextMenuItem.CreateAction("红色", (t, c) => SetTaskColor(t as GanttTask, Color.Red)),
                ContextMenuItem.CreateAction("橙色", (t, c) => SetTaskColor(t as GanttTask, Color.Orange)),
                ContextMenuItem.CreateAction("紫色", (t, c) => SetTaskColor(t as GanttTask, Color.Purple)),
                ContextMenuItem.CreateSeparator(),
                ContextMenuItem.CreateAction("自定义颜色...", (t, c) => ShowColorPicker(t as GanttTask))
            };
            menuItems.Add(colorMenu);
            
            // 设置状态子菜单
            var statusMenu = ContextMenuItem.CreateAction("设置状态", null);
            statusMenu.SubItems = new List<ContextMenuItem>
            {
                ContextMenuItem.CreateAction("未开始", (t, c) => SetTaskStatus(t as GanttTask, TaskStatus.NotStarted), 
                    true, null, null),
                ContextMenuItem.CreateAction("进行中", (t, c) => SetTaskStatus(t as GanttTask, TaskStatus.InProgress), 
                    true, null, null),
                ContextMenuItem.CreateAction("已完成", (t, c) => SetTaskStatus(t as GanttTask, TaskStatus.Completed), 
                    true, null, null),
                ContextMenuItem.CreateAction("已延期", (t, c) => SetTaskStatus(t as GanttTask, TaskStatus.Delayed), 
                    true, null, null),
                ContextMenuItem.CreateAction("已暂停", (t, c) => SetTaskStatus(t as GanttTask, TaskStatus.OnHold), 
                    true, null, null)
            };
            menuItems.Add(statusMenu);
            
            menuItems.Add(ContextMenuItem.CreateSeparator());
            
            // 依赖关系
            menuItems.Add(ContextMenuItem.CreateAction("添加前置任务", (t, c) =>
            {
                return ShowTaskSelector(t as GanttTask, "选择前置任务");
            }));
            
            menuItems.Add(ContextMenuItem.CreateAction("添加后续任务", (t, c) =>
            {
                return ShowTaskSelector(t as GanttTask, "选择后续任务");
            }));
            
            menuItems.Add(ContextMenuItem.CreateAction("查看依赖关系", (t, c) =>
            {
                return ShowDependencyView(t as GanttTask);
            }));
            
            menuItems.Add(ContextMenuItem.CreateSeparator());
            
            // 任务操作
            menuItems.Add(ContextMenuItem.CreateAction("插入任务(上方)", (t, c) =>
            {
                return InsertTask(t as GanttTask, true, c);
            }));
            
            menuItems.Add(ContextMenuItem.CreateAction("插入任务(下方)", (t, c) =>
            {
                return InsertTask(t as GanttTask, false, c);
            }));
            
            menuItems.Add(ContextMenuItem.CreateAction("删除任务", (t, c) =>
            {
                return DeleteTask(t as GanttTask, c);
            }, true, null, "Delete"));
            
            menuItems.Add(ContextMenuItem.CreateSeparator());
            
            // 剪贴板操作
            menuItems.Add(ContextMenuItem.CreateAction("复制", (t, c) =>
            {
                return CopyTask(t as GanttTask);
            }, true, null, "Ctrl+C"));
            
            menuItems.Add(ContextMenuItem.CreateAction("粘贴", (t, c) =>
            {
                return PasteTask(c);
            }, CanPaste(), null, "Ctrl+V"));
            
            menuItems.Add(ContextMenuItem.CreateSeparator());
            
            // 属性
            menuItems.Add(ContextMenuItem.CreateAction("属性...", (t, c) =>
            {
                return ShowTaskProperties(t as GanttTask);
            }));
            
            return menuItems;
        }
        
        #region 私有方法
        
        private object SetTaskColor(GanttTask task, Color color)
        {
            if (task != null)
            {
                task.Color = ColorTranslator.ToHtml(color);
                Logger.Debug($"设置任务颜色: {task.Name} -> {color.Name}");
                return true;
            }
            return false;
        }
        
        private object SetTaskStatus(GanttTask task, TaskStatus status)
        {
            if (task != null)
            {
                task.Status = status;
                Logger.Debug($"设置任务状态: {task.Name} -> {status}");
                return true;
            }
            return false;
        }
        
        private object ShowColorPicker(GanttTask task)
        {
            // 显示颜色选择器
            Logger.Debug($"显示颜色选择器: {task?.Name}");
            return null;
        }
        
        private object ShowTaskSelector(GanttTask task, string title)
        {
            // 显示任务选择器
            Logger.Debug($"显示任务选择器: {title}");
            return null;
        }
        
        private object ShowDependencyView(GanttTask task)
        {
            // 显示依赖关系视图
            Logger.Debug($"显示依赖关系视图: {task?.Name}");
            return null;
        }
        
        private object InsertTask(GanttTask task, bool above, IGanttChartComponent component)
        {
            // 插入新任务
            var newTask = new GanttTask
            {
                Id = Guid.NewGuid().ToString("N"),
                Name = "新任务",
                StartDate = DateTime.Today,
                EndDate = DateTime.Today.AddDays(1),
                RowIndex = above ? task.RowIndex : task.RowIndex + 1
            };
            
            Logger.Debug($"插入任务: {newTask.Name} {(above ? "上方" : "下方")}");
            return newTask;
        }
        
        private object DeleteTask(GanttTask task, IGanttChartComponent component)
        {
            // 删除任务
            Logger.Debug($"删除任务: {task?.Name}");
            return true;
        }
        
        private object CopyTask(GanttTask task)
        {
            // 复制任务到剪贴板
            Logger.Debug($"复制任务: {task?.Name}");
            return true;
        }
        
        private object PasteTask(IGanttChartComponent component)
        {
            // 从剪贴板粘贴任务
            Logger.Debug("粘贴任务");
            return true;
        }
        
        private bool CanPaste()
        {
            // 检查是否可以粘贴
            return true; // 简化实现
        }
        
        private object ShowTaskProperties(GanttTask task)
        {
            // 显示任务属性对话框
            Logger.Debug($"显示任务属性: {task?.Name}");
            return null;
        }
        
        #endregion
    }
    
    #endregion
    
    #region 里程碑菜单提供者
    
    /// <summary>
    /// 里程碑上下文菜单提供者
    /// </summary>
    public class MilestoneContextMenuProvider : IContextMenuProvider
    {
        public IEnumerable<ContextMenuItem> GetMenuItems(object target, IGanttChartComponent component)
        {
            if (!(target is GanttMilestone milestone)) return Enumerable.Empty<ContextMenuItem>();
            
            var menuItems = new List<ContextMenuItem>();
            
            // 编辑里程碑
            menuItems.Add(ContextMenuItem.CreateAction("编辑里程碑...", (t, c) =>
            {
                return c.StartDirectEdit(t, EditType.Text);
            }, true, null, "F2"));
            
            menuItems.Add(ContextMenuItem.CreateSeparator());
            
            // 设置形状子菜单
            var shapeMenu = ContextMenuItem.CreateAction("设置形状", null);
            shapeMenu.SubItems = new List<ContextMenuItem>
            {
                ContextMenuItem.CreateAction("菱形", (t, c) => SetMilestoneShape(t as GanttMilestone, MilestoneShape.Diamond)),
                ContextMenuItem.CreateAction("圆形", (t, c) => SetMilestoneShape(t as GanttMilestone, MilestoneShape.Circle)),
                ContextMenuItem.CreateAction("三角形", (t, c) => SetMilestoneShape(t as GanttMilestone, MilestoneShape.Triangle)),
                ContextMenuItem.CreateAction("星形", (t, c) => SetMilestoneShape(t as GanttMilestone, MilestoneShape.Star))
            };
            menuItems.Add(shapeMenu);
            
            // 设置颜色子菜单
            var colorMenu = ContextMenuItem.CreateAction("设置颜色", null);
            colorMenu.SubItems = new List<ContextMenuItem>
            {
                ContextMenuItem.CreateAction("蓝色", (t, c) => SetMilestoneColor(t as GanttMilestone, Color.Blue)),
                ContextMenuItem.CreateAction("绿色", (t, c) => SetMilestoneColor(t as GanttMilestone, Color.Green)),
                ContextMenuItem.CreateAction("红色", (t, c) => SetMilestoneColor(t as GanttMilestone, Color.Red)),
                ContextMenuItem.CreateAction("橙色", (t, c) => SetMilestoneColor(t as GanttMilestone, Color.Orange)),
                ContextMenuItem.CreateSeparator(),
                ContextMenuItem.CreateAction("自定义颜色...", (t, c) => ShowColorPicker(t as GanttMilestone))
            };
            menuItems.Add(colorMenu);
            
            menuItems.Add(ContextMenuItem.CreateSeparator());
            
            // 转换操作
            menuItems.Add(ContextMenuItem.CreateAction("转换为任务", (t, c) =>
            {
                return ConvertToTask(t as GanttMilestone, c);
            }));
            
            menuItems.Add(ContextMenuItem.CreateAction("删除里程碑", (t, c) =>
            {
                return DeleteMilestone(t as GanttMilestone, c);
            }, true, null, "Delete"));
            
            menuItems.Add(ContextMenuItem.CreateSeparator());
            
            // 剪贴板操作
            menuItems.Add(ContextMenuItem.CreateAction("复制", (t, c) =>
            {
                return CopyMilestone(t as GanttMilestone);
            }, true, null, "Ctrl+C"));
            
            menuItems.Add(ContextMenuItem.CreateAction("粘贴", (t, c) =>
            {
                return PasteMilestone(c);
            }, CanPaste(), null, "Ctrl+V"));
            
            menuItems.Add(ContextMenuItem.CreateSeparator());
            
            // 属性
            menuItems.Add(ContextMenuItem.CreateAction("属性...", (t, c) =>
            {
                return ShowMilestoneProperties(t as GanttMilestone);
            }));
            
            return menuItems;
        }
        
        #region 私有方法
        
        private object SetMilestoneShape(GanttMilestone milestone, MilestoneShape shape)
        {
            if (milestone != null)
            {
                milestone.Shape = shape;
                Logger.Debug($"设置里程碑形状: {milestone.Name} -> {shape}");
                return true;
            }
            return false;
        }
        
        private object SetMilestoneColor(GanttMilestone milestone, Color color)
        {
            if (milestone != null)
            {
                milestone.Color = ColorTranslator.ToHtml(color);
                Logger.Debug($"设置里程碑颜色: {milestone.Name} -> {color.Name}");
                return true;
            }
            return false;
        }
        
        private object ShowColorPicker(GanttMilestone milestone)
        {
            Logger.Debug($"显示颜色选择器: {milestone?.Name}");
            return null;
        }
        
        private object ConvertToTask(GanttMilestone milestone, IGanttChartComponent component)
        {
            if (milestone != null)
            {
                var task = new GanttTask
                {
                    Id = Guid.NewGuid().ToString("N"),
                    Name = milestone.Name,
                    StartDate = milestone.Date,
                    EndDate = milestone.Date.AddDays(1),
                    Description = milestone.Description
                };
                
                Logger.Debug($"转换里程碑为任务: {milestone.Name}");
                return task;
            }
            return null;
        }
        
        private object DeleteMilestone(GanttMilestone milestone, IGanttChartComponent component)
        {
            Logger.Debug($"删除里程碑: {milestone?.Name}");
            return true;
        }
        
        private object CopyMilestone(GanttMilestone milestone)
        {
            Logger.Debug($"复制里程碑: {milestone?.Name}");
            return true;
        }
        
        private object PasteMilestone(IGanttChartComponent component)
        {
            Logger.Debug("粘贴里程碑");
            return true;
        }
        
        private bool CanPaste()
        {
            return true; // 简化实现
        }
        
        private object ShowMilestoneProperties(GanttMilestone milestone)
        {
            Logger.Debug($"显示里程碑属性: {milestone?.Name}");
            return null;
        }
        
        #endregion
    }
    
    #endregion
    
    #region 默认菜单提供者
    
    /// <summary>
    /// 默认上下文菜单提供者
    /// </summary>
    public class DefaultContextMenuProvider : IContextMenuProvider
    {
        public IEnumerable<ContextMenuItem> GetMenuItems(object target, IGanttChartComponent component)
        {
            var menuItems = new List<ContextMenuItem>();
            
            // 新建操作
            menuItems.Add(ContextMenuItem.CreateAction("新建任务...", (t, c) =>
            {
                return CreateNewTask(c);
            }));
            
            menuItems.Add(ContextMenuItem.CreateAction("新建里程碑...", (t, c) =>
            {
                return CreateNewMilestone(c);
            }));
            
            menuItems.Add(ContextMenuItem.CreateSeparator());
            
            // 剪贴板操作
            menuItems.Add(ContextMenuItem.CreateAction("粘贴", (t, c) =>
            {
                return Paste(c);
            }, CanPaste(), null, "Ctrl+V"));
            
            menuItems.Add(ContextMenuItem.CreateAction("全选", (t, c) =>
            {
                return SelectAll(c);
            }, true, null, "Ctrl+A"));
            
            menuItems.Add(ContextMenuItem.CreateSeparator());
            
            // 视图设置
            var viewMenu = ContextMenuItem.CreateAction("视图设置", null);
            viewMenu.SubItems = new List<ContextMenuItem>
            {
                ContextMenuItem.CreateAction("显示网格", (t, c) => ToggleGrid(c), true),
                ContextMenuItem.CreateAction("显示周末阴影", (t, c) => ToggleWeekendShading(c), true),
                ContextMenuItem.CreateAction("显示今日线", (t, c) => ToggleTodayLine(c), true)
            };
            menuItems.Add(viewMenu);
            
            menuItems.Add(ContextMenuItem.CreateAction("时间范围...", (t, c) =>
            {
                return ShowTimeRangeDialog(c);
            }));
            
            menuItems.Add(ContextMenuItem.CreateSeparator());
            
            // 数据操作
            menuItems.Add(ContextMenuItem.CreateAction("导入数据...", (t, c) =>
            {
                return ImportData(c);
            }));
            
            menuItems.Add(ContextMenuItem.CreateAction("导出数据...", (t, c) =>
            {
                return ExportData(c);
            }));
            
            menuItems.Add(ContextMenuItem.CreateSeparator());
            
            // 甘特图属性
            menuItems.Add(ContextMenuItem.CreateAction("甘特图属性...", (t, c) =>
            {
                return ShowGanttProperties(c);
            }));
            
            return menuItems;
        }
        
        #region 私有方法
        
        private object CreateNewTask(IGanttChartComponent component)
        {
            Logger.Debug("创建新任务");
            return null;
        }
        
        private object CreateNewMilestone(IGanttChartComponent component)
        {
            Logger.Debug("创建新里程碑");
            return null;
        }
        
        private object Paste(IGanttChartComponent component)
        {
            Logger.Debug("粘贴");
            return true;
        }
        
        private bool CanPaste()
        {
            return true; // 简化实现
        }
        
        private object SelectAll(IGanttChartComponent component)
        {
            Logger.Debug("全选");
            return true;
        }
        
        private object ToggleGrid(IGanttChartComponent component)
        {
            Logger.Debug("切换网格显示");
            return true;
        }
        
        private object ToggleWeekendShading(IGanttChartComponent component)
        {
            Logger.Debug("切换周末阴影");
            return true;
        }
        
        private object ToggleTodayLine(IGanttChartComponent component)
        {
            Logger.Debug("切换今日线");
            return true;
        }
        
        private object ShowTimeRangeDialog(IGanttChartComponent component)
        {
            Logger.Debug("显示时间范围对话框");
            return null;
        }
        
        private object ImportData(IGanttChartComponent component)
        {
            Logger.Debug("导入数据");
            return null;
        }
        
        private object ExportData(IGanttChartComponent component)
        {
            Logger.Debug("导出数据");
            return null;
        }
        
        private object ShowGanttProperties(IGanttChartComponent component)
        {
            Logger.Debug("显示甘特图属性");
            return null;
        }
        
        #endregion
    }
    
    #endregion
}
