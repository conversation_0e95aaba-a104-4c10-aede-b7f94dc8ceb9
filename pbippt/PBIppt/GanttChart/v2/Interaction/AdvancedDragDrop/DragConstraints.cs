using System;
using System.Drawing;
using PBIppt.GanttChart.Models;
using PBIppt.Utils;

namespace PBIppt.GanttChart.v2.Interaction.AdvancedDragDrop
{
    #region 约束接口
    
    /// <summary>
    /// 拖拽约束接口
    /// </summary>
    public interface IDragConstraint
    {
        /// <summary>
        /// 约束名称
        /// </summary>
        string Name { get; }
        
        /// <summary>
        /// 是否启用
        /// </summary>
        bool IsEnabled { get; set; }
        
        /// <summary>
        /// 验证拖拽开始
        /// </summary>
        /// <param name="context">拖拽上下文</param>
        /// <returns>是否允许开始拖拽</returns>
        bool ValidateStart(DragContext context);
        
        /// <summary>
        /// 验证拖拽完成
        /// </summary>
        /// <param name="context">拖拽上下文</param>
        /// <returns>验证结果</returns>
        ValidationResult ValidateComplete(DragContext context);
        
        /// <summary>
        /// 应用约束
        /// </summary>
        /// <param name="context">拖拽上下文</param>
        void ApplyConstraint(DragContext context);
    }
    
    #endregion
    
    #region 基础约束类
    
    /// <summary>
    /// 拖拽约束基类
    /// </summary>
    public abstract class DragConstraintBase : IDragConstraint
    {
        public abstract string Name { get; }
        public bool IsEnabled { get; set; } = true;
        
        public virtual bool ValidateStart(DragContext context)
        {
            return IsEnabled;
        }
        
        public abstract ValidationResult ValidateComplete(DragContext context);
        public abstract void ApplyConstraint(DragContext context);
        
        protected void LogConstraintAction(string action, DragContext context)
        {
            Logger.Debug($"约束 {Name}: {action} - 对象: {context.DragObject?.GetType().Name}");
        }
    }
    
    #endregion
    
    #region 具体约束实现
    
    /// <summary>
    /// 时间轴约束
    /// 确保拖拽不超出时间轴范围
    /// </summary>
    public class TimelineConstraint : DragConstraintBase
    {
        public override string Name => "时间轴约束";
        
        public DateTime MinDate { get; set; } = DateTime.Today.AddYears(-1);
        public DateTime MaxDate { get; set; } = DateTime.Today.AddYears(2);
        
        public override ValidationResult ValidateComplete(DragContext context)
        {
            if (!IsEnabled) return ValidationResult.Valid();
            
            try
            {
                if (context.DragObject is GanttTask task)
                {
                    if (task.StartDate < MinDate || task.EndDate > MaxDate)
                    {
                        return ValidationResult.Invalid(
                            $"任务时间超出允许范围 ({MinDate:yyyy-MM-dd} - {MaxDate:yyyy-MM-dd})",
                            "TIMELINE_OUT_OF_RANGE");
                    }
                }
                else if (context.DragObject is GanttMilestone milestone)
                {
                    if (milestone.Date < MinDate || milestone.Date > MaxDate)
                    {
                        return ValidationResult.Invalid(
                            $"里程碑时间超出允许范围 ({MinDate:yyyy-MM-dd} - {MaxDate:yyyy-MM-dd})",
                            "TIMELINE_OUT_OF_RANGE");
                    }
                }
                
                return ValidationResult.Valid();
            }
            catch (Exception ex)
            {
                Logger.Error($"时间轴约束验证失败: {ex.Message}");
                return ValidationResult.Invalid("时间轴约束验证异常", "TIMELINE_VALIDATION_ERROR");
            }
        }
        
        public override void ApplyConstraint(DragContext context)
        {
            if (!IsEnabled) return;
            
            try
            {
                // 限制拖拽范围在时间轴内
                // 这里需要根据布局信息计算像素限制
                LogConstraintAction("应用时间轴约束", context);
            }
            catch (Exception ex)
            {
                Logger.Error($"应用时间轴约束失败: {ex.Message}");
            }
        }
    }
    
    /// <summary>
    /// 工作日约束
    /// 确保任务只能在工作日
    /// </summary>
    public class WorkdayConstraint : DragConstraintBase
    {
        public override string Name => "工作日约束";
        
        public bool AllowWeekends { get; set; } = false;
        public bool AllowHolidays { get; set; } = false;
        
        public override ValidationResult ValidateComplete(DragContext context)
        {
            if (!IsEnabled) return ValidationResult.Valid();
            
            try
            {
                if (context.DragObject is GanttTask task)
                {
                    if (!AllowWeekends && (IsWeekend(task.StartDate) || IsWeekend(task.EndDate)))
                    {
                        return ValidationResult.Invalid("任务不能安排在周末", "WEEKEND_NOT_ALLOWED");
                    }
                }
                else if (context.DragObject is GanttMilestone milestone)
                {
                    if (!AllowWeekends && IsWeekend(milestone.Date))
                    {
                        return ValidationResult.Invalid("里程碑不能安排在周末", "WEEKEND_NOT_ALLOWED");
                    }
                }
                
                return ValidationResult.Valid();
            }
            catch (Exception ex)
            {
                Logger.Error($"工作日约束验证失败: {ex.Message}");
                return ValidationResult.Invalid("工作日约束验证异常", "WORKDAY_VALIDATION_ERROR");
            }
        }
        
        public override void ApplyConstraint(DragContext context)
        {
            if (!IsEnabled) return;
            
            try
            {
                // 自动调整到最近的工作日
                LogConstraintAction("应用工作日约束", context);
            }
            catch (Exception ex)
            {
                Logger.Error($"应用工作日约束失败: {ex.Message}");
            }
        }
        
        private bool IsWeekend(DateTime date)
        {
            return date.DayOfWeek == DayOfWeek.Saturday || date.DayOfWeek == DayOfWeek.Sunday;
        }
    }
    
    /// <summary>
    /// 依赖关系约束
    /// 确保拖拽不违反任务依赖关系
    /// </summary>
    public class DependencyConstraint : DragConstraintBase
    {
        public override string Name => "依赖关系约束";
        
        public override ValidationResult ValidateComplete(DragContext context)
        {
            if (!IsEnabled) return ValidationResult.Valid();
            
            try
            {
                if (context.DragObject is GanttTask task)
                {
                    // 检查前置任务约束
                    foreach (var dependency in task.Dependencies ?? new System.Collections.Generic.List<TaskDependency>())
                    {
                        if (dependency.PredecessorTask != null)
                        {
                            if (task.StartDate <= dependency.PredecessorTask.EndDate)
                            {
                                return ValidationResult.Invalid(
                                    $"任务开始时间不能早于前置任务 '{dependency.PredecessorTask.Name}' 的结束时间",
                                    "DEPENDENCY_VIOLATION");
                            }
                        }
                    }
                    
                    // 检查后续任务约束
                    // 这里需要从项目中查找所有依赖当前任务的任务
                }
                
                return ValidationResult.Valid();
            }
            catch (Exception ex)
            {
                Logger.Error($"依赖关系约束验证失败: {ex.Message}");
                return ValidationResult.Invalid("依赖关系约束验证异常", "DEPENDENCY_VALIDATION_ERROR");
            }
        }
        
        public override void ApplyConstraint(DragContext context)
        {
            if (!IsEnabled) return;
            
            try
            {
                // 自动调整依赖任务的时间
                LogConstraintAction("应用依赖关系约束", context);
            }
            catch (Exception ex)
            {
                Logger.Error($"应用依赖关系约束失败: {ex.Message}");
            }
        }
    }
    
    /// <summary>
    /// 边界约束
    /// 确保拖拽不超出组件边界
    /// </summary>
    public class BoundaryConstraint : DragConstraintBase
    {
        public override string Name => "边界约束";
        
        public Rectangle Bounds { get; set; }
        
        public override ValidationResult ValidateComplete(DragContext context)
        {
            if (!IsEnabled || Bounds.IsEmpty) return ValidationResult.Valid();
            
            try
            {
                // 检查拖拽位置是否在边界内
                if (!Bounds.Contains(context.CurrentPoint))
                {
                    return ValidationResult.Invalid("拖拽位置超出组件边界", "BOUNDARY_EXCEEDED");
                }
                
                return ValidationResult.Valid();
            }
            catch (Exception ex)
            {
                Logger.Error($"边界约束验证失败: {ex.Message}");
                return ValidationResult.Invalid("边界约束验证异常", "BOUNDARY_VALIDATION_ERROR");
            }
        }
        
        public override void ApplyConstraint(DragContext context)
        {
            if (!IsEnabled || Bounds.IsEmpty) return;
            
            try
            {
                // 限制拖拽位置在边界内
                var constrainedPoint = context.CurrentPoint;
                
                if (constrainedPoint.X < Bounds.Left)
                    constrainedPoint.X = Bounds.Left;
                else if (constrainedPoint.X > Bounds.Right)
                    constrainedPoint.X = Bounds.Right;
                
                if (constrainedPoint.Y < Bounds.Top)
                    constrainedPoint.Y = Bounds.Top;
                else if (constrainedPoint.Y > Bounds.Bottom)
                    constrainedPoint.Y = Bounds.Bottom;
                
                if (constrainedPoint != context.CurrentPoint)
                {
                    context.CurrentPoint = constrainedPoint;
                    context.DeltaX = constrainedPoint.X - context.StartPoint.X;
                    context.DeltaY = constrainedPoint.Y - context.StartPoint.Y;
                    LogConstraintAction("限制在边界内", context);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"应用边界约束失败: {ex.Message}");
            }
        }
    }
    
    /// <summary>
    /// 网格吸附约束
    /// 使拖拽对象吸附到网格
    /// </summary>
    public class GridSnapConstraint : DragConstraintBase
    {
        public override string Name => "网格吸附约束";
        
        public int GridSizeX { get; set; } = 10;
        public int GridSizeY { get; set; } = 10;
        public int SnapThreshold { get; set; } = 5;
        
        public override ValidationResult ValidateComplete(DragContext context)
        {
            // 网格吸附不需要验证，总是有效
            return ValidationResult.Valid();
        }
        
        public override void ApplyConstraint(DragContext context)
        {
            if (!IsEnabled) return;
            
            try
            {
                var snappedPoint = SnapToGrid(context.CurrentPoint);
                
                if (snappedPoint != context.CurrentPoint)
                {
                    context.CurrentPoint = snappedPoint;
                    context.DeltaX = snappedPoint.X - context.StartPoint.X;
                    context.DeltaY = snappedPoint.Y - context.StartPoint.Y;
                    LogConstraintAction("吸附到网格", context);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"应用网格吸附约束失败: {ex.Message}");
            }
        }
        
        private Point SnapToGrid(Point point)
        {
            var snappedX = (int)(Math.Round((double)point.X / GridSizeX) * GridSizeX);
            var snappedY = (int)(Math.Round((double)point.Y / GridSizeY) * GridSizeY);
            
            // 只有在吸附阈值内才进行吸附
            if (Math.Abs(point.X - snappedX) <= SnapThreshold)
                point.X = snappedX;
            
            if (Math.Abs(point.Y - snappedY) <= SnapThreshold)
                point.Y = snappedY;
            
            return point;
        }
    }
    
    #endregion
}
