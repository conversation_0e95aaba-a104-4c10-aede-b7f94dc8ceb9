using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using Microsoft.Office.Interop.PowerPoint;
using PBIppt.GanttChart.Models;
using PBIppt.GanttChart.v2.Events;
using PBIppt.GanttChart.v2.Components;
using PBIppt.Utils;
using DrawingPoint = System.Drawing.Point;
using PowerPointPoint = Microsoft.Office.Interop.PowerPoint.Point;

namespace PBIppt.GanttChart.v2.Interaction.AdvancedDragDrop
{
    /// <summary>
    /// 高级拖拽管理器 v2.0
    /// 提供流畅的拖拽体验，实时预览和智能约束
    /// </summary>
    public class AdvancedDragDropManager : IDisposable
    {
        #region 私有字段
        
        private readonly string _componentId;
        private readonly IGanttChartComponent _component;
        private bool _isDragging;
        private DragContext _currentDragContext;
        private readonly List<IDragConstraint> _constraints;
        private readonly DragPreviewRenderer _previewRenderer;
        private readonly DragFeedbackManager _feedbackManager;
        private bool _disposed = false;
        
        #endregion
        
        #region 属性
        
        /// <summary>
        /// 是否正在拖拽
        /// </summary>
        public bool IsDragging => _isDragging;
        
        /// <summary>
        /// 当前拖拽上下文
        /// </summary>
        public DragContext CurrentDragContext => _currentDragContext;
        
        /// <summary>
        /// 拖拽约束列表
        /// </summary>
        public IReadOnlyList<IDragConstraint> Constraints => _constraints.AsReadOnly();
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 拖拽开始事件
        /// </summary>
        public event EventHandler<AdvancedDragStartEventArgs> DragStarted;
        
        /// <summary>
        /// 拖拽更新事件
        /// </summary>
        public event EventHandler<AdvancedDragUpdateEventArgs> DragUpdated;
        
        /// <summary>
        /// 拖拽完成事件
        /// </summary>
        public event EventHandler<AdvancedDragCompleteEventArgs> DragCompleted;
        
        /// <summary>
        /// 拖拽取消事件
        /// </summary>
        public event EventHandler<AdvancedDragCancelEventArgs> DragCanceled;
        
        #endregion
        
        #region 构造函数
        
        /// <summary>
        /// 初始化高级拖拽管理器
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <param name="component">甘特图组件</param>
        public AdvancedDragDropManager(string componentId, IGanttChartComponent component)
        {
            _componentId = componentId ?? throw new ArgumentNullException(nameof(componentId));
            _component = component ?? throw new ArgumentNullException(nameof(component));
            
            _constraints = new List<IDragConstraint>();
            _previewRenderer = new DragPreviewRenderer();
            _feedbackManager = new DragFeedbackManager();
            
            // 添加默认约束
            AddDefaultConstraints();
            
            // 订阅事件
            SubscribeToEvents();
            
            Logger.Info($"高级拖拽管理器已初始化: {componentId}");
        }
        
        #endregion
        
        #region 拖拽操作
        
        /// <summary>
        /// 开始拖拽操作
        /// </summary>
        /// <param name="dragObject">拖拽对象</param>
        /// <param name="startPoint">起始点</param>
        /// <param name="dragType">拖拽类型</param>
        /// <returns>是否成功开始拖拽</returns>
        public bool StartDrag(object dragObject, DrawingPoint startPoint, DragType dragType)
        {
            if (_isDragging || dragObject == null)
                return false;
            
            try
            {
                // 创建拖拽上下文
                _currentDragContext = new DragContext
                {
                    DragObject = dragObject,
                    StartPoint = startPoint,
                    CurrentPoint = startPoint,
                    DragType = dragType,
                    StartTime = DateTime.Now,
                    IsValid = true
                };
                
                // 检查拖拽约束
                if (!ValidateDragStart(_currentDragContext))
                {
                    _currentDragContext = null;
                    return false;
                }
                
                _isDragging = true;
                
                // 初始化预览渲染
                _previewRenderer.Initialize(_currentDragContext);
                
                // 显示拖拽反馈
                _feedbackManager.ShowDragStart(_currentDragContext);
                
                // 触发事件
                OnDragStarted(_currentDragContext);
                
                // 发布到事件总线
                EventBusSystem.Instance.Publish(new DragStartEvent(
                    startPoint, dragObject, dragType.ToString(), _componentId));
                
                Logger.Debug($"开始高级拖拽: {dragType}, 对象: {dragObject.GetType().Name}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"开始拖拽失败: {ex.Message}");
                CleanupDrag();
                return false;
            }
        }
        
        /// <summary>
        /// 更新拖拽位置
        /// </summary>
        /// <param name="currentPoint">当前位置</param>
        public void UpdateDrag(DrawingPoint currentPoint)
        {
            if (!_isDragging || _currentDragContext == null)
                return;
            
            try
            {
                var oldPoint = _currentDragContext.CurrentPoint;
                _currentDragContext.CurrentPoint = currentPoint;
                _currentDragContext.DeltaX = currentPoint.X - _currentDragContext.StartPoint.X;
                _currentDragContext.DeltaY = currentPoint.Y - _currentDragContext.StartPoint.Y;
                
                // 应用约束
                ApplyConstraints(_currentDragContext);
                
                // 更新预览
                _previewRenderer.UpdatePreview(_currentDragContext);
                
                // 更新反馈
                _feedbackManager.UpdateDragFeedback(_currentDragContext);
                
                // 触发事件
                OnDragUpdated(_currentDragContext, oldPoint);
                
                // 发布到事件总线
                EventBusSystem.Instance.Publish(new DragUpdateEvent(
                    currentPoint, _currentDragContext.StartPoint, 
                    _currentDragContext.DragObject, CalculateTimeOffset(_currentDragContext), _componentId));
                
                Logger.Debug($"更新拖拽位置: ({_currentDragContext.DeltaX}, {_currentDragContext.DeltaY})");
            }
            catch (Exception ex)
            {
                Logger.Error($"更新拖拽失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 完成拖拽操作
        /// </summary>
        /// <param name="endPoint">结束点</param>
        /// <returns>拖拽结果</returns>
        public DragResult CompleteDrag(DrawingPoint endPoint)
        {
            if (!_isDragging || _currentDragContext == null)
                return DragResult.Failed("未在拖拽状态");
            
            try
            {
                _currentDragContext.CurrentPoint = endPoint;
                _currentDragContext.EndTime = DateTime.Now;
                
                // 最终验证
                var validationResult = ValidateDragComplete(_currentDragContext);
                if (!validationResult.IsValid)
                {
                    var cancelResult = DragResult.Canceled(validationResult.ErrorMessage);
                    OnDragCanceled(_currentDragContext, validationResult.ErrorMessage);
                    CleanupDrag();
                    return cancelResult;
                }
                
                // 应用拖拽结果
                var applyResult = ApplyDragResult(_currentDragContext);
                
                // 清理预览
                _previewRenderer.ClearPreview();
                
                // 隐藏反馈
                _feedbackManager.HideDragFeedback();
                
                // 触发事件
                OnDragCompleted(_currentDragContext, applyResult);
                
                // 发布到事件总线
                EventBusSystem.Instance.Publish(new DragCompleteEvent(
                    endPoint, _currentDragContext.StartPoint, _currentDragContext.DragObject,
                    applyResult.IsSuccess, applyResult.Message, _componentId));
                
                CleanupDrag();
                
                Logger.Info($"完成拖拽: {(applyResult.IsSuccess ? "成功" : "失败")} - {applyResult.Message}");
                return applyResult;
            }
            catch (Exception ex)
            {
                Logger.Error($"完成拖拽失败: {ex.Message}");
                CleanupDrag();
                return DragResult.Failed($"拖拽操作异常: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 取消拖拽操作
        /// </summary>
        /// <param name="reason">取消原因</param>
        public void CancelDrag(string reason = "用户取消")
        {
            if (!_isDragging || _currentDragContext == null)
                return;
            
            try
            {
                // 清理预览
                _previewRenderer.ClearPreview();
                
                // 隐藏反馈
                _feedbackManager.HideDragFeedback();
                
                // 触发事件
                OnDragCanceled(_currentDragContext, reason);
                
                // 发布到事件总线
                EventBusSystem.Instance.Publish(new DragCancelEvent(
                    _currentDragContext.DragObject, reason, _componentId));
                
                CleanupDrag();
                
                Logger.Debug($"取消拖拽: {reason}");
            }
            catch (Exception ex)
            {
                Logger.Error($"取消拖拽失败: {ex.Message}");
                CleanupDrag();
            }
        }
        
        #endregion
        
        #region 约束管理
        
        /// <summary>
        /// 添加拖拽约束
        /// </summary>
        /// <param name="constraint">约束对象</param>
        public void AddConstraint(IDragConstraint constraint)
        {
            if (constraint != null && !_constraints.Contains(constraint))
            {
                _constraints.Add(constraint);
                Logger.Debug($"添加拖拽约束: {constraint.GetType().Name}");
            }
        }
        
        /// <summary>
        /// 移除拖拽约束
        /// </summary>
        /// <param name="constraint">约束对象</param>
        public void RemoveConstraint(IDragConstraint constraint)
        {
            if (constraint != null && _constraints.Remove(constraint))
            {
                Logger.Debug($"移除拖拽约束: {constraint.GetType().Name}");
            }
        }
        
        /// <summary>
        /// 清除所有约束
        /// </summary>
        public void ClearConstraints()
        {
            _constraints.Clear();
            Logger.Debug("已清除所有拖拽约束");
        }
        
        #endregion
        
        #region 私有方法
        
        private void AddDefaultConstraints()
        {
            // 添加默认约束
            AddConstraint(new TimelineConstraint());
            AddConstraint(new WorkdayConstraint());
            AddConstraint(new DependencyConstraint());
            AddConstraint(new BoundaryConstraint());
        }
        
        private void SubscribeToEvents()
        {
            // 订阅相关事件
            EventBusSystem.Instance.Subscribe<ErrorEvent>(OnErrorEvent, $"{_componentId}_DragErrorHandler");
        }
        
        private void UnsubscribeFromEvents()
        {
            // 取消订阅事件
            EventBusSystem.Instance.Unsubscribe($"{_componentId}_DragErrorHandler");
        }
        
        private bool ValidateDragStart(DragContext context)
        {
            return _constraints.All(c => c.ValidateStart(context));
        }
        
        private ValidationResult ValidateDragComplete(DragContext context)
        {
            foreach (var constraint in _constraints)
            {
                var result = constraint.ValidateComplete(context);
                if (!result.IsValid)
                    return result;
            }
            return ValidationResult.Valid();
        }
        
        private void ApplyConstraints(DragContext context)
        {
            foreach (var constraint in _constraints)
            {
                constraint.ApplyConstraint(context);
            }
        }
        
        private TimeSpan CalculateTimeOffset(DragContext context)
        {
            // 根据拖拽距离计算时间偏移
            // 这里需要布局信息来进行像素到时间的转换
            var pixelsPerDay = 20; // 示例值，实际需要从布局管理器获取
            var days = context.DeltaX / pixelsPerDay;
            return TimeSpan.FromDays(days);
        }
        
        private DragResult ApplyDragResult(DragContext context)
        {
            try
            {
                // 根据拖拽类型应用结果
                switch (context.DragType)
                {
                    case DragType.TaskMove:
                        return ApplyTaskMove(context);
                    case DragType.TaskResize:
                        return ApplyTaskResize(context);
                    case DragType.MilestoneMove:
                        return ApplyMilestoneMove(context);
                    case DragType.TimelineScroll:
                        return ApplyTimelineScroll(context);
                    default:
                        return DragResult.Failed("未知的拖拽类型");
                }
            }
            catch (Exception ex)
            {
                return DragResult.Failed($"应用拖拽结果失败: {ex.Message}");
            }
        }
        
        private DragResult ApplyTaskMove(DragContext context)
        {
            if (context.DragObject is GanttTask task)
            {
                var timeOffset = CalculateTimeOffset(context);
                task.StartDate = task.StartDate.Add(timeOffset);
                task.EndDate = task.EndDate.Add(timeOffset);
                return DragResult.Success("任务时间已更新");
            }
            return DragResult.Failed("无效的任务对象");
        }
        
        private DragResult ApplyTaskResize(DragContext context)
        {
            if (context.DragObject is GanttTask task)
            {
                var timeOffset = CalculateTimeOffset(context);
                task.EndDate = task.EndDate.Add(timeOffset);
                return DragResult.Success("任务持续时间已更新");
            }
            return DragResult.Failed("无效的任务对象");
        }
        
        private DragResult ApplyMilestoneMove(DragContext context)
        {
            if (context.DragObject is GanttMilestone milestone)
            {
                var timeOffset = CalculateTimeOffset(context);
                milestone.Date = milestone.Date.Add(timeOffset);
                return DragResult.Success("里程碑日期已更新");
            }
            return DragResult.Failed("无效的里程碑对象");
        }
        
        private DragResult ApplyTimelineScroll(DragContext context)
        {
            // 应用时间轴滚动
            return DragResult.Success("时间轴已滚动");
        }
        
        private void CleanupDrag()
        {
            _isDragging = false;
            _currentDragContext = null;
        }

        #endregion

        #region 事件处理

        private void OnDragStarted(DragContext context)
        {
            DragStarted?.Invoke(this, new AdvancedDragStartEventArgs
            {
                Context = context,
                Timestamp = DateTime.Now
            });
        }

        private void OnDragUpdated(DragContext context, DrawingPoint oldPoint)
        {
            DragUpdated?.Invoke(this, new AdvancedDragUpdateEventArgs
            {
                Context = context,
                OldPoint = oldPoint,
                Timestamp = DateTime.Now
            });
        }

        private void OnDragCompleted(DragContext context, DragResult result)
        {
            DragCompleted?.Invoke(this, new AdvancedDragCompleteEventArgs
            {
                Context = context,
                Result = result,
                Timestamp = DateTime.Now
            });
        }

        private void OnDragCanceled(DragContext context, string reason)
        {
            DragCanceled?.Invoke(this, new AdvancedDragCancelEventArgs
            {
                Context = context,
                Reason = reason,
                Timestamp = DateTime.Now
            });
        }

        private void OnErrorEvent(ErrorEvent errorEvent)
        {
            Logger.Error($"拖拽管理器错误: {errorEvent.ErrorMessage}");
        }

        #endregion

        #region IDisposable实现

        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    // 取消当前拖拽
                    if (_isDragging)
                    {
                        CancelDrag("组件销毁");
                    }

                    // 清理资源
                    UnsubscribeFromEvents();
                    _previewRenderer?.Dispose();
                    _feedbackManager?.Dispose();
                    _constraints.Clear();

                    _disposed = true;
                    Logger.Info($"高级拖拽管理器已销毁: {_componentId}");
                }
                catch (Exception ex)
                {
                    Logger.Error($"拖拽管理器销毁失败: {ex.Message}");
                }
            }
        }

        #endregion
    }

    #region 支持类和枚举

    /// <summary>
    /// 拖拽类型枚举
    /// </summary>
    public enum DragType
    {
        /// <summary>任务移动</summary>
        TaskMove,
        /// <summary>任务调整大小</summary>
        TaskResize,
        /// <summary>里程碑移动</summary>
        MilestoneMove,
        /// <summary>时间轴滚动</summary>
        TimelineScroll,
        /// <summary>任务排序</summary>
        TaskReorder,
        /// <summary>创建依赖关系</summary>
        CreateDependency
    }

    /// <summary>
    /// 拖拽上下文
    /// </summary>
    public class DragContext
    {
        /// <summary>拖拽对象</summary>
        public object DragObject { get; set; }

        /// <summary>拖拽类型</summary>
        public DragType DragType { get; set; }

        /// <summary>起始点</summary>
        public DrawingPoint StartPoint { get; set; }

        /// <summary>当前点</summary>
        public DrawingPoint CurrentPoint { get; set; }

        /// <summary>X轴偏移</summary>
        public int DeltaX { get; set; }

        /// <summary>Y轴偏移</summary>
        public int DeltaY { get; set; }

        /// <summary>开始时间</summary>
        public DateTime StartTime { get; set; }

        /// <summary>结束时间</summary>
        public DateTime? EndTime { get; set; }

        /// <summary>是否有效</summary>
        public bool IsValid { get; set; }

        /// <summary>约束信息</summary>
        public Dictionary<string, object> ConstraintData { get; set; } = new Dictionary<string, object>();

        /// <summary>拖拽持续时间</summary>
        public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? DateTime.Now.Subtract(StartTime);
    }

    /// <summary>
    /// 拖拽结果
    /// </summary>
    public class DragResult
    {
        /// <summary>是否成功</summary>
        public bool IsSuccess { get; set; }

        /// <summary>结果消息</summary>
        public string Message { get; set; }

        /// <summary>错误代码</summary>
        public string ErrorCode { get; set; }

        /// <summary>附加数据</summary>
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();

        /// <summary>创建成功结果</summary>
        public static DragResult Success(string message = "操作成功")
        {
            return new DragResult { IsSuccess = true, Message = message };
        }

        /// <summary>创建失败结果</summary>
        public static DragResult Failed(string message, string errorCode = null)
        {
            return new DragResult { IsSuccess = false, Message = message, ErrorCode = errorCode };
        }

        /// <summary>创建取消结果</summary>
        public static DragResult Canceled(string message = "操作已取消")
        {
            return new DragResult { IsSuccess = false, Message = message, ErrorCode = "CANCELED" };
        }
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        /// <summary>是否有效</summary>
        public bool IsValid { get; set; }

        /// <summary>错误消息</summary>
        public string ErrorMessage { get; set; }

        /// <summary>错误代码</summary>
        public string ErrorCode { get; set; }

        /// <summary>创建有效结果</summary>
        public static ValidationResult Valid()
        {
            return new ValidationResult { IsValid = true };
        }

        /// <summary>创建无效结果</summary>
        public static ValidationResult Invalid(string message, string errorCode = null)
        {
            return new ValidationResult { IsValid = false, ErrorMessage = message, ErrorCode = errorCode };
        }
    }

    #endregion

    #region 事件参数类

    /// <summary>
    /// 高级拖拽开始事件参数
    /// </summary>
    public class AdvancedDragStartEventArgs : EventArgs
    {
        public DragContext Context { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 高级拖拽更新事件参数
    /// </summary>
    public class AdvancedDragUpdateEventArgs : EventArgs
    {
        public DragContext Context { get; set; }
        public DrawingPoint OldPoint { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 高级拖拽完成事件参数
    /// </summary>
    public class AdvancedDragCompleteEventArgs : EventArgs
    {
        public DragContext Context { get; set; }
        public DragResult Result { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 高级拖拽取消事件参数
    /// </summary>
    public class AdvancedDragCancelEventArgs : EventArgs
    {
        public DragContext Context { get; set; }
        public string Reason { get; set; }
        public DateTime Timestamp { get; set; }
    }

    #endregion
}
