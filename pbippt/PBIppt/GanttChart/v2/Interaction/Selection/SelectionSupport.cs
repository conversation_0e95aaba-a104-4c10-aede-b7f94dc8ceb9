using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using PBIppt.Utils;

namespace PBIppt.GanttChart.v2.Interaction.Selection
{
    #region 枚举定义
    
    /// <summary>
    /// 选择模式
    /// </summary>
    public enum SelectionMode
    {
        /// <summary>替换选择</summary>
        Replace,
        /// <summary>添加到选择</summary>
        Add,
        /// <summary>切换选择状态</summary>
        Toggle,
        /// <summary>从选择中移除</summary>
        Remove
    }
    
    #endregion
    
    #region 选择框类
    
    /// <summary>
    /// 选择框
    /// 提供框选功能的视觉反馈和区域计算
    /// </summary>
    public class SelectionBox : IDisposable
    {
        #region 私有字段
        
        private Point _startPoint;
        private Point _currentPoint;
        private bool _isActive;
        private bool _disposed = false;
        
        #endregion
        
        #region 属性
        
        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive => _isActive;
        
        /// <summary>
        /// 起始点
        /// </summary>
        public Point StartPoint => _startPoint;
        
        /// <summary>
        /// 当前点
        /// </summary>
        public Point CurrentPoint => _currentPoint;
        
        /// <summary>
        /// 选择矩形
        /// </summary>
        public Rectangle SelectionRectangle
        {
            get
            {
                if (!_isActive) return Rectangle.Empty;
                
                var x = Math.Min(_startPoint.X, _currentPoint.X);
                var y = Math.Min(_startPoint.Y, _currentPoint.Y);
                var width = Math.Abs(_currentPoint.X - _startPoint.X);
                var height = Math.Abs(_currentPoint.Y - _startPoint.Y);
                
                return new Rectangle(x, y, width, height);
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 开始选择
        /// </summary>
        /// <param name="startPoint">起始点</param>
        public void StartSelection(Point startPoint)
        {
            _startPoint = startPoint;
            _currentPoint = startPoint;
            _isActive = true;
            
            Logger.Debug($"选择框开始: ({startPoint.X}, {startPoint.Y})");
        }
        
        /// <summary>
        /// 更新选择
        /// </summary>
        /// <param name="currentPoint">当前点</param>
        public void UpdateSelection(Point currentPoint)
        {
            if (!_isActive) return;
            
            _currentPoint = currentPoint;
            
            Logger.Debug($"选择框更新: ({currentPoint.X}, {currentPoint.Y})");
        }
        
        /// <summary>
        /// 结束选择
        /// </summary>
        public void EndSelection()
        {
            _isActive = false;
            _startPoint = Point.Empty;
            _currentPoint = Point.Empty;
            
            Logger.Debug("选择框结束");
        }
        
        #endregion
        
        #region IDisposable实现
        
        public void Dispose()
        {
            if (!_disposed)
            {
                EndSelection();
                _disposed = true;
            }
        }
        
        #endregion
    }
    
    #endregion
    
    #region 选择历史类
    
    /// <summary>
    /// 选择历史
    /// 管理选择操作的撤销重做功能
    /// </summary>
    public class SelectionHistory
    {
        #region 私有字段
        
        private readonly List<object[]> _history;
        private readonly int _maxHistorySize;
        private int _currentIndex;
        
        #endregion
        
        #region 属性
        
        /// <summary>
        /// 历史记录数量
        /// </summary>
        public int Count => _history.Count;
        
        /// <summary>
        /// 当前索引
        /// </summary>
        public int CurrentIndex => _currentIndex;
        
        /// <summary>
        /// 是否可以撤销
        /// </summary>
        public bool CanUndo => _currentIndex > 0;
        
        /// <summary>
        /// 是否可以重做
        /// </summary>
        public bool CanRedo => _currentIndex < _history.Count - 1;
        
        #endregion
        
        #region 构造函数
        
        /// <summary>
        /// 初始化选择历史
        /// </summary>
        /// <param name="maxHistorySize">最大历史记录数量</param>
        public SelectionHistory(int maxHistorySize = 50)
        {
            _maxHistorySize = Math.Max(1, maxHistorySize);
            _history = new List<object[]>();
            _currentIndex = -1;
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 添加选择记录
        /// </summary>
        /// <param name="selection">选择的对象数组</param>
        public void AddSelection(object[] selection)
        {
            try
            {
                // 移除当前索引之后的所有记录
                if (_currentIndex < _history.Count - 1)
                {
                    _history.RemoveRange(_currentIndex + 1, _history.Count - _currentIndex - 1);
                }
                
                // 添加新记录
                _history.Add(selection?.ToArray() ?? new object[0]);
                _currentIndex = _history.Count - 1;
                
                // 限制历史记录大小
                while (_history.Count > _maxHistorySize)
                {
                    _history.RemoveAt(0);
                    _currentIndex--;
                }
                
                Logger.Debug($"添加选择历史: {selection?.Length ?? 0} 个对象, 索引: {_currentIndex}");
            }
            catch (Exception ex)
            {
                Logger.Error($"添加选择历史失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 撤销
        /// </summary>
        /// <returns>上一个选择状态</returns>
        public object[] Undo()
        {
            if (!CanUndo) return null;
            
            try
            {
                _currentIndex--;
                var selection = _history[_currentIndex];
                
                Logger.Debug($"撤销选择: 索引 {_currentIndex}, {selection.Length} 个对象");
                return selection;
            }
            catch (Exception ex)
            {
                Logger.Error($"撤销选择失败: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 重做
        /// </summary>
        /// <returns>下一个选择状态</returns>
        public object[] Redo()
        {
            if (!CanRedo) return null;
            
            try
            {
                _currentIndex++;
                var selection = _history[_currentIndex];
                
                Logger.Debug($"重做选择: 索引 {_currentIndex}, {selection.Length} 个对象");
                return selection;
            }
            catch (Exception ex)
            {
                Logger.Error($"重做选择失败: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 清除历史记录
        /// </summary>
        public void Clear()
        {
            _history.Clear();
            _currentIndex = -1;
            
            Logger.Debug("已清除选择历史");
        }
        
        /// <summary>
        /// 获取历史记录
        /// </summary>
        /// <returns>历史记录数组</returns>
        public object[][] GetHistory()
        {
            return _history.ToArray();
        }
        
        #endregion
    }
    
    #endregion
    
    #region 选择统计类
    
    /// <summary>
    /// 选择统计信息
    /// </summary>
    public class SelectionStatistics
    {
        /// <summary>总数量</summary>
        public int TotalCount { get; set; }
        
        /// <summary>任务数量</summary>
        public int TaskCount { get; set; }
        
        /// <summary>里程碑数量</summary>
        public int MilestoneCount { get; set; }
        
        /// <summary>是否包含混合类型</summary>
        public bool HasMixedTypes { get; set; }
        
        /// <summary>最早日期</summary>
        public DateTime? EarliestDate { get; set; }
        
        /// <summary>最晚日期</summary>
        public DateTime? LatestDate { get; set; }
        
        /// <summary>时间跨度</summary>
        public TimeSpan? TimeSpan
        {
            get
            {
                if (EarliestDate.HasValue && LatestDate.HasValue)
                    return LatestDate.Value - EarliestDate.Value;
                return null;
            }
        }
        
        /// <summary>
        /// 获取统计摘要
        /// </summary>
        /// <returns>统计摘要字符串</returns>
        public string GetSummary()
        {
            if (TotalCount == 0)
                return "未选择任何对象";
            
            var parts = new List<string>();
            
            if (TaskCount > 0)
                parts.Add($"{TaskCount} 个任务");
            
            if (MilestoneCount > 0)
                parts.Add($"{MilestoneCount} 个里程碑");
            
            var summary = $"已选择 {string.Join("、", parts)}";
            
            if (TimeSpan.HasValue)
                summary += $"，时间跨度 {TimeSpan.Value.Days} 天";
            
            return summary;
        }
        
        public override string ToString()
        {
            return GetSummary();
        }
    }
    
    #endregion
    
    #region 事件参数类
    
    /// <summary>
    /// 高级选择改变事件参数
    /// </summary>
    public class AdvancedSelectionChangedEventArgs : EventArgs
    {
        public object[] OldSelection { get; set; }
        public object[] NewSelection { get; set; }
        public SelectionMode SelectionMode { get; set; }
        public SelectionStatistics Statistics { get; set; }
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 框选开始事件参数
    /// </summary>
    public class BoxSelectionStartEventArgs : EventArgs
    {
        public Point StartPoint { get; set; }
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 框选更新事件参数
    /// </summary>
    public class BoxSelectionUpdateEventArgs : EventArgs
    {
        public Point CurrentPoint { get; set; }
        public Rectangle SelectionRectangle { get; set; }
        public object[] ObjectsInBox { get; set; }
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 框选完成事件参数
    /// </summary>
    public class BoxSelectionCompleteEventArgs : EventArgs
    {
        public Point EndPoint { get; set; }
        public Rectangle SelectionRectangle { get; set; }
        public object[] SelectedObjects { get; set; }
        public SelectionMode SelectionMode { get; set; }
        public DateTime Timestamp { get; set; }
    }
    
    #endregion
}
