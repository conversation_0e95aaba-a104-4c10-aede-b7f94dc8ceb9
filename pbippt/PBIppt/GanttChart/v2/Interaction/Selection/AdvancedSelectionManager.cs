using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using PBIppt.GanttChart.Models;
using PBIppt.GanttChart.v2.Components;
using PBIppt.GanttChart.v2.Events;
using PBIppt.Utils;

namespace PBIppt.GanttChart.v2.Interaction.Selection
{
    /// <summary>
    /// 高级选择管理器
    /// 提供多选、范围选择、选择框等高级选择功能
    /// </summary>
    public class AdvancedSelectionManager : IDisposable
    {
        #region 私有字段
        
        private readonly string _componentId;
        private readonly IGanttChartComponent _component;
        private readonly HashSet<object> _selectedObjects;
        private readonly SelectionBox _selectionBox;
        private readonly SelectionHistory _selectionHistory;
        private object _lastSelectedObject;
        private Point _selectionStartPoint;
        private bool _isBoxSelecting;
        private bool _disposed = false;
        
        #endregion
        
        #region 属性
        
        /// <summary>
        /// 选中的对象数量
        /// </summary>
        public int SelectedCount => _selectedObjects.Count;
        
        /// <summary>
        /// 是否有选择
        /// </summary>
        public bool HasSelection => _selectedObjects.Count > 0;
        
        /// <summary>
        /// 是否正在框选
        /// </summary>
        public bool IsBoxSelecting => _isBoxSelecting;
        
        /// <summary>
        /// 最后选择的对象
        /// </summary>
        public object LastSelectedObject => _lastSelectedObject;
        
        /// <summary>
        /// 选择框
        /// </summary>
        public SelectionBox SelectionBox => _selectionBox;
        
        /// <summary>
        /// 选择历史
        /// </summary>
        public SelectionHistory SelectionHistory => _selectionHistory;
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 选择改变事件
        /// </summary>
        public event EventHandler<AdvancedSelectionChangedEventArgs> SelectionChanged;
        
        /// <summary>
        /// 框选开始事件
        /// </summary>
        public event EventHandler<BoxSelectionStartEventArgs> BoxSelectionStarted;
        
        /// <summary>
        /// 框选更新事件
        /// </summary>
        public event EventHandler<BoxSelectionUpdateEventArgs> BoxSelectionUpdated;
        
        /// <summary>
        /// 框选完成事件
        /// </summary>
        public event EventHandler<BoxSelectionCompleteEventArgs> BoxSelectionCompleted;
        
        #endregion
        
        #region 构造函数
        
        /// <summary>
        /// 初始化高级选择管理器
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <param name="component">甘特图组件</param>
        public AdvancedSelectionManager(string componentId, IGanttChartComponent component)
        {
            _componentId = componentId ?? throw new ArgumentNullException(nameof(componentId));
            _component = component ?? throw new ArgumentNullException(nameof(component));
            
            _selectedObjects = new HashSet<object>();
            _selectionBox = new SelectionBox();
            _selectionHistory = new SelectionHistory(50); // 最多保存50个历史记录
            
            // 订阅事件
            SubscribeToEvents();
            
            Logger.Info($"高级选择管理器已初始化: {componentId}");
        }
        
        #endregion
        
        #region 基础选择操作
        
        /// <summary>
        /// 选择对象
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="mode">选择模式</param>
        public void SelectObject(object target, SelectionMode mode = SelectionMode.Replace)
        {
            if (target == null) return;
            
            try
            {
                var oldSelection = GetSelectedObjects();
                
                switch (mode)
                {
                    case SelectionMode.Replace:
                        _selectedObjects.Clear();
                        _selectedObjects.Add(target);
                        break;
                        
                    case SelectionMode.Add:
                        _selectedObjects.Add(target);
                        break;
                        
                    case SelectionMode.Toggle:
                        if (_selectedObjects.Contains(target))
                            _selectedObjects.Remove(target);
                        else
                            _selectedObjects.Add(target);
                        break;
                        
                    case SelectionMode.Remove:
                        _selectedObjects.Remove(target);
                        break;
                }
                
                _lastSelectedObject = target;
                
                // 保存到历史记录
                _selectionHistory.AddSelection(GetSelectedObjects());
                
                // 触发选择改变事件
                OnSelectionChanged(oldSelection, GetSelectedObjects(), mode);
                
                Logger.Debug($"选择对象: {target.GetType().Name}, 模式: {mode}, 总数: {_selectedObjects.Count}");
            }
            catch (Exception ex)
            {
                Logger.Error($"选择对象失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 选择多个对象
        /// </summary>
        /// <param name="targets">目标对象列表</param>
        /// <param name="mode">选择模式</param>
        public void SelectObjects(IEnumerable<object> targets, SelectionMode mode = SelectionMode.Replace)
        {
            if (targets == null) return;
            
            try
            {
                var targetList = targets.ToList();
                if (targetList.Count == 0) return;
                
                var oldSelection = GetSelectedObjects();
                
                switch (mode)
                {
                    case SelectionMode.Replace:
                        _selectedObjects.Clear();
                        foreach (var target in targetList)
                            _selectedObjects.Add(target);
                        break;
                        
                    case SelectionMode.Add:
                        foreach (var target in targetList)
                            _selectedObjects.Add(target);
                        break;
                        
                    case SelectionMode.Toggle:
                        foreach (var target in targetList)
                        {
                            if (_selectedObjects.Contains(target))
                                _selectedObjects.Remove(target);
                            else
                                _selectedObjects.Add(target);
                        }
                        break;
                        
                    case SelectionMode.Remove:
                        foreach (var target in targetList)
                            _selectedObjects.Remove(target);
                        break;
                }
                
                _lastSelectedObject = targetList.LastOrDefault();
                
                // 保存到历史记录
                _selectionHistory.AddSelection(GetSelectedObjects());
                
                // 触发选择改变事件
                OnSelectionChanged(oldSelection, GetSelectedObjects(), mode);
                
                Logger.Debug($"选择多个对象: {targetList.Count} 个, 模式: {mode}, 总数: {_selectedObjects.Count}");
            }
            catch (Exception ex)
            {
                Logger.Error($"选择多个对象失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 范围选择
        /// </summary>
        /// <param name="startObject">起始对象</param>
        /// <param name="endObject">结束对象</param>
        /// <param name="mode">选择模式</param>
        public void SelectRange(object startObject, object endObject, SelectionMode mode = SelectionMode.Replace)
        {
            if (startObject == null || endObject == null) return;
            
            try
            {
                var rangeObjects = GetObjectsInRange(startObject, endObject);
                SelectObjects(rangeObjects, mode);
                
                Logger.Debug($"范围选择: 从 {startObject.GetType().Name} 到 {endObject.GetType().Name}, 共 {rangeObjects.Count} 个对象");
            }
            catch (Exception ex)
            {
                Logger.Error($"范围选择失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 清除选择
        /// </summary>
        public void ClearSelection()
        {
            if (_selectedObjects.Count == 0) return;
            
            try
            {
                var oldSelection = GetSelectedObjects();
                _selectedObjects.Clear();
                _lastSelectedObject = null;
                
                // 保存到历史记录
                _selectionHistory.AddSelection(new object[0]);
                
                // 触发选择改变事件
                OnSelectionChanged(oldSelection, new object[0], SelectionMode.Replace);
                
                Logger.Debug("已清除所有选择");
            }
            catch (Exception ex)
            {
                Logger.Error($"清除选择失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 全选
        /// </summary>
        public void SelectAll()
        {
            try
            {
                var allObjects = GetAllSelectableObjects();
                SelectObjects(allObjects, SelectionMode.Replace);
                
                Logger.Debug($"全选: {allObjects.Count} 个对象");
            }
            catch (Exception ex)
            {
                Logger.Error($"全选失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 反选
        /// </summary>
        public void InvertSelection()
        {
            try
            {
                var allObjects = GetAllSelectableObjects();
                var currentSelection = GetSelectedObjects();
                var invertedSelection = allObjects.Except(currentSelection).ToList();
                
                SelectObjects(invertedSelection, SelectionMode.Replace);
                
                Logger.Debug($"反选: {invertedSelection.Count} 个对象");
            }
            catch (Exception ex)
            {
                Logger.Error($"反选失败: {ex.Message}");
            }
        }
        
        #endregion
        
        #region 框选操作
        
        /// <summary>
        /// 开始框选
        /// </summary>
        /// <param name="startPoint">起始点</param>
        public void StartBoxSelection(Point startPoint)
        {
            try
            {
                _selectionStartPoint = startPoint;
                _isBoxSelecting = true;
                
                _selectionBox.StartSelection(startPoint);
                
                // 触发框选开始事件
                OnBoxSelectionStarted(startPoint);
                
                Logger.Debug($"开始框选: ({startPoint.X}, {startPoint.Y})");
            }
            catch (Exception ex)
            {
                Logger.Error($"开始框选失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 更新框选
        /// </summary>
        /// <param name="currentPoint">当前点</param>
        public void UpdateBoxSelection(Point currentPoint)
        {
            if (!_isBoxSelecting) return;
            
            try
            {
                _selectionBox.UpdateSelection(currentPoint);
                
                // 获取框选区域内的对象
                var selectionRect = _selectionBox.SelectionRectangle;
                var objectsInBox = GetObjectsInRectangle(selectionRect);
                
                // 触发框选更新事件
                OnBoxSelectionUpdated(currentPoint, selectionRect, objectsInBox);
                
                Logger.Debug($"更新框选: ({currentPoint.X}, {currentPoint.Y}), 对象数: {objectsInBox.Count}");
            }
            catch (Exception ex)
            {
                Logger.Error($"更新框选失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 完成框选
        /// </summary>
        /// <param name="endPoint">结束点</param>
        /// <param name="mode">选择模式</param>
        public void CompleteBoxSelection(Point endPoint, SelectionMode mode = SelectionMode.Replace)
        {
            if (!_isBoxSelecting) return;
            
            try
            {
                _selectionBox.UpdateSelection(endPoint);
                
                // 获取最终框选区域内的对象
                var selectionRect = _selectionBox.SelectionRectangle;
                var objectsInBox = GetObjectsInRectangle(selectionRect);
                
                // 应用选择
                if (objectsInBox.Count > 0)
                {
                    SelectObjects(objectsInBox, mode);
                }
                
                // 清理框选状态
                _selectionBox.EndSelection();
                _isBoxSelecting = false;
                
                // 触发框选完成事件
                OnBoxSelectionCompleted(endPoint, selectionRect, objectsInBox, mode);
                
                Logger.Debug($"完成框选: 选择了 {objectsInBox.Count} 个对象");
            }
            catch (Exception ex)
            {
                Logger.Error($"完成框选失败: {ex.Message}");
                CancelBoxSelection();
            }
        }
        
        /// <summary>
        /// 取消框选
        /// </summary>
        public void CancelBoxSelection()
        {
            if (!_isBoxSelecting) return;
            
            try
            {
                _selectionBox.EndSelection();
                _isBoxSelecting = false;
                
                Logger.Debug("取消框选");
            }
            catch (Exception ex)
            {
                Logger.Error($"取消框选失败: {ex.Message}");
            }
        }
        
        #endregion
        
        #region 选择历史
        
        /// <summary>
        /// 撤销选择
        /// </summary>
        /// <returns>是否成功撤销</returns>
        public bool UndoSelection()
        {
            try
            {
                var previousSelection = _selectionHistory.Undo();
                if (previousSelection != null)
                {
                    var oldSelection = GetSelectedObjects();
                    _selectedObjects.Clear();
                    foreach (var obj in previousSelection)
                        _selectedObjects.Add(obj);
                    
                    _lastSelectedObject = previousSelection.LastOrDefault();
                    
                    OnSelectionChanged(oldSelection, previousSelection, SelectionMode.Replace);
                    
                    Logger.Debug($"撤销选择: 恢复到 {previousSelection.Length} 个对象");
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error($"撤销选择失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 重做选择
        /// </summary>
        /// <returns>是否成功重做</returns>
        public bool RedoSelection()
        {
            try
            {
                var nextSelection = _selectionHistory.Redo();
                if (nextSelection != null)
                {
                    var oldSelection = GetSelectedObjects();
                    _selectedObjects.Clear();
                    foreach (var obj in nextSelection)
                        _selectedObjects.Add(obj);
                    
                    _lastSelectedObject = nextSelection.LastOrDefault();
                    
                    OnSelectionChanged(oldSelection, nextSelection, SelectionMode.Replace);
                    
                    Logger.Debug($"重做选择: 恢复到 {nextSelection.Length} 个对象");
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error($"重做选择失败: {ex.Message}");
                return false;
            }
        }
        
        #endregion

        #region 查询方法

        /// <summary>
        /// 获取选中的对象
        /// </summary>
        /// <returns>选中的对象数组</returns>
        public object[] GetSelectedObjects()
        {
            return _selectedObjects.ToArray();
        }

        /// <summary>
        /// 检查对象是否被选中
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <returns>是否被选中</returns>
        public bool IsSelected(object target)
        {
            return target != null && _selectedObjects.Contains(target);
        }

        /// <summary>
        /// 获取选中的任务
        /// </summary>
        /// <returns>选中的任务列表</returns>
        public GanttTask[] GetSelectedTasks()
        {
            return _selectedObjects.OfType<GanttTask>().ToArray();
        }

        /// <summary>
        /// 获取选中的里程碑
        /// </summary>
        /// <returns>选中的里程碑列表</returns>
        public GanttMilestone[] GetSelectedMilestones()
        {
            return _selectedObjects.OfType<GanttMilestone>().ToArray();
        }

        /// <summary>
        /// 获取选择统计信息
        /// </summary>
        /// <returns>选择统计</returns>
        public SelectionStatistics GetSelectionStatistics()
        {
            var tasks = GetSelectedTasks();
            var milestones = GetSelectedMilestones();

            return new SelectionStatistics
            {
                TotalCount = _selectedObjects.Count,
                TaskCount = tasks.Length,
                MilestoneCount = milestones.Length,
                HasMixedTypes = tasks.Length > 0 && milestones.Length > 0,
                EarliestDate = GetEarliestDate(tasks, milestones),
                LatestDate = GetLatestDate(tasks, milestones)
            };
        }

        #endregion

        #region 辅助方法

        private void SubscribeToEvents()
        {
            // 订阅相关事件
            EventBusSystem.Instance.Subscribe<MouseClickEvent>(OnMouseClickEvent, $"{_componentId}_SelectionMouseHandler");
            EventBusSystem.Instance.Subscribe<ErrorEvent>(OnErrorEvent, $"{_componentId}_SelectionErrorHandler");
        }

        private void UnsubscribeFromEvents()
        {
            // 取消订阅事件
            EventBusSystem.Instance.Unsubscribe($"{_componentId}_SelectionMouseHandler");
            EventBusSystem.Instance.Unsubscribe($"{_componentId}_SelectionErrorHandler");
        }

        private List<object> GetAllSelectableObjects()
        {
            var project = _component.Project;
            if (project == null) return new List<object>();

            var allObjects = new List<object>();
            allObjects.AddRange(project.Tasks.Cast<object>());
            allObjects.AddRange(project.Milestones.Cast<object>());

            return allObjects;
        }

        private List<object> GetObjectsInRange(object startObject, object endObject)
        {
            var allObjects = GetAllSelectableObjects();
            var startIndex = allObjects.IndexOf(startObject);
            var endIndex = allObjects.IndexOf(endObject);

            if (startIndex == -1 || endIndex == -1)
                return new List<object>();

            // 确保起始索引小于结束索引
            if (startIndex > endIndex)
            {
                var temp = startIndex;
                startIndex = endIndex;
                endIndex = temp;
            }

            var rangeObjects = new List<object>();
            for (int i = startIndex; i <= endIndex; i++)
            {
                rangeObjects.Add(allObjects[i]);
            }

            return rangeObjects;
        }

        private List<object> GetObjectsInRectangle(Rectangle rectangle)
        {
            var objectsInRect = new List<object>();
            var project = _component.Project;
            if (project == null) return objectsInRect;

            // 检查任务
            foreach (var task in project.Tasks)
            {
                var taskBounds = GetObjectBounds(task);
                if (rectangle.IntersectsWith(taskBounds))
                {
                    objectsInRect.Add(task);
                }
            }

            // 检查里程碑
            foreach (var milestone in project.Milestones)
            {
                var milestoneBounds = GetObjectBounds(milestone);
                if (rectangle.IntersectsWith(milestoneBounds))
                {
                    objectsInRect.Add(milestone);
                }
            }

            return objectsInRect;
        }

        private Rectangle GetObjectBounds(object obj)
        {
            // 这里需要从布局管理器获取对象的实际边界
            // 暂时使用占位符实现
            switch (obj)
            {
                case GanttTask task:
                    return new Rectangle(100, 50 + task.RowIndex * 30, 200, 25);
                case GanttMilestone milestone:
                    return new Rectangle(150, 50 + milestone.RowIndex * 30, 100, 25);
                default:
                    return Rectangle.Empty;
            }
        }

        private DateTime? GetEarliestDate(GanttTask[] tasks, GanttMilestone[] milestones)
        {
            var dates = new List<DateTime>();

            foreach (var task in tasks)
                dates.Add(task.StartDate);

            foreach (var milestone in milestones)
                dates.Add(milestone.Date);

            return dates.Count > 0 ? dates.Min() : (DateTime?)null;
        }

        private DateTime? GetLatestDate(GanttTask[] tasks, GanttMilestone[] milestones)
        {
            var dates = new List<DateTime>();

            foreach (var task in tasks)
                dates.Add(task.EndDate);

            foreach (var milestone in milestones)
                dates.Add(milestone.Date);

            return dates.Count > 0 ? dates.Max() : (DateTime?)null;
        }

        #endregion

        #region 事件处理

        private void OnMouseClickEvent(MouseClickEvent mouseClickEvent)
        {
            try
            {
                // 处理鼠标点击选择逻辑
                if (mouseClickEvent.Button == MouseButtons.Left)
                {
                    var mode = DetermineSelectionMode(mouseClickEvent);

                    if (mouseClickEvent.Target != null)
                    {
                        SelectObject(mouseClickEvent.Target, mode);
                    }
                    else if (mode == SelectionMode.Replace)
                    {
                        ClearSelection();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"处理鼠标点击选择失败: {ex.Message}");
            }
        }

        private SelectionMode DetermineSelectionMode(MouseClickEvent mouseClickEvent)
        {
            // 根据修饰键确定选择模式
            // 这里需要从事件中获取修饰键状态
            // 暂时使用简化逻辑
            return SelectionMode.Replace;
        }

        private void OnSelectionChanged(object[] oldSelection, object[] newSelection, SelectionMode mode)
        {
            SelectionChanged?.Invoke(this, new AdvancedSelectionChangedEventArgs
            {
                OldSelection = oldSelection,
                NewSelection = newSelection,
                SelectionMode = mode,
                Statistics = GetSelectionStatistics(),
                Timestamp = DateTime.Now
            });

            // 发布到事件总线
            EventBusSystem.Instance.Publish(new SelectionChangedEvent(
                oldSelection, newSelection, newSelection.Length > 1, _componentId));
        }

        private void OnBoxSelectionStarted(Point startPoint)
        {
            BoxSelectionStarted?.Invoke(this, new BoxSelectionStartEventArgs
            {
                StartPoint = startPoint,
                Timestamp = DateTime.Now
            });
        }

        private void OnBoxSelectionUpdated(Point currentPoint, Rectangle selectionRect, List<object> objectsInBox)
        {
            BoxSelectionUpdated?.Invoke(this, new BoxSelectionUpdateEventArgs
            {
                CurrentPoint = currentPoint,
                SelectionRectangle = selectionRect,
                ObjectsInBox = objectsInBox.ToArray(),
                Timestamp = DateTime.Now
            });
        }

        private void OnBoxSelectionCompleted(Point endPoint, Rectangle selectionRect, List<object> selectedObjects, SelectionMode mode)
        {
            BoxSelectionCompleted?.Invoke(this, new BoxSelectionCompleteEventArgs
            {
                EndPoint = endPoint,
                SelectionRectangle = selectionRect,
                SelectedObjects = selectedObjects.ToArray(),
                SelectionMode = mode,
                Timestamp = DateTime.Now
            });
        }

        private void OnErrorEvent(ErrorEvent errorEvent)
        {
            Logger.Error($"高级选择管理器错误: {errorEvent.ErrorMessage}");
        }

        #endregion

        #region IDisposable实现

        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    UnsubscribeFromEvents();
                    ClearSelection();
                    _selectionBox?.Dispose();
                    _selectionHistory?.Clear();

                    _disposed = true;
                    Logger.Info($"高级选择管理器已销毁: {_componentId}");
                }
                catch (Exception ex)
                {
                    Logger.Error($"高级选择管理器销毁失败: {ex.Message}");
                }
            }
        }

        #endregion
    }
}
