using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using Microsoft.Office.Interop.PowerPoint;
using PBIppt.GanttChart.Models;
using PBIppt.GanttChart.Utils;
using PBIppt.GanttChart.v2.Events;
using PBIppt.GanttChart.v2.Components;
using PBIppt.GanttChart.v2.Rendering.SmartLayout;

namespace PBIppt.GanttChart.v2.Integration
{
    /// <summary>
    /// PowerPoint深度集成 - 提供与PowerPoint的深度集成功能
    /// 实现ThinkCell级别的PowerPoint原生体验
    /// </summary>
    public class PowerPointIntegration
    {
        private readonly EventBusSystem _eventBus;
        private readonly SmartLayoutAlgorithm _smartLayout;
        private readonly Dictionary<string, IGanttChartComponent> _activeComponents;

        /// <summary>
        /// 集成配置
        /// </summary>
        public PowerPointIntegrationConfig Config { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public PowerPointIntegration()
        {
            _eventBus = EventBusSystem.Instance;
            _smartLayout = new SmartLayoutAlgorithm();
            _activeComponents = new Dictionary<string, IGanttChartComponent>();
            Config = PowerPointIntegrationConfig.CreateDefault();

            // 订阅PowerPoint事件
            SubscribeToPowerPointEvents();
        }

        /// <summary>
        /// 创建智能甘特图组件
        /// </summary>
        /// <param name="slide">目标幻灯片</param>
        /// <param name="project">甘特图项目</param>
        /// <param name="position">位置信息</param>
        /// <returns>甘特图组件</returns>
        public IGanttChartComponent CreateSmartGanttChart(Slide slide, GanttProject project, 
            ComponentPosition position = null)
        {
            try
            {
                _eventBus.Publish(new ComponentCreationStarted
                {
                    ComponentType = "SmartGanttChart",
                    SlideId = slide.SlideID.ToString(),
                    ProjectId = project.Id
                });

                // 1. 分析幻灯片环境
                var slideAnalysis = AnalyzeSlideEnvironment(slide);

                // 2. 创建布局约束
                var constraints = CreateLayoutConstraints(slide, position, slideAnalysis);

                // 3. 计算智能布局
                var layoutResult = _smartLayout.CalculateOptimalLayout(project, constraints);

                // 4. 创建组件
                var component = new GanttChartComponent();
                
                // 5. 应用PowerPoint特定配置
                ApplyPowerPointSpecificSettings(component, slide, layoutResult);

                // 6. 初始化组件
                var bounds = new Rectangle(0, 0, (int)layoutResult.Layout.TotalWidth, (int)layoutResult.Layout.TotalHeight);
                component.Initialize(slide, project, bounds);

                // 7. 注册组件
                var componentId = RegisterComponent(component, slide);

                // 8. 设置PowerPoint集成功能
                SetupPowerPointIntegration(component, slide, componentId);

                _eventBus.Publish(new ComponentCreationCompleted
                {
                    ComponentId = componentId,
                    OptimizationScore = layoutResult.OptimizationScore,
                    CreationTime = DateTime.Now
                });

                return component;
            }
            catch (Exception ex)
            {
                _eventBus.Publish(new ComponentCreationFailed
                {
                    Error = ex.Message,
                    ProjectId = project.Id
                });
                throw;
            }
        }

        /// <summary>
        /// 分析幻灯片环境
        /// </summary>
        private SlideAnalysis AnalyzeSlideEnvironment(Slide slide)
        {
            var analysis = new SlideAnalysis
            {
                SlideWidth = slide.Master.Width,
                SlideHeight = slide.Master.Height,
                ExistingShapeCount = slide.Shapes.Count,
                SlideLayout = slide.Layout.ToString(),
                Theme = slide.Design.Name,
                HasTitle = HasTitlePlaceholder(slide),
                HasContent = HasContentPlaceholder(slide),
                AvailableSpace = CalculateAvailableSpace(slide)
            };

            // 分析现有内容
            analysis.ContentAnalysis = AnalyzeExistingContent(slide);

            // 检测冲突
            analysis.PotentialConflicts = DetectPotentialConflicts(slide);

            return analysis;
        }

        /// <summary>
        /// 创建布局约束
        /// </summary>
        private LayoutConstraints CreateLayoutConstraints(Slide slide, ComponentPosition position, 
            SlideAnalysis analysis)
        {
            var constraints = new LayoutConstraints();

            if (position != null)
            {
                // 使用指定位置
                constraints.MinWidth = position.Width * 0.8f;
                constraints.MaxWidth = position.Width * 1.2f;
                constraints.MinHeight = position.Height * 0.8f;
                constraints.MaxHeight = position.Height * 1.2f;
            }
            else
            {
                // 根据可用空间自动计算
                var availableSpace = analysis.AvailableSpace;
                constraints.MinWidth = availableSpace.Width * 0.6f;
                constraints.MaxWidth = availableSpace.Width * 0.95f;
                constraints.MinHeight = availableSpace.Height * 0.6f;
                constraints.MaxHeight = availableSpace.Height * 0.95f;
            }

            // 应用幻灯片特定约束
            ApplySlideSpecificConstraints(constraints, slide, analysis);

            return constraints;
        }

        /// <summary>
        /// 应用PowerPoint特定设置
        /// </summary>
        private void ApplyPowerPointSpecificSettings(IGanttChartComponent component, Slide slide, 
            SmartLayoutResult layoutResult)
        {
            // 设置PowerPoint原生行为
            if (component is GanttChartComponent ganttComponent)
            {
                // 启用PowerPoint集成功能
                ganttComponent.EnablePowerPointIntegration();

                // 设置智能布局
                ganttComponent.SetSmartLayout(true);
                
                // 应用主题适配
                ApplySlideTheme(ganttComponent, slide);
                
                // 设置性能优化
                ApplyPerformanceOptimizations(ganttComponent, layoutResult.PerformanceMetrics);
            }
        }

        /// <summary>
        /// 设置PowerPoint集成功能
        /// </summary>
        private void SetupPowerPointIntegration(IGanttChartComponent component, Slide slide, string componentId)
        {
            // 1. 注册PowerPoint事件处理
            RegisterPowerPointEventHandlers(component, slide);

            // 2. 设置右键菜单集成
            SetupContextMenuIntegration(component, slide);

            // 3. 启用拖拽集成
            SetupDragDropIntegration(component, slide);

            // 4. 设置选择同步
            SetupSelectionSync(component, slide);

            // 5. 启用撤销重做集成
            SetupUndoRedoIntegration(component, slide);
        }

        /// <summary>
        /// 注册组件
        /// </summary>
        private string RegisterComponent(IGanttChartComponent component, Slide slide)
        {
            var componentId = component.ComponentId; // 使用组件自己的ID
            _activeComponents[componentId] = component;

            // 组件ID在构造时已设置，无需再次设置

            return componentId;
        }

        /// <summary>
        /// 订阅PowerPoint事件
        /// </summary>
        private void SubscribeToPowerPointEvents()
        {
            try
            {
                // 获取PowerPoint应用程序实例
                var app = Globals.ThisAddIn.Application;

                // 订阅幻灯片选择变化事件
                app.SlideSelectionChanged += OnSlideSelectionChanged;

                // 订阅窗口激活事件
                app.WindowActivate += OnWindowActivate;

                // 订阅窗口大小变化事件
                app.WindowSelectionChange += OnWindowSelectionChange;

                // 订阅演示文稿关闭事件
                app.PresentationClose += OnPresentationClose;
            }
            catch (Exception ex)
            {
                _eventBus.Publish(new ErrorEvent(ex, "PowerPoint事件订阅"));
            }
        }

        /// <summary>
        /// 处理幻灯片选择变化
        /// </summary>
        private void OnSlideSelectionChanged(SlideRange selection)
        {
            try
            {
                foreach (Slide slide in selection)
                {
                    var components = GetComponentsOnSlide(slide);
                    foreach (var component in components)
                    {
                        // 通知组件幻灯片被选中
                        if (component is GanttChartComponent ganttComponent)
                        {
                            ganttComponent.OnSlideActivated();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _eventBus.Publish(new ErrorEvent(ex, "幻灯片选择变化处理"));
            }
        }

        /// <summary>
        /// 处理窗口激活
        /// </summary>
        private void OnWindowActivate(Presentation presentation, DocumentWindow window)
        {
            try
            {
                // 激活相关组件
                var components = GetComponentsInPresentation(presentation);
                foreach (var component in components)
                {
                    if (component is GanttChartComponent ganttComponent)
                    {
                        ganttComponent.OnWindowActivated();
                    }
                }
            }
            catch (Exception ex)
            {
                _eventBus.Publish(new ErrorEvent(ex, "窗口激活处理"));
            }
        }

        /// <summary>
        /// 处理窗口选择变化
        /// </summary>
        private void OnWindowSelectionChange(Selection selection)
        {
            try
            {
                // 检查选择的对象是否包含甘特图组件
                if (selection.Type == PpSelectionType.ppSelectionShapes)
                {
                    foreach (Microsoft.Office.Interop.PowerPoint.Shape shape in selection.ShapeRange)
                    {
                        var component = GetComponentByShape(shape);
                        if (component != null)
                        {
                            // 通知组件被选中
                            if (component is GanttChartComponent ganttComponent)
                            {
                                ganttComponent.OnComponentSelected();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _eventBus.Publish(new ErrorEvent(ex, "窗口选择变化处理"));
            }
        }

        /// <summary>
        /// 处理演示文稿关闭
        /// </summary>
        private void OnPresentationClose(Presentation presentation)
        {
            try
            {
                // 清理相关组件
                var componentsToRemove = new List<string>();
                
                foreach (var kvp in _activeComponents)
                {
                    if (IsComponentInPresentation(kvp.Value, presentation))
                    {
                        kvp.Value.Dispose();
                        componentsToRemove.Add(kvp.Key);
                    }
                }

                foreach (var componentId in componentsToRemove)
                {
                    _activeComponents.Remove(componentId);
                }
            }
            catch (Exception ex)
            {
                _eventBus.Publish(new ErrorEvent(ex, "演示文稿关闭处理"));
            }
        }

        // 辅助方法
        private bool HasTitlePlaceholder(Slide slide) => 
            slide.Shapes.Placeholders.Cast<Microsoft.Office.Interop.PowerPoint.Shape>()
                .Any(s => s.PlaceholderFormat.Type == PpPlaceholderType.ppPlaceholderTitle);

        private bool HasContentPlaceholder(Slide slide) => 
            slide.Shapes.Placeholders.Cast<Microsoft.Office.Interop.PowerPoint.Shape>()
                .Any(s => s.PlaceholderFormat.Type == PpPlaceholderType.ppPlaceholderBody);

        private AvailableSpace CalculateAvailableSpace(Slide slide)
        {
            // 简化实现，实际应该分析现有内容
            return new AvailableSpace
            {
                Left = 50,
                Top = 100,
                Width = slide.Master.Width - 100,
                Height = slide.Master.Height - 150
            };
        }

        private ContentAnalysis AnalyzeExistingContent(Slide slide) => new ContentAnalysis();
        private List<string> DetectPotentialConflicts(Slide slide) => new List<string>();
        private void ApplySlideSpecificConstraints(LayoutConstraints constraints, Slide slide, SlideAnalysis analysis) { }
        private void ApplySlideTheme(GanttChartComponent component, Slide slide) { }
        private void ApplyPerformanceOptimizations(GanttChartComponent component, PerformanceMetrics metrics) { }
        private void RegisterPowerPointEventHandlers(IGanttChartComponent component, Slide slide) { }
        private void SetupContextMenuIntegration(IGanttChartComponent component, Slide slide) { }
        private void SetupDragDropIntegration(IGanttChartComponent component, Slide slide) { }
        private void SetupSelectionSync(IGanttChartComponent component, Slide slide) { }
        private void SetupUndoRedoIntegration(IGanttChartComponent component, Slide slide) { }
        private List<IGanttChartComponent> GetComponentsOnSlide(Slide slide) => new List<IGanttChartComponent>();
        private List<IGanttChartComponent> GetComponentsInPresentation(Presentation presentation) => new List<IGanttChartComponent>();
        private IGanttChartComponent GetComponentByShape(Microsoft.Office.Interop.PowerPoint.Shape shape) => null;
        private bool IsComponentInPresentation(IGanttChartComponent component, Presentation presentation) => false;
    }
}
