using System;
using System.Collections.Generic;

namespace PBIppt.GanttChart.v2.Integration
{
    /// <summary>
    /// PowerPoint集成配置
    /// </summary>
    public class PowerPointIntegrationConfig
    {
        /// <summary>
        /// 启用自动布局调整
        /// </summary>
        public bool EnableAutoLayoutAdjustment { get; set; } = true;

        /// <summary>
        /// 启用主题自动适配
        /// </summary>
        public bool EnableThemeAdaptation { get; set; } = true;

        /// <summary>
        /// 启用性能优化
        /// </summary>
        public bool EnablePerformanceOptimization { get; set; } = true;

        /// <summary>
        /// 启用PowerPoint原生右键菜单
        /// </summary>
        public bool EnableNativeContextMenu { get; set; } = true;

        /// <summary>
        /// 启用拖拽集成
        /// </summary>
        public bool EnableDragDropIntegration { get; set; } = true;

        /// <summary>
        /// 启用选择同步
        /// </summary>
        public bool EnableSelectionSync { get; set; } = true;

        /// <summary>
        /// 启用撤销重做集成
        /// </summary>
        public bool EnableUndoRedoIntegration { get; set; } = true;

        /// <summary>
        /// 自动保存间隔（秒）
        /// </summary>
        public int AutoSaveInterval { get; set; } = 30;

        /// <summary>
        /// 最大撤销步数
        /// </summary>
        public int MaxUndoSteps { get; set; } = 50;

        /// <summary>
        /// 性能监控阈值（毫秒）
        /// </summary>
        public int PerformanceThreshold { get; set; } = 100;

        /// <summary>
        /// 创建默认配置
        /// </summary>
        public static PowerPointIntegrationConfig CreateDefault() => new PowerPointIntegrationConfig();

        /// <summary>
        /// 创建高性能配置
        /// </summary>
        public static PowerPointIntegrationConfig CreateHighPerformance()
        {
            return new PowerPointIntegrationConfig
            {
                EnablePerformanceOptimization = true,
                EnableThemeAdaptation = false,
                AutoSaveInterval = 60,
                MaxUndoSteps = 20,
                PerformanceThreshold = 50
            };
        }

        /// <summary>
        /// 创建完整功能配置
        /// </summary>
        public static PowerPointIntegrationConfig CreateFullFeature()
        {
            return new PowerPointIntegrationConfig
            {
                EnableAutoLayoutAdjustment = true,
                EnableThemeAdaptation = true,
                EnablePerformanceOptimization = true,
                EnableNativeContextMenu = true,
                EnableDragDropIntegration = true,
                EnableSelectionSync = true,
                EnableUndoRedoIntegration = true,
                AutoSaveInterval = 15,
                MaxUndoSteps = 100,
                PerformanceThreshold = 200
            };
        }
    }

    /// <summary>
    /// 组件位置信息
    /// </summary>
    public class ComponentPosition
    {
        /// <summary>
        /// X坐标
        /// </summary>
        public float X { get; set; }

        /// <summary>
        /// Y坐标
        /// </summary>
        public float Y { get; set; }

        /// <summary>
        /// 宽度
        /// </summary>
        public float Width { get; set; }

        /// <summary>
        /// 高度
        /// </summary>
        public float Height { get; set; }

        /// <summary>
        /// 是否自动调整大小
        /// </summary>
        public bool AutoResize { get; set; } = true;

        /// <summary>
        /// 是否保持宽高比
        /// </summary>
        public bool MaintainAspectRatio { get; set; } = true;

        /// <summary>
        /// 创建默认位置
        /// </summary>
        public static ComponentPosition CreateDefault(float slideWidth, float slideHeight)
        {
            return new ComponentPosition
            {
                X = slideWidth * 0.05f,
                Y = slideHeight * 0.15f,
                Width = slideWidth * 0.9f,
                Height = slideHeight * 0.7f
            };
        }

        /// <summary>
        /// 创建居中位置
        /// </summary>
        public static ComponentPosition CreateCentered(float slideWidth, float slideHeight, 
            float width, float height)
        {
            return new ComponentPosition
            {
                X = (slideWidth - width) / 2,
                Y = (slideHeight - height) / 2,
                Width = width,
                Height = height
            };
        }
    }

    /// <summary>
    /// 幻灯片分析结果
    /// </summary>
    public class SlideAnalysis
    {
        /// <summary>
        /// 幻灯片宽度
        /// </summary>
        public float SlideWidth { get; set; }

        /// <summary>
        /// 幻灯片高度
        /// </summary>
        public float SlideHeight { get; set; }

        /// <summary>
        /// 现有图形数量
        /// </summary>
        public int ExistingShapeCount { get; set; }

        /// <summary>
        /// 幻灯片布局名称
        /// </summary>
        public string SlideLayout { get; set; }

        /// <summary>
        /// 主题名称
        /// </summary>
        public string Theme { get; set; }

        /// <summary>
        /// 是否有标题占位符
        /// </summary>
        public bool HasTitle { get; set; }

        /// <summary>
        /// 是否有内容占位符
        /// </summary>
        public bool HasContent { get; set; }

        /// <summary>
        /// 可用空间
        /// </summary>
        public AvailableSpace AvailableSpace { get; set; }

        /// <summary>
        /// 内容分析
        /// </summary>
        public ContentAnalysis ContentAnalysis { get; set; }

        /// <summary>
        /// 潜在冲突
        /// </summary>
        public List<string> PotentialConflicts { get; set; } = new List<string>();

        /// <summary>
        /// 复杂度评分（1-10）
        /// </summary>
        public int ComplexityScore
        {
            get
            {
                int score = 1;
                score += ExistingShapeCount / 10;
                score += PotentialConflicts.Count;
                if (HasTitle) score++;
                if (HasContent) score++;
                return Math.Min(score, 10);
            }
        }

        /// <summary>
        /// 获取分析摘要
        /// </summary>
        public string GetAnalysisSummary()
        {
            return $"尺寸: {SlideWidth}x{SlideHeight}, 图形: {ExistingShapeCount}, " +
                   $"布局: {SlideLayout}, 主题: {Theme}, 复杂度: {ComplexityScore}";
        }
    }

    /// <summary>
    /// 可用空间信息
    /// </summary>
    public class AvailableSpace
    {
        /// <summary>
        /// 左边距
        /// </summary>
        public float Left { get; set; }

        /// <summary>
        /// 上边距
        /// </summary>
        public float Top { get; set; }

        /// <summary>
        /// 可用宽度
        /// </summary>
        public float Width { get; set; }

        /// <summary>
        /// 可用高度
        /// </summary>
        public float Height { get; set; }

        /// <summary>
        /// 右边界
        /// </summary>
        public float Right => Left + Width;

        /// <summary>
        /// 下边界
        /// </summary>
        public float Bottom => Top + Height;

        /// <summary>
        /// 总面积
        /// </summary>
        public float Area => Width * Height;

        /// <summary>
        /// 宽高比
        /// </summary>
        public float AspectRatio => Width / Height;

        /// <summary>
        /// 是否有足够空间
        /// </summary>
        public bool HasSufficientSpace(float requiredWidth, float requiredHeight)
        {
            return Width >= requiredWidth && Height >= requiredHeight;
        }
    }

    /// <summary>
    /// 内容分析结果
    /// </summary>
    public class ContentAnalysis
    {
        /// <summary>
        /// 文本内容数量
        /// </summary>
        public int TextContentCount { get; set; }

        /// <summary>
        /// 图片数量
        /// </summary>
        public int ImageCount { get; set; }

        /// <summary>
        /// 图表数量
        /// </summary>
        public int ChartCount { get; set; }

        /// <summary>
        /// 表格数量
        /// </summary>
        public int TableCount { get; set; }

        /// <summary>
        /// 其他图形数量
        /// </summary>
        public int OtherShapeCount { get; set; }

        /// <summary>
        /// 主要颜色
        /// </summary>
        public List<string> DominantColors { get; set; } = new List<string>();

        /// <summary>
        /// 主要字体
        /// </summary>
        public List<string> DominantFonts { get; set; } = new List<string>();

        /// <summary>
        /// 内容密度（0-1）
        /// </summary>
        public double ContentDensity { get; set; }

        /// <summary>
        /// 总内容数量
        /// </summary>
        public int TotalContentCount => 
            TextContentCount + ImageCount + ChartCount + TableCount + OtherShapeCount;

        /// <summary>
        /// 是否为空幻灯片
        /// </summary>
        public bool IsEmptySlide => TotalContentCount == 0;

        /// <summary>
        /// 是否为复杂幻灯片
        /// </summary>
        public bool IsComplexSlide => TotalContentCount > 10 || ContentDensity > 0.7;
    }

    /// <summary>
    /// 组件创建开始事件
    /// </summary>
    public class ComponentCreationStarted : Events.GanttEventBase
    {
        public string ComponentType { get; set; }
        public string SlideId { get; set; }
        public string ProjectId { get; set; }

        public ComponentCreationStarted(string source = null) : base(source) { }
    }

    /// <summary>
    /// 组件创建完成事件
    /// </summary>
    public class ComponentCreationCompleted : Events.GanttEventBase
    {
        public string ComponentId { get; set; }
        public double OptimizationScore { get; set; }
        public DateTime CreationTime { get; set; }

        public ComponentCreationCompleted(string source = null) : base(source) { }
    }

    /// <summary>
    /// 组件创建失败事件
    /// </summary>
    public class ComponentCreationFailed : Events.GanttEventBase
    {
        public string Error { get; set; }
        public string ProjectId { get; set; }

        public ComponentCreationFailed(string source = null) : base(source) { }
    }

    /// <summary>
    /// PowerPoint集成状态
    /// </summary>
    public enum IntegrationStatus
    {
        /// <summary>
        /// 未初始化
        /// </summary>
        NotInitialized,

        /// <summary>
        /// 初始化中
        /// </summary>
        Initializing,

        /// <summary>
        /// 已激活
        /// </summary>
        Active,

        /// <summary>
        /// 已暂停
        /// </summary>
        Paused,

        /// <summary>
        /// 错误状态
        /// </summary>
        Error,

        /// <summary>
        /// 已释放
        /// </summary>
        Disposed
    }

    /// <summary>
    /// 集成功能类型
    /// </summary>
    [Flags]
    public enum IntegrationFeatures
    {
        None = 0,
        SmartLayout = 1,
        ThemeAdaptation = 2,
        ContextMenu = 4,
        DragDrop = 8,
        SelectionSync = 16,
        UndoRedo = 32,
        AutoSave = 64,
        PerformanceMonitoring = 128,
        All = SmartLayout | ThemeAdaptation | ContextMenu | DragDrop | 
              SelectionSync | UndoRedo | AutoSave | PerformanceMonitoring
    }
}
