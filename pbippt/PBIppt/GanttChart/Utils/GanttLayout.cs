using System;
using System.Collections.Generic;
using System.Drawing;
using PBIppt.GanttChart.Models;

namespace PBIppt.GanttChart.Utils
{
    /// <summary>
    /// 甘特图布局类 - 用于向后兼容
    /// </summary>
    public class GanttLayout
    {
        /// <summary>
        /// 布局宽度
        /// </summary>
        public double TotalWidth { get; set; }

        /// <summary>
        /// 布局高度
        /// </summary>
        public double TotalHeight { get; set; }

        /// <summary>
        /// 任务条高度
        /// </summary>
        public double TaskBarHeight { get; set; } = 20;

        /// <summary>
        /// 行高度（包含任务条和间距）
        /// </summary>
        public double RowHeight { get; set; } = 25;

        /// <summary>
        /// 行间距
        /// </summary>
        public double RowSpacing { get; set; } = 5;

        /// <summary>
        /// 时间轴高度
        /// </summary>
        public double TimelineHeight { get; set; } = 60;

        /// <summary>
        /// 标签列宽度
        /// </summary>
        public double LabelColumnWidth { get; set; } = 150;

        /// <summary>
        /// 每天的宽度（像素）
        /// </summary>
        public double DayWidth { get; set; } = 20;

        // 私有字段用于存储可设置的图表区域属性
        private double? _chartAreaLeft;
        private double? _chartAreaTop;
        private double? _chartAreaWidth;
        private double? _chartAreaHeight;

        /// <summary>
        /// 图表区域左边距
        /// </summary>
        public double ChartAreaLeft
        {
            get => _chartAreaLeft ?? LabelColumnWidth;
            set => _chartAreaLeft = value;
        }

        /// <summary>
        /// 图表区域顶部边距
        /// </summary>
        public double ChartAreaTop
        {
            get => _chartAreaTop ?? TimelineHeight;
            set => _chartAreaTop = value;
        }

        /// <summary>
        /// 图表区域宽度
        /// </summary>
        public double ChartAreaWidth
        {
            get => _chartAreaWidth ?? (TotalWidth - LabelColumnWidth);
            set => _chartAreaWidth = value;
        }

        /// <summary>
        /// 图表区域高度
        /// </summary>
        public double ChartAreaHeight
        {
            get => _chartAreaHeight ?? (TotalHeight - TimelineHeight);
            set => _chartAreaHeight = value;
        }

        /// <summary>
        /// 任务位置信息
        /// </summary>
        public Dictionary<string, Rectangle> TaskPositions { get; set; }

        /// <summary>
        /// 里程碑位置信息
        /// </summary>
        public Dictionary<string, Point> MilestonePositions { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public GanttLayout()
        {
            TaskPositions = new Dictionary<string, Rectangle>();
            MilestonePositions = new Dictionary<string, Point>();

            // 确保RowHeight与TaskBarHeight和RowSpacing保持一致
            RowHeight = TaskBarHeight + RowSpacing;
        }

        /// <summary>
        /// 构造函数（带尺寸）
        /// </summary>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        public GanttLayout(double width, double height) : this()
        {
            TotalWidth = width;
            TotalHeight = height;
        }

        /// <summary>
        /// 计算任务位置
        /// </summary>
        /// <param name="task">任务</param>
        /// <param name="rowIndex">行索引</param>
        /// <param name="startDate">项目开始日期</param>
        /// <param name="endDate">项目结束日期</param>
        /// <returns>任务矩形</returns>
        public Rectangle CalculateTaskPosition(GanttTask task, int rowIndex, DateTime startDate, DateTime endDate)
        {
            var chartWidth = ChartAreaWidth;
            var totalDays = (endDate - startDate).Days;

            if (totalDays <= 0) totalDays = 1;

            var taskStartDays = (task.StartDate - startDate).Days;
            var taskDurationDays = (task.EndDate - task.StartDate).Days + 1;

            var x = ChartAreaLeft + (taskStartDays * chartWidth / totalDays);
            var y = ChartAreaTop + rowIndex * RowHeight;
            var width = taskDurationDays * chartWidth / totalDays;
            var height = TaskBarHeight;

            var rect = new Rectangle((int)x, (int)y, (int)width, (int)height);
            TaskPositions[task.Id] = rect;

            return rect;
        }

        /// <summary>
        /// 计算里程碑位置
        /// </summary>
        /// <param name="milestone">里程碑</param>
        /// <param name="rowIndex">行索引</param>
        /// <param name="startDate">项目开始日期</param>
        /// <param name="endDate">项目结束日期</param>
        /// <returns>里程碑位置</returns>
        public Point CalculateMilestonePosition(GanttMilestone milestone, int rowIndex, DateTime startDate, DateTime endDate)
        {
            var chartWidth = ChartAreaWidth;
            var totalDays = (endDate - startDate).Days;

            if (totalDays <= 0) totalDays = 1;

            var milestoneDays = (milestone.Date - startDate).Days;

            var x = ChartAreaLeft + (milestoneDays * chartWidth / totalDays);
            var y = ChartAreaTop + rowIndex * RowHeight + TaskBarHeight / 2;

            var point = new Point((int)x, (int)y);
            MilestonePositions[milestone.Id] = point;

            return point;
        }

        /// <summary>
        /// 获取任务位置
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务矩形</returns>
        public Rectangle GetTaskPosition(string taskId)
        {
            return TaskPositions.ContainsKey(taskId) ? TaskPositions[taskId] : Rectangle.Empty;
        }

        /// <summary>
        /// 获取里程碑位置
        /// </summary>
        /// <param name="milestoneId">里程碑ID</param>
        /// <returns>里程碑位置</returns>
        public Point GetMilestonePosition(string milestoneId)
        {
            return MilestonePositions.ContainsKey(milestoneId) ? MilestonePositions[milestoneId] : Point.Empty;
        }

        /// <summary>
        /// 清除所有位置信息
        /// </summary>
        public void Clear()
        {
            TaskPositions.Clear();
            MilestonePositions.Clear();
        }

        /// <summary>
        /// 验证布局
        /// </summary>
        /// <returns>是否有效</returns>
        public bool IsValid()
        {
            return TotalWidth > 0 && TotalHeight > 0 && TaskBarHeight > 0;
        }

        /// <summary>
        /// 重置图表区域属性为计算值
        /// </summary>
        public void ResetChartAreaToComputed()
        {
            _chartAreaLeft = null;
            _chartAreaTop = null;
            _chartAreaWidth = null;
            _chartAreaHeight = null;
        }

        /// <summary>
        /// 克隆布局
        /// </summary>
        /// <returns>新的布局实例</returns>
        public GanttLayout Clone()
        {
            var clone = new GanttLayout(TotalWidth, TotalHeight)
            {
                TaskBarHeight = this.TaskBarHeight,
                RowHeight = this.RowHeight,
                RowSpacing = this.RowSpacing,
                TimelineHeight = this.TimelineHeight,
                LabelColumnWidth = this.LabelColumnWidth,
                DayWidth = this.DayWidth
            };

            // 复制图表区域的自定义设置
            clone._chartAreaLeft = this._chartAreaLeft;
            clone._chartAreaTop = this._chartAreaTop;
            clone._chartAreaWidth = this._chartAreaWidth;
            clone._chartAreaHeight = this._chartAreaHeight;

            foreach (var kvp in TaskPositions)
            {
                clone.TaskPositions[kvp.Key] = kvp.Value;
            }

            foreach (var kvp in MilestonePositions)
            {
                clone.MilestonePositions[kvp.Key] = kvp.Value;
            }

            return clone;
        }
    }
}
