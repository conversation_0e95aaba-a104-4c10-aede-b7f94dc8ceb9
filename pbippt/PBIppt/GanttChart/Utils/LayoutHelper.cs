using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using PBIppt.GanttChart.Models;

namespace PBIppt.GanttChart.Utils
{
    /// <summary>
    /// 布局辅助工具类
    /// </summary>
    public static class LayoutHelper
    {
        /// <summary>
        /// 默认任务条高度
        /// </summary>
        public const double DefaultTaskBarHeight = 20.0;

        /// <summary>
        /// 默认行间距
        /// </summary>
        public const double DefaultRowSpacing = 5.0;

        /// <summary>
        /// 默认时间轴高度
        /// </summary>
        public const double DefaultTimelineHeight = 60.0;

        /// <summary>
        /// 默认标签列宽度
        /// </summary>
        public const double DefaultLabelColumnWidth = 150.0;

        /// <summary>
        /// 计算项目的时间范围
        /// </summary>
        /// <param name="project">项目</param>
        /// <returns>开始日期和结束日期</returns>
        public static (DateTime startDate, DateTime endDate) CalculateProjectTimeRange(GanttProject project)
        {
            if (!project.Tasks.Any())
            {
                return (DateTime.Today, DateTime.Today.AddDays(30));
            }

            var startDate = project.Tasks.Min(t => t.StartDate);
            var endDate = project.Tasks.Max(t => t.EndDate);

            // 添加一些边距
            startDate = startDate.AddDays(-1);
            endDate = endDate.AddDays(1);

            return (startDate, endDate);
        }

        /// <summary>
        /// 计算所需的布局高度
        /// </summary>
        /// <param name="taskCount">任务数量</param>
        /// <param name="milestoneCount">里程碑数量</param>
        /// <returns>总高度</returns>
        public static double CalculateRequiredHeight(int taskCount, int milestoneCount)
        {
            var totalRows = taskCount + milestoneCount;
            var defaultRowHeight = DefaultTaskBarHeight + DefaultRowSpacing;
            return DefaultTimelineHeight + totalRows * defaultRowHeight + DefaultRowSpacing;
        }

        /// <summary>
        /// 计算所需的布局宽度
        /// </summary>
        /// <param name="timeSpanDays">时间跨度（天）</param>
        /// <returns>总宽度</returns>
        public static double CalculateRequiredWidth(int timeSpanDays)
        {
            var chartWidth = Math.Max(timeSpanDays * 10, 400); // 每天至少10像素
            return DefaultLabelColumnWidth + chartWidth;
        }

        /// <summary>
        /// 创建默认布局
        /// </summary>
        /// <param name="project">项目</param>
        /// <returns>布局对象</returns>
        public static GanttLayout CreateDefaultLayout(GanttProject project)
        {
            var (startDate, endDate) = CalculateProjectTimeRange(project);
            var timeSpanDays = (endDate - startDate).Days;

            var width = CalculateRequiredWidth(timeSpanDays);
            var height = CalculateRequiredHeight(project.Tasks.Count, project.Milestones.Count);

            var layout = new GanttLayout(width, height);

            // 确保RowHeight正确设置
            layout.RowHeight = layout.TaskBarHeight + layout.RowSpacing;

            // 计算所有任务位置
            for (int i = 0; i < project.Tasks.Count; i++)
            {
                layout.CalculateTaskPosition(project.Tasks[i], i, startDate, endDate);
            }

            // 计算所有里程碑位置
            for (int i = 0; i < project.Milestones.Count; i++)
            {
                var rowIndex = project.Tasks.Count + i; // 里程碑放在任务后面
                layout.CalculateMilestonePosition(project.Milestones[i], rowIndex, startDate, endDate);
            }

            return layout;
        }

        /// <summary>
        /// 优化布局以避免重叠
        /// </summary>
        /// <param name="layout">原始布局</param>
        /// <returns>优化后的布局</returns>
        public static GanttLayout OptimizeLayout(GanttLayout layout)
        {
            var optimized = layout.Clone();
            
            // 简单的重叠检测和调整
            var positions = optimized.TaskPositions.Values.ToList();
            
            for (int i = 0; i < positions.Count; i++)
            {
                for (int j = i + 1; j < positions.Count; j++)
                {
                    if (positions[i].IntersectsWith(positions[j]))
                    {
                        // 如果有重叠，调整第二个任务的位置
                        var rect = positions[j];
                        rect.Y += (int)optimized.RowHeight;
                        positions[j] = rect;
                    }
                }
            }

            return optimized;
        }

        /// <summary>
        /// 验证布局是否合理
        /// </summary>
        /// <param name="layout">布局</param>
        /// <returns>验证结果</returns>
        public static bool ValidateLayout(GanttLayout layout)
        {
            if (!layout.IsValid())
                return false;

            // 检查是否有任务超出边界
            foreach (var position in layout.TaskPositions.Values)
            {
                if (position.Right > layout.TotalWidth || position.Bottom > layout.TotalHeight)
                    return false;
            }

            // 检查是否有里程碑超出边界
            foreach (var position in layout.MilestonePositions.Values)
            {
                if (position.X > layout.TotalWidth || position.Y > layout.TotalHeight)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 计算两个日期之间的工作日数量
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>工作日数量</returns>
        public static int CalculateWorkingDays(DateTime startDate, DateTime endDate)
        {
            var workingDays = 0;
            var current = startDate;

            while (current <= endDate)
            {
                if (current.DayOfWeek != DayOfWeek.Saturday && current.DayOfWeek != DayOfWeek.Sunday)
                {
                    workingDays++;
                }
                current = current.AddDays(1);
            }

            return workingDays;
        }

        /// <summary>
        /// 将像素坐标转换为日期
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="layout">布局</param>
        /// <param name="startDate">项目开始日期</param>
        /// <param name="endDate">项目结束日期</param>
        /// <returns>对应的日期</returns>
        public static DateTime PixelToDate(double x, GanttLayout layout, DateTime startDate, DateTime endDate)
        {
            var chartWidth = layout.TotalWidth - layout.LabelColumnWidth;
            var totalDays = (endDate - startDate).Days;
            
            if (totalDays <= 0) return startDate;

            var relativeX = x - layout.LabelColumnWidth;
            var dayOffset = (relativeX / chartWidth) * totalDays;
            
            return startDate.AddDays(dayOffset);
        }

        /// <summary>
        /// 将日期转换为像素坐标
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="layout">布局</param>
        /// <param name="startDate">项目开始日期</param>
        /// <param name="endDate">项目结束日期</param>
        /// <returns>对应的X坐标</returns>
        public static double DateToPixel(DateTime date, GanttLayout layout, DateTime startDate, DateTime endDate)
        {
            var chartWidth = layout.TotalWidth - layout.LabelColumnWidth;
            var totalDays = (endDate - startDate).Days;
            
            if (totalDays <= 0) return layout.LabelColumnWidth;

            var dayOffset = (date - startDate).Days;
            var relativeX = (dayOffset / (double)totalDays) * chartWidth;
            
            return layout.LabelColumnWidth + relativeX;
        }
    }
}
