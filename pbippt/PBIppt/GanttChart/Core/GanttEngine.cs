using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Office.Interop.PowerPoint;
using PBIppt.GanttChart.Models;
using PBIppt.Utils;

namespace PBIppt.GanttChart.Core
{
    /// <summary>
    /// 甘特图引擎 - 提供甘特图的核心功能
    /// 注意：这是一个简化版本，主要用于向后兼容
    /// </summary>
    public class GanttEngine
    {
        /// <summary>
        /// 从Shape中提取项目数据
        /// </summary>
        /// <param name="shape">甘特图Shape</param>
        /// <returns>项目数据</returns>
        public GanttProject ExtractProjectFromShape(Shape shape)
        {
            try
            {
                Logger.Info($"从Shape提取项目数据: {shape.Name}");

                // 创建一个示例项目（简化实现）
                var project = new GanttProject("从Shape提取的项目");
                project.Description = "这是从PowerPoint Shape中提取的项目数据";
                project.ProjectManager = "系统";

                // 添加一些示例任务
                var task1 = new GanttTask("任务1", DateTime.Today, DateTime.Today.AddDays(5));
                task1.Progress = 50;
                task1.Status = TaskStatus.InProgress;
                project.AddTask(task1);

                var task2 = new GanttTask("任务2", DateTime.Today.AddDays(3), DateTime.Today.AddDays(8));
                task2.Progress = 0;
                task2.Status = TaskStatus.NotStarted;
                project.AddTask(task2);

                Logger.Info($"成功提取项目数据，包含 {project.Tasks.Count} 个任务");
                return project;
            }
            catch (Exception ex)
            {
                Logger.Exception(ex);
                throw new InvalidOperationException($"无法从Shape提取项目数据: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取项目健康度报告
        /// </summary>
        /// <param name="project">项目数据</param>
        /// <returns>健康度报告</returns>
        public ProjectHealthReport GetProjectHealthReport(GanttProject project)
        {
            try
            {
                Logger.Info($"生成项目健康度报告: {project.Name}");

                var report = new ProjectHealthReport
                {
                    ProjectName = project.Name,
                    GeneratedDate = DateTime.Now
                };

                // 计算项目进度
                if (project.Tasks.Any())
                {
                    report.ProjectProgress = project.Tasks.Average(t => t.Progress);
                }

                // 计算关键任务数量
                report.CriticalTaskCount = project.Tasks.Count(t => t.IsOnCriticalPath);

                // 评估整体健康度
                report.OverallHealth = EvaluateProjectHealth(project);
                report.HealthDescription = GetHealthDescription(report.OverallHealth);

                // 生成建议
                report.Recommendations = GenerateRecommendations(project);

                Logger.Info($"健康度报告生成完成，整体健康度: {report.OverallHealth}");
                return report;
            }
            catch (Exception ex)
            {
                Logger.Exception(ex);
                throw new InvalidOperationException($"无法生成项目健康度报告: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 评估项目健康度
        /// </summary>
        private ProjectHealth EvaluateProjectHealth(GanttProject project)
        {
            if (!project.Tasks.Any())
                return ProjectHealth.Poor;

            var avgProgress = project.Tasks.Average(t => t.Progress);
            var delayedTasks = project.Tasks.Count(t => t.IsDelayed);
            var totalTasks = project.Tasks.Count;
            var delayedRatio = (double)delayedTasks / totalTasks;

            if (avgProgress >= 80 && delayedRatio <= 0.1)
                return ProjectHealth.Excellent;
            else if (avgProgress >= 60 && delayedRatio <= 0.2)
                return ProjectHealth.Good;
            else if (avgProgress >= 40 && delayedRatio <= 0.3)
                return ProjectHealth.Fair;
            else if (avgProgress >= 20 && delayedRatio <= 0.5)
                return ProjectHealth.Poor;
            else
                return ProjectHealth.Critical;
        }

        /// <summary>
        /// 获取健康度描述
        /// </summary>
        private string GetHealthDescription(ProjectHealth health)
        {
            switch (health)
            {
                case ProjectHealth.Excellent:
                    return "优秀 - 项目进展顺利";
                case ProjectHealth.Good:
                    return "良好 - 项目基本按计划进行";
                case ProjectHealth.Fair:
                    return "一般 - 项目存在一些问题";
                case ProjectHealth.Poor:
                    return "差 - 项目进度严重滞后";
                case ProjectHealth.Critical:
                    return "危险 - 项目面临严重风险";
                default:
                    return "未知";
            }
        }

        /// <summary>
        /// 生成改进建议
        /// </summary>
        private List<string> GenerateRecommendations(GanttProject project)
        {
            var recommendations = new List<string>();

            var delayedTasks = project.Tasks.Where(t => t.IsDelayed).ToList();
            if (delayedTasks.Any())
            {
                recommendations.Add($"有 {delayedTasks.Count} 个任务延期，建议重新评估时间安排");
            }

            var notStartedTasks = project.Tasks.Where(t => t.Status == TaskStatus.NotStarted).ToList();
            if (notStartedTasks.Any())
            {
                recommendations.Add($"有 {notStartedTasks.Count} 个任务尚未开始，建议尽快启动");
            }

            var avgProgress = project.Tasks.Average(t => t.Progress);
            if (avgProgress < 50)
            {
                recommendations.Add("整体进度偏慢，建议增加资源投入或调整计划");
            }

            if (!recommendations.Any())
            {
                recommendations.Add("项目进展良好，继续保持");
            }

            return recommendations;
        }

        /// <summary>
        /// 创建甘特图（简化版本）
        /// </summary>
        /// <param name="slide">幻灯片</param>
        /// <param name="project">项目数据</param>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <returns>创建的Shape</returns>
        public Shape CreateGanttChart(Slide slide, GanttProject project, float x, float y, float width, float height)
        {
            try
            {
                Logger.Info($"创建甘特图: {project.Name}");

                // 创建一个简单的矩形作为甘特图容器
                var shape = slide.Shapes.AddShape(
                    Microsoft.Office.Core.MsoAutoShapeType.msoShapeRectangle,
                    x, y, width, height);

                shape.Name = $"GanttChart_{Guid.NewGuid():N}";
                shape.Fill.ForeColor.RGB = System.Drawing.ColorTranslator.ToOle(System.Drawing.Color.LightBlue);
                shape.Line.ForeColor.RGB = System.Drawing.ColorTranslator.ToOle(System.Drawing.Color.DarkBlue);

                // 添加文本
                shape.TextFrame.TextRange.Text = $"甘特图: {project.Name}\n任务数量: {project.Tasks.Count}";

                Logger.Info($"甘特图创建成功: {shape.Name}");
                return shape;
            }
            catch (Exception ex)
            {
                Logger.Exception(ex);
                throw new InvalidOperationException($"无法创建甘特图: {ex.Message}", ex);
            }
        }
    }
}
