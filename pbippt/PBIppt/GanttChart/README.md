# 甘特图PPT插件模块

## 📋 概述

甘特图PPT插件模块是PBIppt项目的一个重要组成部分，提供了在PowerPoint中创建、编辑和管理甘特图的完整功能。该模块专注于实用性和易用性，支持项目管理中最常用的甘特图功能。

## 🎯 核心功能

### 1. 甘特图创建与渲染
- **4级时间轴系统**: 支持年/月/周/日多级刻度显示
- **任务条渲染**: 支持进度显示、状态样式、关键路径高亮
- **里程碑渲染**: 支持菱形、圆形、三角形等多种形状
- **依赖关系**: 支持任务间的依赖关系箭头连接
- **周末阴影**: 自动显示周末和假期阴影
- **今日线**: 显示当前日期的参考线

### 2. 数据管理
- **CSV导入导出**: 支持标准CSV格式的任务数据
- **JSON项目文件**: 完整的项目数据序列化和反序列化
- **数据验证**: 内置的数据有效性验证和错误处理
- **Excel直接集成**: COM互操作，直接读写Excel文件
- **多工作表支持**: 支持任务、里程碑、依赖关系分别存储

### 3. 交互功能
- **拖拽操作**: 任务条和里程碑的拖拽时间调整
- **多选管理**: 支持多选操作和批量编辑
- **右键菜单**: 完整的上下文菜单和快捷操作
- **选择反馈**: 视觉选择反馈和状态管理

### 4. 模板系统
- **8种内置模板**: 软件开发、产品发布、营销活动、建筑工程、研发、培训、活动策划、简单项目
- **自定义模板**: 支持注册和使用自定义项目模板
- **快速创建**: 一键从模板创建完整项目
- **模板选择对话框**: 友好的模板选择界面

### 5. 用户界面
- **Ribbon集成**: 在现有PBI-ppt标签页中添加甘特图功能组
- **分割按钮**: 创建甘特图的多种方式（示例、模板、空白）
- **任务编辑对话框**: 完整的任务属性编辑界面
- **示例项目**: 自动生成演示用的甘特图项目
- **数据导入向导**: 简化的数据导入流程

### 6. 高级分析功能
- **关键路径计算**: 完整的CPM（关键路径法）算法实现
- **依赖关系管理**: 智能依赖验证、循环检测、冲突解决
- **性能优化**: 大数据量处理和自动优化建议
- **项目健康度分析**: 综合项目状态评估和改进建议
- **延迟影响分析**: 任务延迟对项目整体的影响计算

### 7. PowerPoint集成
- **原生Shape**: 甘特图保存为PowerPoint原生形状
- **跨用户兼容**: 未安装插件的用户可查看静态图表
- **主题适配**: 自动适应PowerPoint主题样式
- **缩放支持**: 支持PowerPoint的缩放和变换操作

## 🏗️ 架构设计

### 目录结构
```
GanttChart/
├── Core/                      # 核心引擎
│   ├── GanttEngine.cs         # 主渲染引擎
│   ├── TimelineRenderer.cs    # 时间轴渲染器
│   ├── TaskRenderer.cs        # 任务条渲染器
│   ├── MilestoneRenderer.cs   # 里程碑渲染器
│   ├── DependencyRenderer.cs  # 依赖关系渲染器
│   ├── CriticalPathCalculator.cs # 关键路径计算器
│   ├── DependencyManager.cs   # 依赖关系管理器
│   └── PerformanceOptimizer.cs # 性能优化器
├── Models/                    # 数据模型
│   ├── GanttProject.cs        # 项目模型
│   ├── GanttTask.cs           # 任务模型
│   ├── GanttMilestone.cs      # 里程碑模型
│   ├── TaskDependency.cs      # 依赖关系模型
│   ├── TimelineSettings.cs    # 时间轴设置
│   └── GanttEnums.cs          # 枚举和常量
├── UI/                        # 用户界面
│   └── TaskEditDialog.cs      # 任务编辑对话框
├── Data/                      # 数据管理
│   ├── DataImporter.cs        # 数据导入器
│   ├── ExcelDataBinder.cs     # Excel集成
│   └── ProjectTemplateManager.cs # 模板管理器
├── Interaction/               # 交互功能
│   ├── DragDropManager.cs     # 拖拽管理器
│   ├── SelectionManager.cs    # 选择管理器
│   └── ContextMenuManager.cs  # 右键菜单管理器
├── Utils/                     # 工具类
│   ├── GanttLayout.cs         # 布局计算器
│   └── ShapeHelper.cs         # Shape操作助手
└── Tests/                     # 测试
    └── GanttEngineTests.cs    # 引擎测试
```

### 核心类关系
- **GanttEngine**: 主控制器，协调所有渲染器
- **GanttProject**: 项目容器，包含所有任务、里程碑和设置
- **Renderers**: 专门的渲染器负责不同元素的绘制
- **Models**: 完整的数据模型，支持验证和序列化

## 🚀 使用方法

### 1. 创建甘特图
1. 在PowerPoint中打开PBI-ppt标签页
2. 点击"创建甘特图"按钮
3. 系统会自动创建一个包含示例数据的甘特图

### 2. 编辑甘特图
1. 选中甘特图
2. 点击"编辑甘特图"按钮
3. 在弹出的对话框中添加或修改任务

### 3. 导入数据
1. 点击"从Excel导入"按钮
2. 选择CSV或JSON格式的数据文件
3. 系统会自动解析并创建甘特图

### 4. 导出数据
1. 选中甘特图
2. 点击"导出到Excel"按钮
3. 选择保存格式（CSV或JSON）

### 5. 刷新甘特图
1. 选中甘特图
2. 点击"刷新甘特图"按钮
3. 系统会重新计算关键路径和项目进度

### 6. 项目分析
1. 选中甘特图
2. 点击"项目分析"按钮
3. 查看项目健康度报告和改进建议

### 7. 性能优化
1. 选中甘特图
2. 点击"性能优化"按钮
3. 系统会分析项目规模并应用相应优化

## 📊 数据格式

### CSV格式示例
```csv
任务名称,开始日期,结束日期,责任人,进度,状态,优先级,备注
需求分析,2025-06-26,2025-07-03,产品经理,100,Completed,3,完成需求收集和分析
系统设计,2025-07-01,2025-07-10,架构师,50,InProgress,4,正在进行系统架构设计
```

### JSON格式示例
```json
{
  "Name": "示例项目",
  "Description": "这是一个示例甘特图项目",
  "Tasks": [
    {
      "Name": "需求分析",
      "StartDate": "2025-06-26",
      "EndDate": "2025-07-03",
      "ResponsiblePerson": "产品经理",
      "Progress": 100,
      "Status": "Completed"
    }
  ],
  "Milestones": [
    {
      "Name": "需求确认",
      "Date": "2025-07-03",
      "Shape": "Diamond"
    }
  ]
}
```

## 🔧 开发指南

### 添加新的渲染器
1. 在`Core/`目录下创建新的渲染器类
2. 继承基础渲染接口或参考现有渲染器
3. 在`GanttEngine`中注册新渲染器

### 扩展数据模型
1. 在`Models/`目录下修改或添加模型类
2. 更新相关的验证逻辑
3. 确保JSON序列化兼容性

### 添加新的数据格式支持
1. 在`DataImporter`中添加新的解析方法
2. 更新Ribbon界面的文件过滤器
3. 添加相应的错误处理

## 🧪 测试

### 运行测试
```csharp
// 在代码中调用测试方法
PBIppt.GanttChart.Tests.GanttEngineTests.RunAllTests();
```

### 测试覆盖
- 项目创建和验证
- 数据导入导出
- 时间轴设置
- 布局计算

## 📊 开发状态

- **当前版本**: v2.0
- **开发进度**: 95%
- **核心功能**: 已完成
- **交互功能**: 已完成
- **模板系统**: 已完成
- **Excel集成**: 已完成
- **测试状态**: 核心功能测试完成
- **文档状态**: 完整文档完成

## 📝 已知限制

1. **依赖关系类型**: 目前仅支持完成-开始(FS)类型
2. **大数据量**: 支持500+任务，超大项目会自动优化
3. **实时同步**: 暂不支持实时数据源同步
4. **移动端**: 暂不支持移动设备操作

## 🔮 未来计划

1. **键盘快捷键**: 支持常用快捷键操作
2. **实时数据同步**: 与外部数据源的实时同步
3. **报表生成**: 项目进度报表和图表导出
4. **云端协作**: 多用户协作编辑功能
5. **移动端支持**: 移动设备查看和编辑

## 📞 支持

如有问题或建议，请参考：
- 主项目文档: `../README.md`
- 开发进展记录: `../../甘特图模块开发进展.md`
- 需求文档: `../../甘特图PPT插件需求与开发计划.md`