“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_32\mscorlib\v4.0_4.0.0.0__b77a5c561934e089\mscorlib.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\Microsoft.VisualStudio.Tools.Office.Runtime\v4.0_10.0.0.0__b03f5f7f11d50a3a\Microsoft.VisualStudio.Tools.Office.Runtime.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Core\v4.0_4.0.0.0__b77a5c561934e089\System.Core.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System\v4.0_4.0.0.0__b77a5c561934e089\System.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\mscorlib.resources\v4.0_4.0.0.0_zh-Hans_b77a5c561934e089\mscorlib.resources.dll”。模块已生成，不包含符号。
“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\Microsoft.VisualStudio.Tools.Applications.Hosting\v4.0_10.0.0.0__b03f5f7f11d50a3a\Microsoft.VisualStudio.Tools.Applications.Hosting.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\Microsoft.VisualStudio.Tools.Applications.ServerDocument\v4.0_10.0.0.0__b03f5f7f11d50a3a\Microsoft.VisualStudio.Tools.Applications.ServerDocument.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\Microsoft.VisualStudio.Tools.Applications.Runtime\v4.0_10.0.0.0__b03f5f7f11d50a3a\Microsoft.VisualStudio.Tools.Applications.Runtime.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Windows.Forms\v4.0_4.0.0.0__b77a5c561934e089\System.Windows.Forms.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Drawing\v4.0_4.0.0.0__b03f5f7f11d50a3a\System.Drawing.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Configuration\v4.0_4.0.0.0__b03f5f7f11d50a3a\System.Configuration.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Xml\v4.0_4.0.0.0__b77a5c561934e089\System.Xml.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\Accessibility\v4.0_4.0.0.0__b03f5f7f11d50a3a\Accessibility.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\Microsoft.VisualStudio.Tools.Office.Runtime.resources\v4.0_10.0.0.0_zh-Hans_b03f5f7f11d50a3a\Microsoft.VisualStudio.Tools.Office.Runtime.resources.dll”。模块已生成，不包含符号。
“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Deployment\v4.0_4.0.0.0__b03f5f7f11d50a3a\System.Deployment.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Security\v4.0_4.0.0.0__b03f5f7f11d50a3a\System.Security.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Deployment.resources\v4.0_4.0.0.0_zh-Hans_b03f5f7f11d50a3a\System.Deployment.resources.dll”。模块已生成，不包含符号。
引发的异常:“System.Deployment.Application.DeploymentException”(位于 System.Deployment.dll 中)
“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Xml.Linq\v4.0_4.0.0.0__b77a5c561934e089\System.Xml.Linq.dll”。包含/排除设置已禁用符号加载。
引发的异常:“System.Security.Cryptography.CryptographicException”(位于 Microsoft.VisualStudio.Tools.Applications.Hosting.dll 中)
线程 'InstallerThread' (33240) 已退出，返回值为 0 (0x0)。
“POWERPNT.EXE”(CLR v4.0.30319: Domain 2): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_32\mscorlib\v4.0_4.0.0.0__b77a5c561934e089\mscorlib.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: DefaultDomain): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\Microsoft.Office.Tools\v4.0_10.0.0.0__b03f5f7f11d50a3a\Microsoft.Office.Tools.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\Microsoft.VisualStudio.Tools.Office.Runtime\v4.0_10.0.0.0__b03f5f7f11d50a3a\Microsoft.VisualStudio.Tools.Office.Runtime.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Core\v4.0_4.0.0.0__b77a5c561934e089\System.Core.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System\v4.0_4.0.0.0__b77a5c561934e089\System.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Configuration\v4.0_4.0.0.0__b03f5f7f11d50a3a\System.Configuration.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Xml\v4.0_4.0.0.0__b77a5c561934e089\System.Xml.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Security\v4.0_4.0.0.0__b03f5f7f11d50a3a\System.Security.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\Microsoft.VisualStudio.Tools.Applications.Hosting\v4.0_10.0.0.0__b03f5f7f11d50a3a\Microsoft.VisualStudio.Tools.Applications.Hosting.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\Microsoft.VisualStudio.Tools.Applications.Runtime\v4.0_10.0.0.0__b03f5f7f11d50a3a\Microsoft.VisualStudio.Tools.Applications.Runtime.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Deployment\v4.0_4.0.0.0__b03f5f7f11d50a3a\System.Deployment.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\Microsoft.VisualStudio.Tools.Applications.ServerDocument\v4.0_10.0.0.0__b03f5f7f11d50a3a\Microsoft.VisualStudio.Tools.Applications.ServerDocument.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Windows.Forms\v4.0_4.0.0.0__b77a5c561934e089\System.Windows.Forms.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Drawing\v4.0_4.0.0.0__b03f5f7f11d50a3a\System.Drawing.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Xml.Linq\v4.0_4.0.0.0__b77a5c561934e089\System.Xml.Linq.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\Microsoft.Office.Tools\v4.0_10.0.0.0__b03f5f7f11d50a3a\Microsoft.Office.Tools.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\Microsoft.Office.Tools.Common.Implementation\v4.0_10.0.0.0__b03f5f7f11d50a3a\Microsoft.Office.Tools.Common.Implementation.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\Microsoft.Office.Tools.Common\v4.0_10.0.0.0__b03f5f7f11d50a3a\Microsoft.Office.Tools.Common.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Users\<USER>\AppData\Local\assembly\dl3\DYTN8ZZR.JHK\J25WB28V.RXP\4d0f62be\641e6245_2d00dc01\PBIppt.dll”。已加载符号。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Users\<USER>\AppData\Local\assembly\dl3\DYTN8ZZR.JHK\J25WB28V.RXP\d53df655\3cd7ed5b_ece0db01\Microsoft.Office.Tools.Common.v4.0.Utilities.dll”。包含/排除设置已禁用符号加载。
BatteryImageApiClient初始化，使用指定地址: http://localhost:82
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Net.Http\v4.0_4.0.0.0__b03f5f7f11d50a3a\System.Net.Http.dll”。包含/排除设置已禁用符号加载。
HttpClient初始化成功，已清除认证头
2025-07-29 10:05:42 [INFO] PowerPoint Battery Plugin started
BatteryImageApiClient初始化，使用指定地址: http://localhost:82
HttpClient初始化成功，已清除认证头
2025-07-29 10:05:42 [INFO] GetCustomUI called with ribbonID: Microsoft.PowerPoint.Presentation
2025-07-29 10:05:42 [INFO] 使用Debug版本Ribbon XML（包含所有功能）
2025-07-29 10:05:42 [INFO] 返回Debug版本Ribbon XML，长度: 2438
2025-07-29 10:05:42 [INFO] Ribbon_Load called successfully
2025-07-29 10:05:42 [INFO] Status label invalidated successfully
线程 18828 已退出，返回值为 0 (0x0)。
线程 22900 已退出，返回值为 0 (0x0)。
线程 26272 已退出，返回值为 0 (0x0)。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\Microsoft.CSharp\v4.0_4.0.0.0__b03f5f7f11d50a3a\Microsoft.CSharp.dll”。包含/排除设置已禁用符号加载。
用户未登录，功能: PPT表格生成功能
AuthService 初始化，API地址: http://localhost:82
开始登录流程，账号: 071716
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Users\<USER>\AppData\Local\assembly\dl3\DYTN8ZZR.JHK\J25WB28V.RXP\1c541277\00baa3fc_8c51d901\Newtonsoft.Json.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Runtime.Serialization\v4.0_4.0.0.0__b77a5c561934e089\System.Runtime.Serialization.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Numerics\v4.0_4.0.0.0__b77a5c561934e089\System.Numerics.dll”。包含/排除设置已禁用符号加载。
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_32\System.Data\v4.0_4.0.0.0__b77a5c561934e089\System.Data.dll”。包含/排除设置已禁用符号加载。
发送登录请求到: http://localhost:82/login
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\mscorlib.resources\v4.0_4.0.0.0_zh-Hans_b77a5c561934e089\mscorlib.resources.dll”。模块已生成，不包含符号。
登录API响应状态: OK
解析登录响应: jwtToken存在=True, username=071716
设置认证头: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************.bXttZyT4UFh-zhnUS0oDZN8xdZ-d6mqzh0oZb29tfTpA-z-B3MjGFQGMvyxvF6MxrZ2XWcnOflZG1m_HvOY-qQ
开始获取当前用户信息
成功获取用户信息: 071716, 角色数量: 120
成功获取用户角色信息，角色数量: 120
AuthManager.SetCurrentAuth: 设置角色信息，角色数量: 120
认证信息已保存到AuthManager
LoginForm: 登录成功，认证信息和角色信息已正确设置
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“Anonymously Hosted DynamicMethods Assembly”。
令牌已设置到BatteryImageApiClient
响应状态: OK
响应状态: OK
响应状态: OK
响应状态: OK
响应状态: OK
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
BatteryDetectionProcessor初始化完成
开始生成PPT表格
表格尺寸: 5行 x 13列
开始预处理图片数据以计算表格宽度
开始预处理所有图片数据
需要预处理的图片数量: 30
开始下载图片，文件ID: 1947918863608123393, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1947918863608123393
开始下载图片，文件ID: 1940716292083105793, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716292083105793
开始下载图片，文件ID: 1940716094942429185, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716094942429185
开始下载图片，文件ID: 1940716311947329538, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716311947329538
开始下载图片，文件ID: 1940716103809187841, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716103809187841
开始下载图片，文件ID: 1940716321992687617, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716321992687617
开始下载图片，文件ID: 1940716128824016897, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716128824016897
开始下载图片，文件ID: 1940716353542242305, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716353542242305
开始下载图片，文件ID: 1940716120393465857, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716120393465857
开始下载图片，文件ID: 1940716344088281089, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716344088281089
开始下载图片，文件ID: 1940716163452190722, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716163452190722
开始下载图片，文件ID: 1940716386140372994, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716386140372994
开始下载图片，文件ID: 1940716171899518978, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716171899518978
开始下载图片，文件ID: 1940716397460799490, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716397460799490
开始下载图片，文件ID: 1940716180531396610, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716180531396610
开始下载图片，文件ID: 1940716406784737282, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716406784737282
开始下载图片，文件ID: 1940716212726874114, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716212726874114
开始下载图片，文件ID: 1940716436128088065, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716436128088065
开始下载图片，文件ID: 1940716204195659777, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716204195659777
开始下载图片，文件ID: 1940716427282300930, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1940716427282300930
开始下载图片，文件ID: 1945407610642325505, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1945407610642325505
开始下载图片，文件ID: 1945407695237242882, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1945407695237242882
开始下载图片，文件ID: 1945407620649934850, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1945407620649934850
开始下载图片，文件ID: 1945407704611512321, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1945407704611512321
开始下载图片，文件ID: 1945407630208753665, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1945407630208753665
开始下载图片，文件ID: 1945407734936330242, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1945407734936330242
开始下载图片，文件ID: 1945407654158229505, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1945407654158229505
开始下载图片，文件ID: 1945407769153462273, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1945407769153462273
开始下载图片，文件ID: 1945407641915056130, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1945407641915056130
开始下载图片，文件ID: 1945407758789337089, 模式: little
使用AuthManager中的最新token: eyJhbGciOi...
设置Authorization头: Bearer eyJhbGciOi...
请求Minio预览URL: http://localhost:82/minioFile/getFileUrl?fileId=1945407758789337089
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716163452190722/IMG_5744.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020628Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=dd22a369de9cb0e4d826648e4de2d4e7ea11fda86c133af4d532fdf034547345"}
“POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_32\System.Web\v4.0_4.0.0.0__b03f5f7f11d50a3a\System.Web.dll”。包含/排除设置已禁用符号加载。
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716292083105793/IMG_5811.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020628Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=3de00e6df1f88f636644b035f468775f39f2389d442ba9d12d5ffd2555cd64b5"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716094942429185/IMG_5738.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020628Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=d669ceb08a3cbe2db37affe81c00dee917ccc170927a7c31721ead83f2ddcfde"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/test/20250723/1947918863608123393/20250717-144746.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020628Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=159ff6e7aa3b8bec82445044a44520e846086277eaa75afe275727e7b81be928"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716386140372994/IMG_5815.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020628Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=952acf57afdcbf015732ab6d515653ad7e6bf8909653932950ab144b99138002"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716311947329538/IMG_5808.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020628Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=ccea349b6f110313d5f468a48b701f04e0f34b8d2877ad92ee29d4cc44c204af"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716321992687617/IMG_5809.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020628Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=87de15dec5cdbd8790d919ddd7bf9084ae42503d39b051d43be914b49f1bb1eb"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716103809187841/IMG_5737.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020628Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=b2b1b79225dc6290e144e131a0048503b74caf7121c9e7a5cc5341f3d52a2a96"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716353542242305/IMG_5774.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020628Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=126c4c8b657d991cdf154844f06545559a51f440e68984d25af957f8e72fe31a"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716344088281089/IMG_5775.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020628Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=c9a9fe792db8c3a0e5a690de22d738ddd9f25176fed27f264578aec95e4bb855"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716128824016897/IMG_5773.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020628Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=f2ab5f4b5a1448eafa4257d5c9ebcb425c16d63242c9db9fa4b8520585c30a1e"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716120393465857/IMG_5772.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020628Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=e0b5a8b74dc999b2f46843f9bca1d7cbefff8bc3bc821bdfd781ddbd956aeae5"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716171899518978/IMG_5742.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020628Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=1abefafc541e67c1d3bcaea3bcb32ce2d5e89f1ab5467f9f0c07c9be21f93d00"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716397460799490/IMG_5813.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020628Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=89e2c355a7f6f017f035f91c336a6b3e5c8a4810545ad8002bde990f9898703b"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716180531396610/IMG_5741.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020629Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=f61de9d0112b1a314a31f5d3bce692d011f5caab9a0e922e1c712145d2167127"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716427282300930/IMG_5781.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020629Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=a2ddea08d59818eba3932a79765f86e139c8e42092f6457260d6dacf68f19d35"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716406784737282/IMG_5814.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020629Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=6ededeffa2af39f9ac591819a0b6e7c7dd2a98f97a53f59c36e3f50998133a71"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716212726874114/IMG_5778.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020629Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=92fce95074f421297ad4e66bbfbc40a29f56e3c8adc497a9fa634606da4e1a68"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250716/1945407769153462273/IMG_6859.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020629Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=f88f6496adea1c3faf4e218e39bbf43ee099eeccc31f707dff7ea1cf359db3ca"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250716/1945407758789337089/IMG_6860.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020629Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=64ceb38d9dd7dfd2fe1fde09b3b9555c7fdfdd366106a8c835e528d89f548556"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250716/1945407630208753665/IMG_6844.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020629Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=a920c6a340980f264faf3b2e75e9311c836d4229a2b7d1b741f69599a88252e6"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250716/1945407641915056130/IMG_6856.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020629Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=9e5662a1d138a63186dc055ca6d6655ba520acd8b1fc9e4fe1a8fe1c1b3221dc"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716436128088065/IMG_5780.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020629Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=8314e229b5d214be45b5564475577ba94db2f4c637968211b8912d56ccbf9504"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250716/1945407734936330242/IMG_6882.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020629Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=d500ddde0efd765317dbf98094e7e07f23ab6a32aa72827b5072a58a045caebf"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250716/1945407704611512321/IMG_6883.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020629Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=5ff4708f22a4281aeb8bcfcb6af18e16cf9b8890e8d662325bc7712842ccb97e"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250716/1945407610642325505/IMG_6846.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020629Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=6cbb3084573af19c46fb5df0cd4fc10cd9849d509c1f28d8d45324a87e19f457"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250716/1945407695237242882/IMG_6884.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020629Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=989dee39ede9bd325aaa1f98b534084423b841a6dc85a0055be816747c565f69"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250716/1945407654158229505/IMG_6855.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020629Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=33dc8b23371670f4974de152144183d18b71e3782340bff2875c82460b899d75"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250716/1945407620649934850/IMG_6843.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020629Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=6bd37de8f6ebd863782488e2ab665c6b1349ff4750ac932dd5ac4582f5533804"}
Minio预览URL响应: {"success":true,"code":200,"message":"请求成功","data":"http://***********:9000/safetylab/20250703/1940716204195659777/IMG_5777.JPG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0OPH9lNuTC2EnLHF4Di6%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T020629Z&X-Amz-Expires=18000&X-Amz-SignedHeaders=host&X-Amz-Signature=b3d0ec2f2608a764feac134b662ab785250d9c3671297e72e6f8e7b9e71a3616"}
📥 预处理下载: 1947918863608123393
📥 预处理下载: 1940716292083105793
📥 预处理下载: 1940716311947329538
📥 预处理下载: 1940716163452190722
📥 预处理下载: 1940716386140372994
📥 预处理下载: 1940716406784737282
📥 预处理下载: 1940716397460799490
📥 预处理下载: 1940716094942429185
📥 预处理下载: 1940716180531396610
📥 预处理下载: 1940716171899518978
📥 预处理下载: 1940716103809187841
📥 预处理下载: 1945407620649934850
📥 预处理下载: 1945407734936330242
📥 预处理下载: 1945407704611512321
📥 预处理下载: 1945407695237242882
📥 预处理下载: 1940716321992687617
📥 预处理下载: 1945407654158229505
📥 预处理下载: 1945407610642325505
📥 预处理下载: 1945407758789337089
📥 预处理下载: 1940716204195659777
📥 预处理下载: 1940716128824016897
📥 预处理下载: 1940716353542242305
📥 预处理下载: 1945407641915056130
📥 预处理下载: 1945407769153462273
📥 预处理下载: 1940716344088281089
📥 预处理下载: 1945407630208753665
📥 预处理下载: 1940716212726874114
📥 预处理下载: 1940716427282300930
📥 预处理下载: 1940716120393465857
📥 预处理下载: 1940716436128088065
📐 图片尺寸: 4032x3024 (无EXIF旋转)
原始图片尺寸: 4032x3024
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:36 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
2025-07-29 10:06:36 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4032x3024
2025-07-29 10:06:36 [INFO] ✅ 图片压缩成功: 4032x3024 -> 1024x768 (大小: 1061KB -> 60KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1947918863608123393"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
原始图片尺寸: 3024x4032
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:36 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:36 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:36 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
2025-07-29 10:06:36 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
2025-07-29 10:06:36 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:36 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2478KB -> 116KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716292083105793"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
原始图片尺寸: 3024x4032
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:36 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:36 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:36 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
2025-07-29 10:06:36 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
2025-07-29 10:06:36 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:37 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2689KB -> 105KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716094942429185"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
原始图片尺寸: 3024x4032
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:37 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:37 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:37 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
2025-07-29 10:06:37 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
2025-07-29 10:06:37 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:37 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2448KB -> 152KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716311947329538"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
原始图片尺寸: 3024x4032
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:37 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:37 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:37 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
2025-07-29 10:06:37 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
2025-07-29 10:06:37 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:37 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2354KB -> 99KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716103809187841"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
原始图片尺寸: 3024x4032
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:37 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:37 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:37 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
2025-07-29 10:06:37 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
2025-07-29 10:06:37 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:37 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 3080KB -> 144KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716321992687617"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
原始图片尺寸: 4284x5712
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:38 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:38 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:38 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
2025-07-29 10:06:38 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
2025-07-29 10:06:38 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:38 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 6125KB -> 192KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716128824016897"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
原始图片尺寸: 4284x5712
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:38 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:38 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:38 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
2025-07-29 10:06:38 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
2025-07-29 10:06:38 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:38 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 6017KB -> 193KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716353542242305"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
原始图片尺寸: 4284x5712
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:38 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:38 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:38 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
2025-07-29 10:06:39 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
2025-07-29 10:06:39 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:39 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 6218KB -> 176KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716120393465857"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
原始图片尺寸: 4284x5712
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:39 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:39 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:39 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
2025-07-29 10:06:39 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
2025-07-29 10:06:39 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:40 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 6326KB -> 206KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716344088281089"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
原始图片尺寸: 3024x4032
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:40 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:40 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:40 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
2025-07-29 10:06:40 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
2025-07-29 10:06:40 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:40 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2791KB -> 101KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716163452190722"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
原始图片尺寸: 3024x4032
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:40 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:40 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:40 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
2025-07-29 10:06:40 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
2025-07-29 10:06:40 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:41 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2594KB -> 124KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716386140372994"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
原始图片尺寸: 3024x4032
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:41 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:41 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:41 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
2025-07-29 10:06:41 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
2025-07-29 10:06:41 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:41 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2502KB -> 106KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716171899518978"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
原始图片尺寸: 3024x4032
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:41 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:41 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:41 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
2025-07-29 10:06:41 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
2025-07-29 10:06:41 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:41 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2541KB -> 151KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716397460799490"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
原始图片尺寸: 3024x4032
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:41 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:41 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:41 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
2025-07-29 10:06:41 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
2025-07-29 10:06:41 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:42 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2220KB -> 97KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716180531396610"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
原始图片尺寸: 3024x4032
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:42 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:42 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:42 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
2025-07-29 10:06:42 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
2025-07-29 10:06:42 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:42 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2561KB -> 154KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716406784737282"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
原始图片尺寸: 4284x5712
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:42 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:42 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:42 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
2025-07-29 10:06:42 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
2025-07-29 10:06:42 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:42 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 6683KB -> 178KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716212726874114"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
原始图片尺寸: 4284x5712
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:43 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:43 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:43 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
2025-07-29 10:06:43 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
2025-07-29 10:06:43 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:43 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 6940KB -> 188KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716436128088065"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
原始图片尺寸: 4284x5712
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:43 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:43 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:43 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
2025-07-29 10:06:43 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
2025-07-29 10:06:43 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:44 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 6617KB -> 173KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716204195659777"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
原始图片尺寸: 4284x5712
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:44 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:44 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:44 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
2025-07-29 10:06:44 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
2025-07-29 10:06:44 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:44 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 6982KB -> 187KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1940716427282300930"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 1, 需要旋转: False
📐 图片尺寸: 5712x4284 (无EXIF旋转)
原始图片尺寸: 5712x4284
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:44 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
2025-07-29 10:06:44 [INFO] 📐 图片尺寸: 5712x4284 (无EXIF旋转)
2025-07-29 10:06:44 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 5712x4284
2025-07-29 10:06:45 [INFO] ✅ 图片压缩成功: 5712x4284 -> 1024x768 (大小: 5920KB -> 68KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1945407610642325505"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 1, 需要旋转: False
📐 图片尺寸: 4032x3024 (无EXIF旋转)
原始图片尺寸: 4032x3024
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:45 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
2025-07-29 10:06:45 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
2025-07-29 10:06:45 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4032x3024
2025-07-29 10:06:45 [INFO] ✅ 图片压缩成功: 4032x3024 -> 1024x768 (大小: 3251KB -> 108KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1945407695237242882"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 1, 需要旋转: False
📐 图片尺寸: 4032x3024 (无EXIF旋转)
原始图片尺寸: 4032x3024
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:45 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
2025-07-29 10:06:45 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
2025-07-29 10:06:45 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4032x3024
2025-07-29 10:06:45 [INFO] ✅ 图片压缩成功: 4032x3024 -> 1024x768 (大小: 3215KB -> 97KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1945407620649934850"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 1, 需要旋转: False
📐 图片尺寸: 4032x3024 (无EXIF旋转)
原始图片尺寸: 4032x3024
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:45 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
2025-07-29 10:06:45 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
2025-07-29 10:06:45 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4032x3024
2025-07-29 10:06:46 [INFO] ✅ 图片压缩成功: 4032x3024 -> 1024x768 (大小: 2904KB -> 103KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1945407704611512321"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 1, 需要旋转: False
📐 图片尺寸: 5712x4284 (无EXIF旋转)
原始图片尺寸: 5712x4284
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:46 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
2025-07-29 10:06:46 [INFO] 📐 图片尺寸: 5712x4284 (无EXIF旋转)
2025-07-29 10:06:46 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 5712x4284
2025-07-29 10:06:46 [INFO] ✅ 图片压缩成功: 5712x4284 -> 1024x768 (大小: 6397KB -> 84KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1945407630208753665"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 1, 需要旋转: False
📐 图片尺寸: 4032x3024 (无EXIF旋转)
原始图片尺寸: 4032x3024
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:46 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
2025-07-29 10:06:46 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
2025-07-29 10:06:46 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4032x3024
2025-07-29 10:06:46 [INFO] ✅ 图片压缩成功: 4032x3024 -> 1024x768 (大小: 2918KB -> 100KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1945407734936330242"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
原始图片尺寸: 4284x5712
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:47 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:47 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:47 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
2025-07-29 10:06:47 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
2025-07-29 10:06:47 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:47 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 5288KB -> 180KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1945407654158229505"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
原始图片尺寸: 4284x5712
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:47 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:47 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:47 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
2025-07-29 10:06:47 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
2025-07-29 10:06:47 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:48 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 5944KB -> 204KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1945407769153462273"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
原始图片尺寸: 4284x5712
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:48 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:48 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:48 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
2025-07-29 10:06:48 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
2025-07-29 10:06:48 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
2025-07-29 10:06:48 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 5664KB -> 191KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1945407641915056130"]
发送批量检测请求到: http://************:8011/detect/
📐 EXIF Orientation: 6, 需要旋转: True
📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
原始图片尺寸: 4284x5712
BatteryDetectionApiClient初始化，服务器地址: http://************:8011
2025-07-29 10:06:49 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:49 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:49 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
2025-07-29 10:06:49 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
2025-07-29 10:06:49 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
线程 2324 已退出，返回值为 0 (0x0)。
线程 20536 已退出，返回值为 0 (0x0)。
线程 28524 已退出，返回值为 0 (0x0)。
2025-07-29 10:06:49 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 5746KB -> 197KB)
开始检测多张图片，数量: 1
添加图片ID列表: ["1945407758789337089"]
发送批量检测请求到: http://************:8011/detect/
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1947918863608123393","CropLeft":352,"CropTop":353,"CropRight":495,"CropBottom":236,"CropLeftPercent":0.34375,"CropTopPercent":0.459635,"CropRightPercent":0.483398,"CropBottomPercent":0.307292,"center_x_percent":0.430176,"center_y_percent":0.576172,"width_percent":0.172852,"height_percent":0.233073,"confidence":0.8987062573432922,"image_info":{"image_id":"1947918863608123393","width":1024,"height":768,"format":"JPEG","size_bytes":61629,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1947918863608123393, 尺寸: 1024x768
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1947918863608123393, 裁剪区域: (352, 353, 495, 236), 百分比: (0.344, 0.460, 0.483, 0.307), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:49 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
2025-07-29 10:06:49 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:49 [INFO] 验证通过，保持API原始坐标: Left=352, Top=353, Right=495, Bottom=236
✅ 原始图片检测成功: Left=352, Top=353, Right=495, Bottom=236, 置信度=0.8987063
🔍 预处理检测: 1947918863608123393
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716292083105793","CropLeft":153,"CropTop":8,"CropRight":0,"CropBottom":164,"CropLeftPercent":0.199219,"CropTopPercent":0.007812,"CropRightPercent":0.0,"CropBottomPercent":0.160156,"center_x_percent":0.599609,"center_y_percent":0.423828,"width_percent":0.800781,"height_percent":0.832031,"confidence":0.7627882361412048,"image_info":{"image_id":"1940716292083105793","width":768,"height":1024,"format":"JPEG","size_bytes":119273,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716292083105793, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716292083105793, 裁剪区域: (153, 8, 0, 164), 百分比: (0.199, 0.008, 0.000, 0.160), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:49 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:49 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:49 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:49 [INFO] 验证通过，保持API原始坐标: Left=153, Top=8, Right=0, Bottom=164
✅ 原始图片检测成功: Left=153, Top=8, Right=0, Bottom=164, 置信度=0.7627882
🔍 预处理检测: 1940716292083105793
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716094942429185","CropLeft":0,"CropTop":105,"CropRight":152,"CropBottom":152,"CropLeftPercent":0.0,"CropTopPercent":0.102539,"CropRightPercent":0.197917,"CropBottomPercent":0.148438,"center_x_percent":0.401042,"center_y_percent":0.477051,"width_percent":0.802083,"height_percent":0.749023,"confidence":0.7899401783943176,"image_info":{"image_id":"1940716094942429185","width":768,"height":1024,"format":"JPEG","size_bytes":108480,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716094942429185, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716094942429185, 裁剪区域: (0, 105, 152, 152), 百分比: (0.000, 0.103, 0.198, 0.148), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:49 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:49 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:49 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:49 [INFO] 验证通过，保持API原始坐标: Left=0, Top=105, Right=152, Bottom=152
✅ 原始图片检测成功: Left=0, Top=105, Right=152, Bottom=152, 置信度=0.7899402
🔍 预处理检测: 1940716094942429185
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716311947329538","CropLeft":240,"CropTop":119,"CropRight":226,"CropBottom":7,"CropLeftPercent":0.3125,"CropTopPercent":0.116211,"CropRightPercent":0.294271,"CropBottomPercent":0.006836,"center_x_percent":0.509115,"center_y_percent":0.554688,"width_percent":0.393229,"height_percent":0.876953,"confidence":0.886547863483429,"image_info":{"image_id":"1940716311947329538","width":768,"height":1024,"format":"JPEG","size_bytes":155672,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716311947329538, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716311947329538, 裁剪区域: (240, 119, 226, 7), 百分比: (0.313, 0.116, 0.294, 0.007), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:49 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:49 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:49 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:49 [INFO] 验证通过，保持API原始坐标: Left=240, Top=119, Right=226, Bottom=7
✅ 原始图片检测成功: Left=240, Top=119, Right=226, Bottom=7, 置信度=0.8865479
🔍 预处理检测: 1940716311947329538
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716103809187841","CropLeft":0,"CropTop":0,"CropRight":0,"CropBottom":0,"CropLeftPercent":0.0,"CropTopPercent":0.0,"CropRightPercent":0.0,"CropBottomPercent":0.0,"center_x_percent":0.5,"center_y_percent":0.5,"width_percent":1.0,"height_percent":1.0,"confidence":0.0,"image_info":{"image_id":"1940716103809187841","width":768,"height":1024,"format":"JPEG","size_bytes":102269,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716103809187841, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716103809187841, 裁剪区域: (0, 0, 0, 0), 百分比: (0.000, 0.000, 0.000, 0.000), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:49 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:49 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:49 [INFO] 计算百分比坐标: Left=0.000, Top=0.000, Right=0.000, Bottom=0.000
2025-07-29 10:06:49 [INFO] 验证通过，保持API原始坐标: Left=0, Top=0, Right=0, Bottom=0
✅ 原始图片检测成功: Left=0, Top=0, Right=0, Bottom=0, 置信度=0
🔍 预处理检测: 1940716103809187841
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716321992687617","CropLeft":245,"CropTop":81,"CropRight":0,"CropBottom":90,"CropLeftPercent":0.31901,"CropTopPercent":0.079102,"CropRightPercent":0.0,"CropBottomPercent":0.087891,"center_x_percent":0.659505,"center_y_percent":0.495605,"width_percent":0.68099,"height_percent":0.833008,"confidence":0.8579339385032654,"image_info":{"image_id":"1940716321992687617","width":768,"height":1024,"format":"JPEG","size_bytes":147560,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716321992687617, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716321992687617, 裁剪区域: (245, 81, 0, 90), 百分比: (0.319, 0.079, 0.000, 0.088), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:49 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:49 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:49 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:49 [INFO] 验证通过，保持API原始坐标: Left=245, Top=81, Right=0, Bottom=90
✅ 原始图片检测成功: Left=245, Top=81, Right=0, Bottom=90, 置信度=0.8579339
🔍 预处理检测: 1940716321992687617
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716128824016897","CropLeft":29,"CropTop":0,"CropRight":9,"CropBottom":134,"CropLeftPercent":0.03776,"CropTopPercent":0.0,"CropRightPercent":0.011719,"CropBottomPercent":0.130859,"center_x_percent":0.513021,"center_y_percent":0.43457,"width_percent":0.950521,"height_percent":0.869141,"confidence":0.9090352058410645,"image_info":{"image_id":"1940716128824016897","width":768,"height":1024,"format":"JPEG","size_bytes":197089,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716128824016897, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716128824016897, 裁剪区域: (29, 0, 9, 134), 百分比: (0.038, 0.000, 0.012, 0.131), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:49 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:49 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:49 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:49 [INFO] 验证通过，保持API原始坐标: Left=29, Top=0, Right=9, Bottom=134
✅ 原始图片检测成功: Left=29, Top=0, Right=9, Bottom=134, 置信度=0.9090352
🔍 预处理检测: 1940716128824016897
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716353542242305","CropLeft":111,"CropTop":5,"CropRight":9,"CropBottom":91,"CropLeftPercent":0.144531,"CropTopPercent":0.004883,"CropRightPercent":0.011719,"CropBottomPercent":0.088867,"center_x_percent":0.566406,"center_y_percent":0.458008,"width_percent":0.84375,"height_percent":0.90625,"confidence":0.8546498417854309,"image_info":{"image_id":"1940716353542242305","width":768,"height":1024,"format":"JPEG","size_bytes":197719,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716353542242305, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716353542242305, 裁剪区域: (111, 5, 9, 91), 百分比: (0.145, 0.005, 0.012, 0.089), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:49 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:49 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:49 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:49 [INFO] 验证通过，保持API原始坐标: Left=111, Top=5, Right=9, Bottom=91
✅ 原始图片检测成功: Left=111, Top=5, Right=9, Bottom=91, 置信度=0.8546498
🔍 预处理检测: 1940716353542242305
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716120393465857","CropLeft":97,"CropTop":0,"CropRight":11,"CropBottom":142,"CropLeftPercent":0.126302,"CropTopPercent":0.0,"CropRightPercent":0.014323,"CropBottomPercent":0.138672,"center_x_percent":0.55599,"center_y_percent":0.430664,"width_percent":0.859375,"height_percent":0.861328,"confidence":0.8905104994773865,"image_info":{"image_id":"1940716120393465857","width":768,"height":1024,"format":"JPEG","size_bytes":181176,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716120393465857, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716120393465857, 裁剪区域: (97, 0, 11, 142), 百分比: (0.126, 0.000, 0.014, 0.139), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:50 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:50 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:50 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:50 [INFO] 验证通过，保持API原始坐标: Left=97, Top=0, Right=11, Bottom=142
✅ 原始图片检测成功: Left=97, Top=0, Right=11, Bottom=142, 置信度=0.8905105
🔍 预处理检测: 1940716120393465857
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716344088281089","CropLeft":125,"CropTop":9,"CropRight":0,"CropBottom":94,"CropLeftPercent":0.16276,"CropTopPercent":0.008789,"CropRightPercent":0.0,"CropBottomPercent":0.091797,"center_x_percent":0.58138,"center_y_percent":0.458496,"width_percent":0.83724,"height_percent":0.899414,"confidence":0.802056610584259,"image_info":{"image_id":"1940716344088281089","width":768,"height":1024,"format":"JPEG","size_bytes":211956,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716344088281089, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716344088281089, 裁剪区域: (125, 9, 0, 94), 百分比: (0.163, 0.009, 0.000, 0.092), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:50 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:50 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:50 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:50 [INFO] 验证通过，保持API原始坐标: Left=125, Top=9, Right=0, Bottom=94
✅ 原始图片检测成功: Left=125, Top=9, Right=0, Bottom=94, 置信度=0.8020566
🔍 预处理检测: 1940716344088281089
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716163452190722","CropLeft":238,"CropTop":250,"CropRight":201,"CropBottom":256,"CropLeftPercent":0.309896,"CropTopPercent":0.244141,"CropRightPercent":0.261719,"CropBottomPercent":0.25,"center_x_percent":0.524089,"center_y_percent":0.49707,"width_percent":0.428385,"height_percent":0.505859,"confidence":0.8881973028182983,"image_info":{"image_id":"1940716163452190722","width":768,"height":1024,"format":"JPEG","size_bytes":104158,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716163452190722, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716163452190722, 裁剪区域: (238, 250, 201, 256), 百分比: (0.310, 0.244, 0.262, 0.250), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:50 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:50 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:50 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:50 [INFO] 验证通过，保持API原始坐标: Left=238, Top=250, Right=201, Bottom=256
✅ 原始图片检测成功: Left=238, Top=250, Right=201, Bottom=256, 置信度=0.8881973
🔍 预处理检测: 1940716163452190722
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716386140372994","CropLeft":171,"CropTop":196,"CropRight":38,"CropBottom":30,"CropLeftPercent":0.222656,"CropTopPercent":0.191406,"CropRightPercent":0.049479,"CropBottomPercent":0.029297,"center_x_percent":0.586589,"center_y_percent":0.581055,"width_percent":0.727865,"height_percent":0.779297,"confidence":0.7742365002632141,"image_info":{"image_id":"1940716386140372994","width":768,"height":1024,"format":"JPEG","size_bytes":127055,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716386140372994, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716386140372994, 裁剪区域: (171, 196, 38, 30), 百分比: (0.223, 0.191, 0.049, 0.029), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:50 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:50 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:50 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:50 [INFO] 验证通过，保持API原始坐标: Left=171, Top=196, Right=38, Bottom=30
✅ 原始图片检测成功: Left=171, Top=196, Right=38, Bottom=30, 置信度=0.7742365
🔍 预处理检测: 1940716386140372994
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716171899518978","CropLeft":0,"CropTop":110,"CropRight":167,"CropBottom":162,"CropLeftPercent":0.0,"CropTopPercent":0.107422,"CropRightPercent":0.217448,"CropBottomPercent":0.158203,"center_x_percent":0.391276,"center_y_percent":0.474609,"width_percent":0.782552,"height_percent":0.734375,"confidence":0.8102781772613525,"image_info":{"image_id":"1940716171899518978","width":768,"height":1024,"format":"JPEG","size_bytes":109320,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716171899518978, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716171899518978, 裁剪区域: (0, 110, 167, 162), 百分比: (0.000, 0.107, 0.217, 0.158), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:50 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:50 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:50 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:50 [INFO] 验证通过，保持API原始坐标: Left=0, Top=110, Right=167, Bottom=162
✅ 原始图片检测成功: Left=0, Top=110, Right=167, Bottom=162, 置信度=0.8102782
🔍 预处理检测: 1940716171899518978
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716397460799490","CropLeft":157,"CropTop":100,"CropRight":12,"CropBottom":106,"CropLeftPercent":0.204427,"CropTopPercent":0.097656,"CropRightPercent":0.015625,"CropBottomPercent":0.103516,"center_x_percent":0.594401,"center_y_percent":0.49707,"width_percent":0.779948,"height_percent":0.798828,"confidence":0.8049425482749939,"image_info":{"image_id":"1940716397460799490","width":768,"height":1024,"format":"JPEG","size_bytes":155139,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716397460799490, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716397460799490, 裁剪区域: (157, 100, 12, 106), 百分比: (0.204, 0.098, 0.016, 0.104), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:50 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:50 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:50 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:50 [INFO] 验证通过，保持API原始坐标: Left=157, Top=100, Right=12, Bottom=106
✅ 原始图片检测成功: Left=157, Top=100, Right=12, Bottom=106, 置信度=0.8049425
🔍 预处理检测: 1940716397460799490
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716180531396610","CropLeft":306,"CropTop":91,"CropRight":209,"CropBottom":78,"CropLeftPercent":0.398438,"CropTopPercent":0.088867,"CropRightPercent":0.272135,"CropBottomPercent":0.076172,"center_x_percent":0.563151,"center_y_percent":0.506348,"width_percent":0.329427,"height_percent":0.834961,"confidence":0.7889789938926697,"image_info":{"image_id":"1940716180531396610","width":768,"height":1024,"format":"JPEG","size_bytes":99973,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716180531396610, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716180531396610, 裁剪区域: (306, 91, 209, 78), 百分比: (0.398, 0.089, 0.272, 0.076), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:50 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:50 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:50 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:50 [INFO] 验证通过，保持API原始坐标: Left=306, Top=91, Right=209, Bottom=78
✅ 原始图片检测成功: Left=306, Top=91, Right=209, Bottom=78, 置信度=0.788979
🔍 预处理检测: 1940716180531396610
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716406784737282","CropLeft":253,"CropTop":106,"CropRight":0,"CropBottom":94,"CropLeftPercent":0.329427,"CropTopPercent":0.103516,"CropRightPercent":0.0,"CropBottomPercent":0.091797,"center_x_percent":0.664714,"center_y_percent":0.505859,"width_percent":0.670573,"height_percent":0.804688,"confidence":0.9046792984008789,"image_info":{"image_id":"1940716406784737282","width":768,"height":1024,"format":"JPEG","size_bytes":157999,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716406784737282, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716406784737282, 裁剪区域: (253, 106, 0, 94), 百分比: (0.329, 0.104, 0.000, 0.092), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:50 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:50 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
2025-07-29 10:06:50 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:50 [INFO] 验证通过，保持API原始坐标: Left=253, Top=106, Right=0, Bottom=94
✅ 原始图片检测成功: Left=253, Top=106, Right=0, Bottom=94, 置信度=0.9046793
🔍 预处理检测: 1940716406784737282
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716212726874114","CropLeft":157,"CropTop":21,"CropRight":0,"CropBottom":80,"CropLeftPercent":0.204427,"CropTopPercent":0.020508,"CropRightPercent":0.0,"CropBottomPercent":0.078125,"center_x_percent":0.602214,"center_y_percent":0.471191,"width_percent":0.795573,"height_percent":0.901367,"confidence":0.8357086181640625,"image_info":{"image_id":"1940716212726874114","width":768,"height":1024,"format":"JPEG","size_bytes":183152,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716212726874114, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716212726874114, 裁剪区域: (157, 21, 0, 80), 百分比: (0.204, 0.021, 0.000, 0.078), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:50 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:50 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:50 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:50 [INFO] 验证通过，保持API原始坐标: Left=157, Top=21, Right=0, Bottom=80
✅ 原始图片检测成功: Left=157, Top=21, Right=0, Bottom=80, 置信度=0.8357086
🔍 预处理检测: 1940716212726874114
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716436128088065","CropLeft":92,"CropTop":0,"CropRight":12,"CropBottom":142,"CropLeftPercent":0.119792,"CropTopPercent":0.0,"CropRightPercent":0.015625,"CropBottomPercent":0.138672,"center_x_percent":0.552083,"center_y_percent":0.430664,"width_percent":0.864583,"height_percent":0.861328,"confidence":0.9120100140571594,"image_info":{"image_id":"1940716436128088065","width":768,"height":1024,"format":"JPEG","size_bytes":192895,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716436128088065, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716436128088065, 裁剪区域: (92, 0, 12, 142), 百分比: (0.120, 0.000, 0.016, 0.139), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:50 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:50 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:50 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:50 [INFO] 验证通过，保持API原始坐标: Left=92, Top=0, Right=12, Bottom=142
✅ 原始图片检测成功: Left=92, Top=0, Right=12, Bottom=142, 置信度=0.91201
🔍 预处理检测: 1940716436128088065
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716204195659777","CropLeft":77,"CropTop":0,"CropRight":10,"CropBottom":138,"CropLeftPercent":0.10026,"CropTopPercent":0.0,"CropRightPercent":0.013021,"CropBottomPercent":0.134766,"center_x_percent":0.54362,"center_y_percent":0.432617,"width_percent":0.886719,"height_percent":0.865234,"confidence":0.916082501411438,"image_info":{"image_id":"1940716204195659777","width":768,"height":1024,"format":"JPEG","size_bytes":177362,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716204195659777, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716204195659777, 裁剪区域: (77, 0, 10, 138), 百分比: (0.100, 0.000, 0.013, 0.135), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:51 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:51 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:51 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:51 [INFO] 验证通过，保持API原始坐标: Left=77, Top=0, Right=10, Bottom=138
线程 13868 已退出，返回值为 0 (0x0)。
✅ 原始图片检测成功: Left=77, Top=0, Right=10, Bottom=138, 置信度=0.9160825
🔍 预处理检测: 1940716204195659777
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1940716427282300930","CropLeft":108,"CropTop":6,"CropRight":9,"CropBottom":92,"CropLeftPercent":0.140625,"CropTopPercent":0.005859,"CropRightPercent":0.011719,"CropBottomPercent":0.089844,"center_x_percent":0.564453,"center_y_percent":0.458008,"width_percent":0.847656,"height_percent":0.904297,"confidence":0.853536069393158,"image_info":{"image_id":"1940716427282300930","width":768,"height":1024,"format":"JPEG","size_bytes":191763,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1940716427282300930, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1940716427282300930, 裁剪区域: (108, 6, 9, 92), 百分比: (0.141, 0.006, 0.012, 0.090), DPI: 120x120
线程 29964 已退出，返回值为 0 (0x0)。
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:51 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:51 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:51 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:51 [INFO] 验证通过，保持API原始坐标: Left=108, Top=6, Right=9, Bottom=92
✅ 原始图片检测成功: Left=108, Top=6, Right=9, Bottom=92, 置信度=0.8535361
🔍 预处理检测: 1940716427282300930
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1945407610642325505","CropLeft":0,"CropTop":0,"CropRight":0,"CropBottom":0,"CropLeftPercent":0.0,"CropTopPercent":0.0,"CropRightPercent":0.0,"CropBottomPercent":0.0,"center_x_percent":0.5,"center_y_percent":0.5,"width_percent":1.0,"height_percent":1.0,"confidence":0.0,"image_info":{"image_id":"1945407610642325505","width":1024,"height":768,"format":"JPEG","size_bytes":69790,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1945407610642325505, 尺寸: 1024x768
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1945407610642325505, 裁剪区域: (0, 0, 0, 0), 百分比: (0.000, 0.000, 0.000, 0.000), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:51 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
2025-07-29 10:06:51 [INFO] 📐 图片尺寸: 5712x4284 (无EXIF旋转)
2025-07-29 10:06:51 [INFO] 计算百分比坐标: Left=0.000, Top=0.000, Right=0.000, Bottom=0.000
2025-07-29 10:06:51 [INFO] 验证通过，保持API原始坐标: Left=0, Top=0, Right=0, Bottom=0
✅ 原始图片检测成功: Left=0, Top=0, Right=0, Bottom=0, 置信度=0
🔍 预处理检测: 1945407610642325505
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1945407695237242882","CropLeft":0,"CropTop":0,"CropRight":0,"CropBottom":0,"CropLeftPercent":0.0,"CropTopPercent":0.0,"CropRightPercent":0.0,"CropBottomPercent":0.0,"center_x_percent":0.5,"center_y_percent":0.5,"width_percent":1.0,"height_percent":1.0,"confidence":0.0,"image_info":{"image_id":"1945407695237242882","width":1024,"height":768,"format":"JPEG","size_bytes":110797,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1945407695237242882, 尺寸: 1024x768
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1945407695237242882, 裁剪区域: (0, 0, 0, 0), 百分比: (0.000, 0.000, 0.000, 0.000), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:51 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
2025-07-29 10:06:51 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
2025-07-29 10:06:51 [INFO] 计算百分比坐标: Left=0.000, Top=0.000, Right=0.000, Bottom=0.000
2025-07-29 10:06:51 [INFO] 验证通过，保持API原始坐标: Left=0, Top=0, Right=0, Bottom=0
✅ 原始图片检测成功: Left=0, Top=0, Right=0, Bottom=0, 置信度=0
🔍 预处理检测: 1945407695237242882
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1945407620649934850","CropLeft":0,"CropTop":0,"CropRight":0,"CropBottom":0,"CropLeftPercent":0.0,"CropTopPercent":0.0,"CropRightPercent":0.0,"CropBottomPercent":0.0,"center_x_percent":0.5,"center_y_percent":0.5,"width_percent":1.0,"height_percent":1.0,"confidence":0.0,"image_info":{"image_id":"1945407620649934850","width":1024,"height":768,"format":"JPEG","size_bytes":99420,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1945407620649934850, 尺寸: 1024x768
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1945407620649934850, 裁剪区域: (0, 0, 0, 0), 百分比: (0.000, 0.000, 0.000, 0.000), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:51 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
2025-07-29 10:06:51 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
2025-07-29 10:06:51 [INFO] 计算百分比坐标: Left=0.000, Top=0.000, Right=0.000, Bottom=0.000
2025-07-29 10:06:51 [INFO] 验证通过，保持API原始坐标: Left=0, Top=0, Right=0, Bottom=0
✅ 原始图片检测成功: Left=0, Top=0, Right=0, Bottom=0, 置信度=0
🔍 预处理检测: 1945407620649934850
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1945407704611512321","CropLeft":257,"CropTop":36,"CropRight":271,"CropBottom":162,"CropLeftPercent":0.250977,"CropTopPercent":0.046875,"CropRightPercent":0.264648,"CropBottomPercent":0.210938,"center_x_percent":0.493164,"center_y_percent":0.417969,"width_percent":0.484375,"height_percent":0.742188,"confidence":0.8934313058853149,"image_info":{"image_id":"1945407704611512321","width":1024,"height":768,"format":"JPEG","size_bytes":105732,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1945407704611512321, 尺寸: 1024x768
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1945407704611512321, 裁剪区域: (257, 36, 271, 162), 百分比: (0.251, 0.047, 0.265, 0.211), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:51 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
2025-07-29 10:06:51 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
2025-07-29 10:06:51 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:51 [INFO] 验证通过，保持API原始坐标: Left=257, Top=36, Right=271, Bottom=162
✅ 原始图片检测成功: Left=257, Top=36, Right=271, Bottom=162, 置信度=0.8934313
🔍 预处理检测: 1945407704611512321
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1945407630208753665","CropLeft":402,"CropTop":373,"CropRight":349,"CropBottom":241,"CropLeftPercent":0.392578,"CropTopPercent":0.485677,"CropRightPercent":0.34082,"CropBottomPercent":0.313802,"center_x_percent":0.525879,"center_y_percent":0.585938,"width_percent":0.266602,"height_percent":0.200521,"confidence":0.8380988836288452,"image_info":{"image_id":"1945407630208753665","width":1024,"height":768,"format":"JPEG","size_bytes":86366,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1945407630208753665, 尺寸: 1024x768
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1945407630208753665, 裁剪区域: (402, 373, 349, 241), 百分比: (0.393, 0.486, 0.341, 0.314), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:51 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
2025-07-29 10:06:51 [INFO] 📐 图片尺寸: 5712x4284 (无EXIF旋转)
2025-07-29 10:06:51 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:51 [INFO] 验证通过，保持API原始坐标: Left=402, Top=373, Right=349, Bottom=241
✅ 原始图片检测成功: Left=402, Top=373, Right=349, Bottom=241, 置信度=0.8380989
🔍 预处理检测: 1945407630208753665
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1945407734936330242","CropLeft":292,"CropTop":35,"CropRight":272,"CropBottom":198,"CropLeftPercent":0.285156,"CropTopPercent":0.045573,"CropRightPercent":0.265625,"CropBottomPercent":0.257812,"center_x_percent":0.509766,"center_y_percent":0.39388,"width_percent":0.449219,"height_percent":0.696615,"confidence":0.7977867722511292,"image_info":{"image_id":"1945407734936330242","width":1024,"height":768,"format":"JPEG","size_bytes":103127,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1945407734936330242, 尺寸: 1024x768
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1945407734936330242, 裁剪区域: (292, 35, 272, 198), 百分比: (0.285, 0.046, 0.266, 0.258), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:51 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
2025-07-29 10:06:51 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
2025-07-29 10:06:51 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:51 [INFO] 验证通过，保持API原始坐标: Left=292, Top=35, Right=272, Bottom=198
✅ 原始图片检测成功: Left=292, Top=35, Right=272, Bottom=198, 置信度=0.7977868
🔍 预处理检测: 1945407734936330242
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1945407654158229505","CropLeft":73,"CropTop":0,"CropRight":9,"CropBottom":134,"CropLeftPercent":0.095052,"CropTopPercent":0.0,"CropRightPercent":0.011719,"CropBottomPercent":0.130859,"center_x_percent":0.541667,"center_y_percent":0.43457,"width_percent":0.893229,"height_percent":0.869141,"confidence":0.8771527409553528,"image_info":{"image_id":"1945407654158229505","width":768,"height":1024,"format":"JPEG","size_bytes":184475,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1945407654158229505, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1945407654158229505, 裁剪区域: (73, 0, 9, 134), 百分比: (0.095, 0.000, 0.012, 0.131), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:52 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:52 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:52 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:52 [INFO] 验证通过，保持API原始坐标: Left=73, Top=0, Right=9, Bottom=134
✅ 原始图片检测成功: Left=73, Top=0, Right=9, Bottom=134, 置信度=0.8771527
🔍 预处理检测: 1945407654158229505
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1945407769153462273","CropLeft":54,"CropTop":16,"CropRight":7,"CropBottom":79,"CropLeftPercent":0.070312,"CropTopPercent":0.015625,"CropRightPercent":0.009115,"CropBottomPercent":0.077148,"center_x_percent":0.530599,"center_y_percent":0.469238,"width_percent":0.920573,"height_percent":0.907227,"confidence":0.8685340285301208,"image_info":{"image_id":"1945407769153462273","width":768,"height":1024,"format":"JPEG","size_bytes":209144,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1945407769153462273, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1945407769153462273, 裁剪区域: (54, 16, 7, 79), 百分比: (0.070, 0.016, 0.009, 0.077), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
线程 34420 已退出，返回值为 0 (0x0)。
2025-07-29 10:06:52 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:52 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:52 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:52 [INFO] 验证通过，保持API原始坐标: Left=54, Top=16, Right=7, Bottom=79
✅ 原始图片检测成功: Left=54, Top=16, Right=7, Bottom=79, 置信度=0.868534
🔍 预处理检测: 1945407769153462273
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1945407641915056130","CropLeft":106,"CropTop":0,"CropRight":10,"CropBottom":142,"CropLeftPercent":0.138021,"CropTopPercent":0.0,"CropRightPercent":0.013021,"CropBottomPercent":0.138672,"center_x_percent":0.5625,"center_y_percent":0.430664,"width_percent":0.848958,"height_percent":0.861328,"confidence":0.855282723903656,"image_info":{"image_id":"1945407641915056130","width":768,"height":1024,"format":"JPEG","size_bytes":196260,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1945407641915056130, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1945407641915056130, 裁剪区域: (106, 0, 10, 142), 百分比: (0.138, 0.000, 0.013, 0.139), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:52 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:52 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:52 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:52 [INFO] 验证通过，保持API原始坐标: Left=106, Top=0, Right=10, Bottom=142
✅ 原始图片检测成功: Left=106, Top=0, Right=10, Bottom=142, 置信度=0.8552827
🔍 预处理检测: 1945407641915056130
服务器响应状态: OK
=== API原始响应内容 ===
{"results":[{"image_id":"1945407758789337089","CropLeft":62,"CropTop":0,"CropRight":9,"CropBottom":129,"CropLeftPercent":0.080729,"CropTopPercent":0.0,"CropRightPercent":0.011719,"CropBottomPercent":0.125977,"center_x_percent":0.534505,"center_y_percent":0.437012,"width_percent":0.907552,"height_percent":0.874023,"confidence":0.8589644432067871,"image_info":{"image_id":"1945407758789337089","width":768,"height":1024,"format":"JPEG","size_bytes":202455,"dpi":[120,120]}}]}
=== API响应内容结束 ===
解析image_info - 图片ID: 1945407758789337089, 尺寸: 768x1024
解析到DPI信息: 120x120
解析检测结果 - ImageId: 1945407758789337089, 裁剪区域: (62, 0, 9, 129), 百分比: (0.081, 0.000, 0.012, 0.126), DPI: 120x120
批量检测完成，成功: True, 结果数量: 1
2025-07-29 10:06:52 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
2025-07-29 10:06:52 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
2025-07-29 10:06:52 [INFO] 使用API返回的百分比坐标
2025-07-29 10:06:52 [INFO] 验证通过，保持API原始坐标: Left=62, Top=0, Right=9, Bottom=129
✅ 原始图片检测成功: Left=62, Top=0, Right=9, Bottom=129, 置信度=0.8589644
🔍 预处理检测: 1945407758789337089
预处理完成: 图片数据30项, 检测结果30项
开始计算列宽自适应
📦 使用预处理缓存的图片数据: 1947918863608123393
📐 检测结果缓存存在: 1947918863608123393, detectionResult != null: True
📐 API尺寸信息: ImageWidth=1024, ImageHeight=768
📐 使用API压缩后尺寸: 1024x768
📦 使用预处理的检测结果: 1947918863608123393
🔍 开始基于API数据计算旋转: 1947918863608123393_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
✅ 无EXIF旋转，直接使用API判断
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 1024x768
📊 电池区域像素尺寸: width=177.0, height=179.0 (API检测: 竖向)
📊 电池区域宽高比: 0.99 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.99)
🔄 EXIF需要旋转: False
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1947918863608123393: 预处理旋转判断=True
📏 列宽计算 - 图片 1947918863608123393: 裁剪前1024x768 -> 裁剪后177.0x179.0 -> 旋转后179x177
📏 列宽计算 - 图片 1947918863608123393: 旋转后宽高比1.01, 可用高度57.9点，图片宽度58.6点
📏 列宽计算 - 图片 1947918863608123393: 可用高度57.9点，所需列宽66.6点（含8点边距）
位置 vent面 before阶段 图片1947918863608123393: 宽度需求66.6
📦 使用预处理缓存的图片数据: 1940716163452190722
📐 检测结果缓存存在: 1940716163452190722, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716163452190722
🔍 开始基于API数据计算旋转: 1940716163452190722_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=329.0, height=518.0 (API检测: 竖向)
📊 电池区域宽高比: 0.64 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.64)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716163452190722: 预处理旋转判断=True
📏 列宽计算 - 图片 1940716163452190722: 裁剪前768x1024 -> 裁剪后329.0x518.0 -> 旋转后517x328
📏 列宽计算 - 图片 1940716163452190722: 旋转后宽高比1.58, 可用高度57.9点，图片宽度91.3点
📏 列宽计算 - 图片 1940716163452190722: 可用高度57.9点，所需列宽99.3点（含8点边距）
位置 vent面 before阶段 图片1940716163452190722: 宽度需求99.3
📦 使用预处理缓存的图片数据: 1945407610642325505
📐 检测结果缓存存在: 1945407610642325505, detectionResult != null: True
📐 API尺寸信息: ImageWidth=1024, ImageHeight=768
📐 使用API压缩后尺寸: 1024x768
📦 使用预处理的检测结果: 1945407610642325505
🔍 开始基于API数据计算旋转: 1945407610642325505_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 1, 需要旋转: False
✅ 无EXIF旋转，直接使用API判断
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 1024x768
📊 电池区域像素尺寸: width=1024.0, height=768.0 (API检测: 横向)
📊 电池区域宽高比: 1.33 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: False (基于电池区域宽高比 1.33)
🔄 EXIF需要旋转: False
✅ 最终旋转判断: False (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1945407610642325505: 预处理旋转判断=False
⚠️ 检测结果无有效裁剪区域: Left=0, Top=0, Right=0, Bottom=0
📏 列宽计算 - 图片 1945407610642325505: 无裁剪信息，使用完整尺寸 1024x768 -> 旋转后1024x768
📏 列宽计算 - 图片 1945407610642325505: 旋转后宽高比1.33, 可用高度57.9点，图片宽度77.2点
📏 列宽计算 - 图片 1945407610642325505: 可用高度57.9点，所需列宽85.2点（含8点边距）
位置 vent面 before阶段 图片1945407610642325505: 宽度需求85.2
位置 vent面 Before阶段最优宽度: 99.3
📦 使用预处理缓存的图片数据: 1940716292083105793
📐 检测结果缓存存在: 1940716292083105793, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716292083105793
🔍 开始基于API数据计算旋转: 1940716292083105793_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=615.0, height=852.0 (API检测: 竖向)
📊 电池区域宽高比: 0.72 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.72)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716292083105793: 预处理旋转判断=True
📏 列宽计算 - 图片 1940716292083105793: 裁剪前768x1024 -> 裁剪后615.0x852.0 -> 旋转后852x614
📏 列宽计算 - 图片 1940716292083105793: 旋转后宽高比1.39, 可用高度57.9点，图片宽度80.4点
📏 列宽计算 - 图片 1940716292083105793: 可用高度57.9点，所需列宽88.4点（含8点边距）
位置 vent面 after阶段 图片1940716292083105793: 宽度需求88.4
📦 使用预处理缓存的图片数据: 1940716386140372994
📐 检测结果缓存存在: 1940716386140372994, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716386140372994
🔍 开始基于API数据计算旋转: 1940716386140372994_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=559.0, height=798.0 (API检测: 竖向)
📊 电池区域宽高比: 0.70 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.70)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716386140372994: 预处理旋转判断=True
📏 列宽计算 - 图片 1940716386140372994: 裁剪前768x1024 -> 裁剪后559.0x798.0 -> 旋转后798x559
📏 列宽计算 - 图片 1940716386140372994: 旋转后宽高比1.43, 可用高度57.9点，图片宽度82.7点
📏 列宽计算 - 图片 1940716386140372994: 可用高度57.9点，所需列宽90.7点（含8点边距）
位置 vent面 after阶段 图片1940716386140372994: 宽度需求90.7
📦 使用预处理缓存的图片数据: 1945407695237242882
📐 检测结果缓存存在: 1945407695237242882, detectionResult != null: True
📐 API尺寸信息: ImageWidth=1024, ImageHeight=768
📐 使用API压缩后尺寸: 1024x768
📦 使用预处理的检测结果: 1945407695237242882
🔍 开始基于API数据计算旋转: 1945407695237242882_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 1, 需要旋转: False
✅ 无EXIF旋转，直接使用API判断
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 1024x768
📊 电池区域像素尺寸: width=1024.0, height=768.0 (API检测: 横向)
📊 电池区域宽高比: 1.33 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: False (基于电池区域宽高比 1.33)
🔄 EXIF需要旋转: False
✅ 最终旋转判断: False (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1945407695237242882: 预处理旋转判断=False
⚠️ 检测结果无有效裁剪区域: Left=0, Top=0, Right=0, Bottom=0
📏 列宽计算 - 图片 1945407695237242882: 无裁剪信息，使用完整尺寸 1024x768 -> 旋转后1024x768
📏 列宽计算 - 图片 1945407695237242882: 旋转后宽高比1.33, 可用高度57.9点，图片宽度77.2点
📏 列宽计算 - 图片 1945407695237242882: 可用高度57.9点，所需列宽85.2点（含8点边距）
位置 vent面 after阶段 图片1945407695237242882: 宽度需求85.2
位置 vent面 After阶段最优宽度: 90.7
📦 使用预处理缓存的图片数据: 1940716094942429185
📐 检测结果缓存存在: 1940716094942429185, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716094942429185
🔍 开始基于API数据计算旋转: 1940716094942429185_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=616.0, height=767.0 (API检测: 竖向)
📊 电池区域宽高比: 0.80 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.80)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716094942429185: 预处理旋转判断=True
📏 列宽计算 - 图片 1940716094942429185: 裁剪前768x1024 -> 裁剪后616.0x767.0 -> 旋转后766x615
📏 列宽计算 - 图片 1940716094942429185: 旋转后宽高比1.25, 可用高度57.9点，图片宽度72.2点
📏 列宽计算 - 图片 1940716094942429185: 可用高度57.9点，所需列宽80.2点（含8点边距）
位置 大面1 before阶段 图片1940716094942429185: 宽度需求80.2
📦 使用预处理缓存的图片数据: 1940716171899518978
📐 检测结果缓存存在: 1940716171899518978, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716171899518978
🔍 开始基于API数据计算旋转: 1940716171899518978_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=601.0, height=752.0 (API检测: 竖向)
📊 电池区域宽高比: 0.80 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.80)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716171899518978: 预处理旋转判断=True
📏 列宽计算 - 图片 1940716171899518978: 裁剪前768x1024 -> 裁剪后601.0x752.0 -> 旋转后752x600
📏 列宽计算 - 图片 1940716171899518978: 旋转后宽高比1.25, 可用高度57.9点，图片宽度72.6点
📏 列宽计算 - 图片 1940716171899518978: 可用高度57.9点，所需列宽80.6点（含8点边距）
位置 大面1 before阶段 图片1940716171899518978: 宽度需求80.6
📦 使用预处理缓存的图片数据: 1945407620649934850
📐 检测结果缓存存在: 1945407620649934850, detectionResult != null: True
📐 API尺寸信息: ImageWidth=1024, ImageHeight=768
📐 使用API压缩后尺寸: 1024x768
📦 使用预处理的检测结果: 1945407620649934850
🔍 开始基于API数据计算旋转: 1945407620649934850_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 1, 需要旋转: False
✅ 无EXIF旋转，直接使用API判断
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 1024x768
📊 电池区域像素尺寸: width=1024.0, height=768.0 (API检测: 横向)
📊 电池区域宽高比: 1.33 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: False (基于电池区域宽高比 1.33)
🔄 EXIF需要旋转: False
✅ 最终旋转判断: False (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1945407620649934850: 预处理旋转判断=False
⚠️ 检测结果无有效裁剪区域: Left=0, Top=0, Right=0, Bottom=0
📏 列宽计算 - 图片 1945407620649934850: 无裁剪信息，使用完整尺寸 1024x768 -> 旋转后1024x768
📏 列宽计算 - 图片 1945407620649934850: 旋转后宽高比1.33, 可用高度57.9点，图片宽度77.2点
📏 列宽计算 - 图片 1945407620649934850: 可用高度57.9点，所需列宽85.2点（含8点边距）
位置 大面1 before阶段 图片1945407620649934850: 宽度需求85.2
位置 大面1 Before阶段最优宽度: 85.2
📦 使用预处理缓存的图片数据: 1940716311947329538
📐 检测结果缓存存在: 1940716311947329538, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716311947329538
🔍 开始基于API数据计算旋转: 1940716311947329538_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=302.0, height=898.0 (API检测: 竖向)
📊 电池区域宽高比: 0.34 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.34)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716311947329538: 预处理旋转判断=True
📏 列宽计算 - 图片 1940716311947329538: 裁剪前768x1024 -> 裁剪后302.0x898.0 -> 旋转后897x301
📏 列宽计算 - 图片 1940716311947329538: 旋转后宽高比2.98, 可用高度57.9点，图片宽度172.7点
📏 列宽计算 - 图片 1940716311947329538: 可用高度57.9点，所需列宽180.7点（含8点边距）
位置 大面1 after阶段 图片1940716311947329538: 宽度需求180.7
📦 使用预处理缓存的图片数据: 1940716397460799490
📐 检测结果缓存存在: 1940716397460799490, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716397460799490
🔍 开始基于API数据计算旋转: 1940716397460799490_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=599.0, height=818.0 (API检测: 竖向)
📊 电池区域宽高比: 0.73 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.73)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716397460799490: 预处理旋转判断=True
📏 列宽计算 - 图片 1940716397460799490: 裁剪前768x1024 -> 裁剪后599.0x818.0 -> 旋转后817x599
📏 列宽计算 - 图片 1940716397460799490: 旋转后宽高比1.36, 可用高度57.9点，图片宽度79.0点
📏 列宽计算 - 图片 1940716397460799490: 可用高度57.9点，所需列宽87.0点（含8点边距）
位置 大面1 after阶段 图片1940716397460799490: 宽度需求87.0
📦 使用预处理缓存的图片数据: 1945407704611512321
📐 检测结果缓存存在: 1945407704611512321, detectionResult != null: True
📐 API尺寸信息: ImageWidth=1024, ImageHeight=768
📐 使用API压缩后尺寸: 1024x768
📦 使用预处理的检测结果: 1945407704611512321
🔍 开始基于API数据计算旋转: 1945407704611512321_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 1, 需要旋转: False
✅ 无EXIF旋转，直接使用API判断
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 1024x768
📊 电池区域像素尺寸: width=496.0, height=570.0 (API检测: 竖向)
📊 电池区域宽高比: 0.87 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.87)
🔄 EXIF需要旋转: False
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1945407704611512321: 预处理旋转判断=True
📏 列宽计算 - 图片 1945407704611512321: 裁剪前1024x768 -> 裁剪后496.0x570.0 -> 旋转后569x496
📏 列宽计算 - 图片 1945407704611512321: 旋转后宽高比1.15, 可用高度57.9点，图片宽度66.5点
📏 列宽计算 - 图片 1945407704611512321: 可用高度57.9点，所需列宽74.5点（含8点边距）
位置 大面1 after阶段 图片1945407704611512321: 宽度需求74.5
位置 大面1 After阶段最优宽度: 180.7
📦 使用预处理缓存的图片数据: 1940716103809187841
📐 检测结果缓存存在: 1940716103809187841, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716103809187841
🔍 开始基于API数据计算旋转: 1940716103809187841_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=768.0, height=1024.0 (API检测: 竖向)
📊 电池区域宽高比: 0.75 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.75)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716103809187841: 预处理旋转判断=True
⚠️ 检测结果无有效裁剪区域: Left=0, Top=0, Right=0, Bottom=0
📏 列宽计算 - 图片 1940716103809187841: 无裁剪信息，使用完整尺寸 768x1024 -> 旋转后1024x768
📏 列宽计算 - 图片 1940716103809187841: 旋转后宽高比1.33, 可用高度57.9点，图片宽度77.2点
📏 列宽计算 - 图片 1940716103809187841: 可用高度57.9点，所需列宽85.2点（含8点边距）
位置 大面2 before阶段 图片1940716103809187841: 宽度需求85.2
📦 使用预处理缓存的图片数据: 1940716180531396610
📐 检测结果缓存存在: 1940716180531396610, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716180531396610
🔍 开始基于API数据计算旋转: 1940716180531396610_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=253.0, height=855.0 (API检测: 竖向)
📊 电池区域宽高比: 0.30 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.30)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716180531396610: 预处理旋转判断=True
📏 列宽计算 - 图片 1940716180531396610: 裁剪前768x1024 -> 裁剪后253.0x855.0 -> 旋转后855x252
📏 列宽计算 - 图片 1940716180531396610: 旋转后宽高比3.39, 可用高度57.9点，图片宽度196.6点
📏 列宽计算 - 图片 1940716180531396610: 可用高度57.9点，所需列宽204.6点（含8点边距）
位置 大面2 before阶段 图片1940716180531396610: 宽度需求204.6
📦 使用预处理缓存的图片数据: 1945407630208753665
📐 检测结果缓存存在: 1945407630208753665, detectionResult != null: True
📐 API尺寸信息: ImageWidth=1024, ImageHeight=768
📐 使用API压缩后尺寸: 1024x768
📦 使用预处理的检测结果: 1945407630208753665
🔍 开始基于API数据计算旋转: 1945407630208753665_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 1, 需要旋转: False
✅ 无EXIF旋转，直接使用API判断
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 1024x768
📊 电池区域像素尺寸: width=273.0, height=154.0 (API检测: 横向)
📊 电池区域宽高比: 1.77 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: False (基于电池区域宽高比 1.77)
🔄 EXIF需要旋转: False
✅ 最终旋转判断: False (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1945407630208753665: 预处理旋转判断=False
📏 列宽计算 - 图片 1945407630208753665: 裁剪前1024x768 -> 裁剪后273.0x154.0 -> 旋转后273x154
📏 列宽计算 - 图片 1945407630208753665: 旋转后宽高比1.77, 可用高度57.9点，图片宽度102.7点
📏 列宽计算 - 图片 1945407630208753665: 可用高度57.9点，所需列宽110.7点（含8点边距）
位置 大面2 before阶段 图片1945407630208753665: 宽度需求110.7
位置 大面2 Before阶段最优宽度: 204.6
📦 使用预处理缓存的图片数据: 1940716321992687617
📐 检测结果缓存存在: 1940716321992687617, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716321992687617
🔍 开始基于API数据计算旋转: 1940716321992687617_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=523.0, height=853.0 (API检测: 竖向)
📊 电池区域宽高比: 0.61 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.61)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716321992687617: 预处理旋转判断=True
📏 列宽计算 - 图片 1940716321992687617: 裁剪前768x1024 -> 裁剪后523.0x853.0 -> 旋转后852x523
📏 列宽计算 - 图片 1940716321992687617: 旋转后宽高比1.63, 可用高度57.9点，图片宽度94.4点
📏 列宽计算 - 图片 1940716321992687617: 可用高度57.9点，所需列宽102.4点（含8点边距）
位置 大面2 after阶段 图片1940716321992687617: 宽度需求102.4
📦 使用预处理缓存的图片数据: 1940716406784737282
📐 检测结果缓存存在: 1940716406784737282, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716406784737282
🔍 开始基于API数据计算旋转: 1940716406784737282_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
线程 24448 已退出，返回值为 0 (0x0)。
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=515.0, height=824.0 (API检测: 竖向)
📊 电池区域宽高比: 0.62 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.62)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716406784737282: 预处理旋转判断=True
📏 列宽计算 - 图片 1940716406784737282: 裁剪前768x1024 -> 裁剪后515.0x824.0 -> 旋转后823x515
📏 列宽计算 - 图片 1940716406784737282: 旋转后宽高比1.60, 可用高度57.9点，图片宽度92.6点
📏 列宽计算 - 图片 1940716406784737282: 可用高度57.9点，所需列宽100.6点（含8点边距）
位置 大面2 after阶段 图片1940716406784737282: 宽度需求100.6
📦 使用预处理缓存的图片数据: 1945407734936330242
📐 检测结果缓存存在: 1945407734936330242, detectionResult != null: True
📐 API尺寸信息: ImageWidth=1024, ImageHeight=768
📐 使用API压缩后尺寸: 1024x768
📦 使用预处理的检测结果: 1945407734936330242
🔍 开始基于API数据计算旋转: 1945407734936330242_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 1, 需要旋转: False
✅ 无EXIF旋转，直接使用API判断
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 1024x768
📊 电池区域像素尺寸: width=460.0, height=535.0 (API检测: 竖向)
📊 电池区域宽高比: 0.86 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.86)
🔄 EXIF需要旋转: False
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1945407734936330242: 预处理旋转判断=True
📏 列宽计算 - 图片 1945407734936330242: 裁剪前1024x768 -> 裁剪后460.0x535.0 -> 旋转后535x460
📏 列宽计算 - 图片 1945407734936330242: 旋转后宽高比1.16, 可用高度57.9点，图片宽度67.4点
📏 列宽计算 - 图片 1945407734936330242: 可用高度57.9点，所需列宽75.4点（含8点边距）
位置 大面2 after阶段 图片1945407734936330242: 宽度需求75.4
位置 大面2 After阶段最优宽度: 102.4
📦 使用预处理缓存的图片数据: 1940716128824016897
📐 检测结果缓存存在: 1940716128824016897, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716128824016897
🔍 开始基于API数据计算旋转: 1940716128824016897_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=730.0, height=890.0 (API检测: 竖向)
📊 电池区域宽高比: 0.82 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.82)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716128824016897: 预处理旋转判断=True
📏 列宽计算 - 图片 1940716128824016897: 裁剪前768x1024 -> 裁剪后730.0x890.0 -> 旋转后890x730
📏 列宽计算 - 图片 1940716128824016897: 旋转后宽高比1.22, 可用高度57.9点，图片宽度70.6点
📏 列宽计算 - 图片 1940716128824016897: 可用高度57.9点，所需列宽78.6点（含8点边距）
位置 工装装配整体 before阶段 图片1940716128824016897: 宽度需求78.6
📦 使用预处理缓存的图片数据: 1940716212726874114
📐 检测结果缓存存在: 1940716212726874114, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716212726874114
🔍 开始基于API数据计算旋转: 1940716212726874114_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=611.0, height=923.0 (API检测: 竖向)
📊 电池区域宽高比: 0.66 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.66)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716212726874114: 预处理旋转判断=True
📏 列宽计算 - 图片 1940716212726874114: 裁剪前768x1024 -> 裁剪后611.0x923.0 -> 旋转后922x611
📏 列宽计算 - 图片 1940716212726874114: 旋转后宽高比1.51, 可用高度57.9点，图片宽度87.4点
📏 列宽计算 - 图片 1940716212726874114: 可用高度57.9点，所需列宽95.4点（含8点边距）
位置 工装装配整体 before阶段 图片1940716212726874114: 宽度需求95.4
📦 使用预处理缓存的图片数据: 1945407654158229505
📐 检测结果缓存存在: 1945407654158229505, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1945407654158229505
🔍 开始基于API数据计算旋转: 1945407654158229505_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=686.0, height=890.0 (API检测: 竖向)
📊 电池区域宽高比: 0.77 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.77)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1945407654158229505: 预处理旋转判断=True
📏 列宽计算 - 图片 1945407654158229505: 裁剪前768x1024 -> 裁剪后686.0x890.0 -> 旋转后890x685
📏 列宽计算 - 图片 1945407654158229505: 旋转后宽高比1.30, 可用高度57.9点，图片宽度75.3点
📏 列宽计算 - 图片 1945407654158229505: 可用高度57.9点，所需列宽83.3点（含8点边距）
位置 工装装配整体 before阶段 图片1945407654158229505: 宽度需求83.3
位置 工装装配整体 Before阶段最优宽度: 95.4
📦 使用预处理缓存的图片数据: 1940716353542242305
📐 检测结果缓存存在: 1940716353542242305, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716353542242305
🔍 开始基于API数据计算旋转: 1940716353542242305_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=648.0, height=928.0 (API检测: 竖向)
📊 电池区域宽高比: 0.70 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.70)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716353542242305: 预处理旋转判断=True
📏 列宽计算 - 图片 1940716353542242305: 裁剪前768x1024 -> 裁剪后648.0x928.0 -> 旋转后928x648
📏 列宽计算 - 图片 1940716353542242305: 旋转后宽高比1.43, 可用高度57.9点，图片宽度83.0点
📏 列宽计算 - 图片 1940716353542242305: 可用高度57.9点，所需列宽91.0点（含8点边距）
位置 工装装配整体 after阶段 图片1940716353542242305: 宽度需求91.0
📦 使用预处理缓存的图片数据: 1940716436128088065
📐 检测结果缓存存在: 1940716436128088065, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716436128088065
🔍 开始基于API数据计算旋转: 1940716436128088065_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=664.0, height=882.0 (API检测: 竖向)
📊 电池区域宽高比: 0.75 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.75)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716436128088065: 预处理旋转判断=True
📏 列宽计算 - 图片 1940716436128088065: 裁剪前768x1024 -> 裁剪后664.0x882.0 -> 旋转后881x663
📏 列宽计算 - 图片 1940716436128088065: 旋转后宽高比1.33, 可用高度57.9点，图片宽度77.0点
📏 列宽计算 - 图片 1940716436128088065: 可用高度57.9点，所需列宽85.0点（含8点边距）
位置 工装装配整体 after阶段 图片1940716436128088065: 宽度需求85.0
📦 使用预处理缓存的图片数据: 1945407769153462273
📐 检测结果缓存存在: 1945407769153462273, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1945407769153462273
🔍 开始基于API数据计算旋转: 1945407769153462273_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
线程 31316 已退出，返回值为 0 (0x0)。
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=707.0, height=929.0 (API检测: 竖向)
📊 电池区域宽高比: 0.76 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.76)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1945407769153462273: 预处理旋转判断=True
📏 列宽计算 - 图片 1945407769153462273: 裁剪前768x1024 -> 裁剪后707.0x929.0 -> 旋转后929x707
📏 列宽计算 - 图片 1945407769153462273: 旋转后宽高比1.31, 可用高度57.9点，图片宽度76.1点
📏 列宽计算 - 图片 1945407769153462273: 可用高度57.9点，所需列宽84.1点（含8点边距）
位置 工装装配整体 after阶段 图片1945407769153462273: 宽度需求84.1
位置 工装装配整体 After阶段最优宽度: 91.0
📦 使用预处理缓存的图片数据: 1940716120393465857
📐 检测结果缓存存在: 1940716120393465857, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716120393465857
🔍 开始基于API数据计算旋转: 1940716120393465857_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=660.0, height=882.0 (API检测: 竖向)
📊 电池区域宽高比: 0.75 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.75)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716120393465857: 预处理旋转判断=True
📏 列宽计算 - 图片 1940716120393465857: 裁剪前768x1024 -> 裁剪后660.0x882.0 -> 旋转后881x660
📏 列宽计算 - 图片 1940716120393465857: 旋转后宽高比1.33, 可用高度57.9点，图片宽度77.3点
📏 列宽计算 - 图片 1940716120393465857: 可用高度57.9点，所需列宽85.3点（含8点边距）
位置 工装装配正面 before阶段 图片1940716120393465857: 宽度需求85.3
📦 使用预处理缓存的图片数据: 1940716204195659777
📐 检测结果缓存存在: 1940716204195659777, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716204195659777
🔍 开始基于API数据计算旋转: 1940716204195659777_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=681.0, height=886.0 (API检测: 竖向)
📊 电池区域宽高比: 0.77 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.77)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716204195659777: 预处理旋转判断=True
📏 列宽计算 - 图片 1940716204195659777: 裁剪前768x1024 -> 裁剪后681.0x886.0 -> 旋转后885x681
📏 列宽计算 - 图片 1940716204195659777: 旋转后宽高比1.30, 可用高度57.9点，图片宽度75.3点
📏 列宽计算 - 图片 1940716204195659777: 可用高度57.9点，所需列宽83.3点（含8点边距）
位置 工装装配正面 before阶段 图片1940716204195659777: 宽度需求83.3
📦 使用预处理缓存的图片数据: 1945407641915056130
📐 检测结果缓存存在: 1945407641915056130, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1945407641915056130
🔍 开始基于API数据计算旋转: 1945407641915056130_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=652.0, height=882.0 (API检测: 竖向)
📊 电池区域宽高比: 0.74 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.74)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1945407641915056130: 预处理旋转判断=True
📏 列宽计算 - 图片 1945407641915056130: 裁剪前768x1024 -> 裁剪后652.0x882.0 -> 旋转后881x651
📏 列宽计算 - 图片 1945407641915056130: 旋转后宽高比1.35, 可用高度57.9点，图片宽度78.4点
📏 列宽计算 - 图片 1945407641915056130: 可用高度57.9点，所需列宽86.4点（含8点边距）
位置 工装装配正面 before阶段 图片1945407641915056130: 宽度需求86.4
位置 工装装配正面 Before阶段最优宽度: 86.4
📦 使用预处理缓存的图片数据: 1940716344088281089
📐 检测结果缓存存在: 1940716344088281089, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716344088281089
🔍 开始基于API数据计算旋转: 1940716344088281089_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=643.0, height=921.0 (API检测: 竖向)
📊 电池区域宽高比: 0.70 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.70)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716344088281089: 预处理旋转判断=True
📏 列宽计算 - 图片 1940716344088281089: 裁剪前768x1024 -> 裁剪后643.0x921.0 -> 旋转后920x643
📏 列宽计算 - 图片 1940716344088281089: 旋转后宽高比1.43, 可用高度57.9点，图片宽度82.9点
📏 列宽计算 - 图片 1940716344088281089: 可用高度57.9点，所需列宽90.9点（含8点边距）
位置 工装装配正面 after阶段 图片1940716344088281089: 宽度需求90.9
📦 使用预处理缓存的图片数据: 1940716427282300930
📐 检测结果缓存存在: 1940716427282300930, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1940716427282300930
🔍 开始基于API数据计算旋转: 1940716427282300930_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=651.0, height=926.0 (API检测: 竖向)
📊 电池区域宽高比: 0.70 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.70)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1940716427282300930: 预处理旋转判断=True
📏 列宽计算 - 图片 1940716427282300930: 裁剪前768x1024 -> 裁剪后651.0x926.0 -> 旋转后926x650
📏 列宽计算 - 图片 1940716427282300930: 旋转后宽高比1.42, 可用高度57.9点，图片宽度82.5点
📏 列宽计算 - 图片 1940716427282300930: 可用高度57.9点，所需列宽90.5点（含8点边距）
位置 工装装配正面 after阶段 图片1940716427282300930: 宽度需求90.5
📦 使用预处理缓存的图片数据: 1945407758789337089
📐 检测结果缓存存在: 1945407758789337089, detectionResult != null: True
📐 API尺寸信息: ImageWidth=768, ImageHeight=1024
📐 使用API压缩后尺寸: 768x1024
📦 使用预处理的检测结果: 1945407758789337089
🔍 开始基于API数据计算旋转: 1945407758789337089_columnWidth (电芯列宽计算-未知位置-列宽计算阶段)
🎯 检测策略: 直接基于API电池区域宽高比判断
📐 EXIF Orientation: 6, 需要旋转: True
🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
ℹ️ API判断基于修正后的图片尺寸，无需额外处理
🎯 基于电池区域宽高比判断旋转 (电芯列宽计算-未知位置-列宽计算阶段):
📊 API图片尺寸: 768x1024
📊 电池区域像素尺寸: width=697.0, height=895.0 (API检测: 竖向)
📊 电池区域宽高比: 0.78 (寽高比 < 1.0 时为竖向，需要旋转)
🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
🎯 API判断结果: True (基于电池区域宽高比 0.78)
🔄 EXIF需要旋转: True
✅ 最终旋转判断: True (考虑EXIF旋转影响)
📏 列宽计算 - 图片 1945407758789337089: 预处理旋转判断=True
📏 列宽计算 - 图片 1945407758789337089: 裁剪前768x1024 -> 裁剪后697.0x895.0 -> 旋转后894x696
📏 列宽计算 - 图片 1945407758789337089: 旋转后宽高比1.28, 可用高度57.9点，图片宽度74.4点
📏 列宽计算 - 图片 1945407758789337089: 可用高度57.9点，所需列宽82.4点（含8点边距）
位置 工装装配正面 after阶段 图片1945407758789337089: 宽度需求82.4
位置 工装装配正面 After阶段最优宽度: 90.9
Before列3(vent面): 使用宽度99.3
Before列4(大面1): 使用宽度85.2
Before列5(大面2): 使用宽度204.6
Before列6(工装装配整体): 使用宽度95.4
Before列7(工装装配正面): 使用宽度86.4
After列8(vent面): 使用宽度90.7
After列9(大面1): 使用宽度180.7
After列10(大面2): 使用宽度102.4
After列11(工装装配整体): 使用宽度91.0
After列12(工装装配正面): 使用宽度90.9
📏 采用完全基于图片需求的列宽设置策略
列3设置为图片所需宽度: 99.3点
列4设置为图片所需宽度: 85.2点
列5设置为图片所需宽度: 204.6点
列6设置为图片所需宽度: 95.4点
列7设置为图片所需宽度: 86.4点
列8设置为图片所需宽度: 90.7点
列9设置为图片所需宽度: 180.7点
列10设置为图片所需宽度: 102.4点
列11设置为图片所需宽度: 91.0点
列12设置为图片所需宽度: 90.9点
📊 表格宽度: 1346.6点，超出可用宽度960.0点 (1.4倍)
✅ 优先保证图片2.1cm高度，表格将自动调整宽度
📏 跳过边界调整，完全使用图片所需列宽
列宽计算完成:
- 总列数: 13
- 可用宽度: 960.0
- 实际总宽度: 1346.6
- 固定列宽度: 220.0
- 图片列总需求: 1126.6
- 列1: 80.0
- 列2: 60.0
- 列13: 80.0
- 列3: 99.3
- 列4: 85.2
- 列5: 204.6
- 列6: 95.4
- 列7: 86.4
- 列8: 90.7
- 列9: 180.7
- 列10: 102.4
- 列11: 91.0
- 列12: 90.9
  ⚠️ 表格宽度1346.6超出幻灯片95%宽度912.0，但优先保证图片质量
  📊 表格尺寸计算: 需求宽度=1346.6, 实际宽度=1346.6
  📍 表格位置: Left=10.0, Top=135.6, Width=1346.6, Height=268.8
  开始创建表格
  表格创建成功
  开始设置表格样式
  开始设置表格样式（使用兼容方法）
  线程 24604 已退出，返回值为 0 (0x0)。
  应用内置表格样式成功
  开始设置表格边框
  设置边框颜色: #7f7f7f, 表格尺寸: 5x13
  线程 20508 已退出，返回值为 0 (0x0)。
  表格边框设置完成
  表格边框设置完成
  开始使用优化的样式设置方法
  优化样式设置完成
  表格样式设置完成
  开始设置列宽（使用预计算结果）
  列1宽度设置为: 80.0
  列2宽度设置为: 60.0
  列3宽度设置为: 99.3
  列4宽度设置为: 85.2
  列5宽度设置为: 204.6
  列6宽度设置为: 95.4
  列7宽度设置为: 86.4
  列8宽度设置为: 90.7
  列9宽度设置为: 180.7
  列10宽度设置为: 102.4
  列11宽度设置为: 91.0
  列12宽度设置为: 90.9
  列13宽度设置为: 80.0
  列宽设置完成
  表格样式设置成功
  开始填充表头
  表头填充成功
  开始填充数据行
  线程 22748 已退出，返回值为 0 (0x0)。
  === PPT表格数据诊断 ===
  委托单号: 202507230001
  测试项目: 过放电1
  照片部位: [vent面, 大面1, 大面2, 工装装配整体, 工装装配正面]
  测试阶段: [before, after]
  电芯行数: 3
  数据概览: 3个电芯, 部位:vent面,大面1,大面2,工装装配整体,工装装配正面, 阶段:before,after
  引发的异常:“System.ArgumentException”(位于 PBIppt.dll 中)
  设置Cell No单元格样式失败: 指定的值超出了范围。
  引发的异常:“System.ArgumentException”(位于 PBIppt.dll 中)
  线程 23296 已退出，返回值为 0 (0x0)。
  设置Cell No单元格样式失败: 指定的值超出了范围。
  引发的异常:“System.ArgumentException”(位于 PBIppt.dll 中)
  设置Cell No单元格样式失败: 指定的值超出了范围。
  🔄 开始批量处理 30 张图片
  📦 使用缓存图片: 1947918863608123393
  📦 使用缓存图片: 1940716094942429185
  📦 使用缓存图片: 1940716103809187841
  📦 使用缓存图片: 1940716128824016897
  📦 使用缓存图片: 1940716120393465857
  📦 使用缓存图片: 1940716292083105793
  📦 使用缓存图片: 1940716311947329538
  📦 使用缓存图片: 1940716321992687617
  📦 使用缓存图片: 1940716353542242305
  📦 使用缓存图片: 1940716344088281089
  📦 使用缓存图片: 1940716163452190722
  📦 使用缓存图片: 1940716171899518978
  📦 使用缓存图片: 1940716180531396610
  📦 使用缓存图片: 1940716212726874114
  📦 使用缓存图片: 1940716204195659777
  📦 使用缓存图片: 1940716386140372994
  📦 使用缓存图片: 1940716397460799490
  📦 使用缓存图片: 1940716406784737282
  📦 使用缓存图片: 1940716436128088065
  📦 使用缓存图片: 1940716427282300930
  📦 使用缓存图片: 1945407610642325505
  📦 使用缓存图片: 1945407620649934850
  📦 使用缓存图片: 1945407630208753665
  📦 使用缓存图片: 1945407654158229505
  📦 使用缓存图片: 1945407641915056130
  📦 使用缓存图片: 1945407695237242882
  📦 使用缓存图片: 1945407704611512321
  📦 使用缓存图片: 1945407734936330242
  📦 使用缓存图片: 1945407769153462273
  📦 使用缓存图片: 1945407758789337089
  📥 批量获取完成，成功获取 30/30 张图片
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  2025-07-29 10:07:00 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
  2025-07-29 10:07:00 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4032x3024
  2025-07-29 10:07:00 [INFO] ✅ 图片压缩成功: 4032x3024 -> 1024x768 (大小: 1061KB -> 60KB)
  2025-07-29 10:07:01 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:01 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:01 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
  2025-07-29 10:07:01 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
  2025-07-29 10:07:01 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  线程 12720 已退出，返回值为 0 (0x0)。
  线程 33132 已退出，返回值为 0 (0x0)。
  2025-07-29 10:07:01 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2689KB -> 105KB)
  2025-07-29 10:07:01 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:01 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:01 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
  2025-07-29 10:07:01 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
  2025-07-29 10:07:01 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:01 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2354KB -> 99KB)
  2025-07-29 10:07:01 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:01 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:01 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
  2025-07-29 10:07:02 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
  2025-07-29 10:07:02 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:02 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 6125KB -> 192KB)
  2025-07-29 10:07:02 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:02 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:02 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
  线程 24484 已退出，返回值为 0 (0x0)。
  2025-07-29 10:07:02 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
  2025-07-29 10:07:02 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:03 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 6218KB -> 176KB)
  2025-07-29 10:07:03 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:03 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:03 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
  2025-07-29 10:07:03 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
  2025-07-29 10:07:03 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:03 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2478KB -> 116KB)
  2025-07-29 10:07:03 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:03 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:03 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
  2025-07-29 10:07:03 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
  2025-07-29 10:07:03 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  线程 20532 已退出，返回值为 0 (0x0)。
  2025-07-29 10:07:03 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2448KB -> 152KB)
  2025-07-29 10:07:03 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:03 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:03 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
  2025-07-29 10:07:03 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
  2025-07-29 10:07:03 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  线程 9920 已退出，返回值为 0 (0x0)。
  2025-07-29 10:07:03 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 3080KB -> 144KB)
  线程 33016 已退出，返回值为 0 (0x0)。
  线程 1536 已退出，返回值为 0 (0x0)。
  线程 15024 已退出，返回值为 0 (0x0)。
  2025-07-29 10:07:04 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:04 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:04 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
  2025-07-29 10:07:04 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
  2025-07-29 10:07:04 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  线程 5216 已退出，返回值为 0 (0x0)。
  2025-07-29 10:07:04 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 6017KB -> 193KB)
  线程 24640 已退出，返回值为 0 (0x0)。
  2025-07-29 10:07:04 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:04 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:04 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
  2025-07-29 10:07:04 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
  2025-07-29 10:07:04 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:04 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 6326KB -> 206KB)
  2025-07-29 10:07:04 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:05 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:05 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
  2025-07-29 10:07:05 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
  2025-07-29 10:07:05 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:05 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2791KB -> 101KB)
  2025-07-29 10:07:05 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:05 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:05 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
  2025-07-29 10:07:06 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
  2025-07-29 10:07:06 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:06 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2502KB -> 106KB)
  2025-07-29 10:07:06 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:06 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:06 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
  2025-07-29 10:07:06 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
  2025-07-29 10:07:06 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:06 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2220KB -> 97KB)
  2025-07-29 10:07:06 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:06 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:06 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
  2025-07-29 10:07:07 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
  2025-07-29 10:07:07 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:07 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 6683KB -> 178KB)
  2025-07-29 10:07:07 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:07 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:07 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
  线程 10680 已退出，返回值为 0 (0x0)。
  2025-07-29 10:07:07 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
  2025-07-29 10:07:07 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:07 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 6617KB -> 173KB)
  2025-07-29 10:07:08 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:08 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:08 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
  2025-07-29 10:07:08 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
  2025-07-29 10:07:08 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:08 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2594KB -> 124KB)
  2025-07-29 10:07:08 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:08 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:08 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
  2025-07-29 10:07:08 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
  2025-07-29 10:07:08 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:08 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2541KB -> 151KB)
  2025-07-29 10:07:08 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:08 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:08 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 3024x4032
  2025-07-29 10:07:08 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 3024x4032, System.Drawing: 4032x3024
  2025-07-29 10:07:08 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:08 [INFO] ✅ 图片压缩成功: 3024x4032 -> 768x1024 (大小: 2561KB -> 154KB)
  2025-07-29 10:07:08 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:08 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:08 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
  2025-07-29 10:07:08 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
  2025-07-29 10:07:08 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:09 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 6940KB -> 188KB)
  2025-07-29 10:07:09 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:09 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:09 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
  2025-07-29 10:07:09 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
  2025-07-29 10:07:09 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:09 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 6982KB -> 187KB)
  2025-07-29 10:07:09 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
  2025-07-29 10:07:09 [INFO] 📐 图片尺寸: 5712x4284 (无EXIF旋转)
  2025-07-29 10:07:09 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 5712x4284
  2025-07-29 10:07:10 [INFO] ✅ 图片压缩成功: 5712x4284 -> 1024x768 (大小: 5920KB -> 68KB)
  2025-07-29 10:07:10 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
  2025-07-29 10:07:10 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
  2025-07-29 10:07:10 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4032x3024
  2025-07-29 10:07:10 [INFO] ✅ 图片压缩成功: 4032x3024 -> 1024x768 (大小: 3215KB -> 97KB)
  2025-07-29 10:07:10 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
  2025-07-29 10:07:10 [INFO] 📐 图片尺寸: 5712x4284 (无EXIF旋转)
  2025-07-29 10:07:10 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 5712x4284
  2025-07-29 10:07:10 [INFO] ✅ 图片压缩成功: 5712x4284 -> 1024x768 (大小: 6397KB -> 84KB)
  2025-07-29 10:07:10 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:10 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:10 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
  2025-07-29 10:07:10 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
  2025-07-29 10:07:10 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:11 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 5288KB -> 180KB)
  2025-07-29 10:07:11 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:11 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:11 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
  2025-07-29 10:07:11 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
  2025-07-29 10:07:11 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:11 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 5664KB -> 191KB)
  2025-07-29 10:07:11 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
  2025-07-29 10:07:11 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
  2025-07-29 10:07:11 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4032x3024
  2025-07-29 10:07:11 [INFO] ✅ 图片压缩成功: 4032x3024 -> 1024x768 (大小: 3251KB -> 108KB)
  2025-07-29 10:07:11 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
  2025-07-29 10:07:11 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
  2025-07-29 10:07:11 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4032x3024
  2025-07-29 10:07:11 [INFO] ✅ 图片压缩成功: 4032x3024 -> 1024x768 (大小: 2904KB -> 103KB)
  2025-07-29 10:07:12 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
  2025-07-29 10:07:12 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
  2025-07-29 10:07:12 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4032x3024
  2025-07-29 10:07:12 [INFO] ✅ 图片压缩成功: 4032x3024 -> 1024x768 (大小: 2918KB -> 100KB)
  2025-07-29 10:07:12 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:12 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:12 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
  2025-07-29 10:07:12 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
  2025-07-29 10:07:12 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:12 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 5944KB -> 204KB)
  2025-07-29 10:07:12 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:12 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:12 [INFO] 🔍 图片压缩 - 检测到的图片尺寸: 4284x5712
  2025-07-29 10:07:12 [WARNING] ⚠️ 图片尺寸检测不一致 - 真实尺寸: 4284x5712, System.Drawing: 5712x4284
  2025-07-29 10:07:12 [WARNING] ⚠️ 可能受加密系统影响，使用真实尺寸进行压缩计算
  2025-07-29 10:07:13 [INFO] ✅ 图片压缩成功: 4284x5712 -> 768x1024 (大小: 5746KB -> 197KB)
  开始检测多张图片，数量: 30
  添加图片ID列表: ["1947918863608123393","1940716094942429185","1940716103809187841","1940716128824016897","1940716120393465857","1940716292083105793","1940716311947329538","1940716321992687617","1940716353542242305","1940716344088281089","1940716163452190722","1940716171899518978","1940716180531396610","1940716212726874114","1940716204195659777","1940716386140372994","1940716397460799490","1940716406784737282","1940716436128088065","1940716427282300930","1945407610642325505","1945407620649934850","1945407630208753665","1945407654158229505","1945407641915056130","1945407695237242882","1945407704611512321","1945407734936330242","1945407769153462273","1945407758789337089"]
  发送批量检测请求到: http://************:8011/detect/
  服务器响应状态: OK
  === API原始响应内容 ===
  {"results":[{"image_id":"1947918863608123393","CropLeft":354,"CropTop":356,"CropRight":496,"CropBottom":238,"CropLeftPercent":0.345703,"CropTopPercent":0.463542,"CropRightPercent":0.484375,"CropBottomPercent":0.309896,"center_x_percent":0.430664,"center_y_percent":0.576823,"width_percent":0.169922,"height_percent":0.226562,"confidence":0.9106835722923279,"image_info":{"image_id":"1947918863608123393","width":1024,"height":768,"format":"JPEG","size_bytes":61629,"dpi":[120,120]}},{"image_id":"1940716094942429185","CropLeft":305,"CropTop":94,"CropRight":222,"CropBottom":111,"CropLeftPercent":0.397135,"CropTopPercent":0.091797,"CropRightPercent":0.289062,"CropBottomPercent":0.108398,"center_x_percent":0.554036,"center_y_percent":0.491699,"width_percent":0.313802,"height_percent":0.799805,"confidence":0.7270834445953369,"image_info":{"image_id":"1940716094942429185","width":768,"height":1024,"format":"JPEG","size_bytes":108480,"dpi":[120,120]}},{"image_id":"1940716103809187841","CropLeft":321,"CropTop":127,"CropRight":180,"CropBottom":90,"CropLeftPercent":0.417969,"CropTopPercent":0.124023,"CropRightPercent":0.234375,"CropBottomPercent":0.087891,"center_x_percent":0.591797,"center_y_percent":0.518066,"width_percent":0.347656,"height_percent":0.788086,"confidence":0.8227367997169495,"image_info":{"image_id":"1940716103809187841","width":768,"height":1024,"format":"JPEG","size_bytes":102269,"dpi":[120,120]}},{"image_id":"1940716128824016897","CropLeft":41,"CropTop":0,"CropRight":0,"CropBottom":139,"CropLeftPercent":0.053385,"CropTopPercent":0.0,"CropRightPercent":0.0,"CropBottomPercent":0.135742,"center_x_percent":0.526693,"center_y_percent":0.432129,"width_percent":0.946615,"height_percent":0.864258,"confidence":0.8460705280303955,"image_info":{"image_id":"1940716128824016897","width":768,"height":1024,"format":"JPEG","size_bytes":197089,"dpi":[120,120]}},{"image_id":"1940716120393465857","CropLeft":101,"CropTop":0,"CropRight":0,"CropBottom":139,"CropLeftPercent":0.13151,"CropTopPercent":0.0,"CropRightPercent":0.0,"CropBottomPercent":0.135742,"center_x_percent":0.565755,"center_y_percent":0.432129,"width_percent":0.86849,"height_percent":0.864258,"confidence":0.8960005640983582,"image_info":{"image_id":"1940716120393465857","width":768,"height":1024,"format":"JPEG","size_bytes":181176,"dpi":[120,120]}},{"image_id":"1940716292083105793","CropLeft":155,"CropTop":7,"CropRight":0,"CropBottom":180,"CropLeftPercent":0.201823,"CropTopPercent":0.006836,"CropRightPercent":0.0,"CropBottomPercent":0.175781,"center_x_percent":0.600911,"center_y_percent":0.415527,"width_percent":0.798177,"height_percent":0.817383,"confidence":0.7423936724662781,"image_info":{"image_id":"1940716292083105793","width":768,"height":1024,"format":"JPEG","size_bytes":119273,"dpi":[120,120]}},{"image_id":"1940716311947329538","CropLeft":241,"CropTop":123,"CropRight":216,"CropBottom":36,"CropLeftPercent":0.313802,"CropTopPercent":0.120117,"CropRightPercent":0.28125,"CropBottomPercent":0.035156,"center_x_percent":0.516276,"center_y_percent":0.54248,"width_percent":0.404948,"height_percent":0.844727,"confidence":0.8601300716400146,"image_info":{"image_id":"1940716311947329538","width":768,"height":1024,"format":"JPEG","size_bytes":155672,"dpi":[120,120]}},{"image_id":"1940716321992687617","CropLeft":238,"CropTop":81,"CropRight":147,"CropBottom":86,"CropLeftPercent":0.309896,"CropTopPercent":0.079102,"CropRightPercent":0.191406,"CropBottomPercent":0.083984,"center_x_percent":0.559245,"center_y_percent":0.497559,"width_percent":0.498698,"height_percent":0.836914,"confidence":0.8269150853157043,"image_info":{"image_id":"1940716321992687617","width":768,"height":1024,"format":"JPEG","size_bytes":147560,"dpi":[120,120]}},{"image_id":"1940716353542242305","CropLeft":127,"CropTop":0,"CropRight":0,"CropBottom":143,"CropLeftPercent":0.165365,"CropTopPercent":0.0,"CropRightPercent":0.0,"CropBottomPercent":0.139648,"center_x_percent":0.582682,"center_y_percent":0.430176,"width_percent":0.834635,"height_percent":0.860352,"confidence":0.9084040522575378,"image_info":{"image_id":"1940716353542242305","width":768,"height":1024,"format":"JPEG","size_bytes":197719,"dpi":[120,120]}},{"image_id":"1940716344088281089","CropLeft":139,"CropTop":0,"CropRight":0,"CropBottom":140,"CropLeftPercent":0.18099,"CropTopPercent":0.0,"CropRightPercent":0.0,"CropBottomPercent":0.136719,"center_x_percent":0.590495,"center_y_percent":0.431641,"width_percent":0.81901,"height_percent":0.863281,"confidence":0.8579598665237427,"image_info":{"image_id":"1940716344088281089","width":768,"height":1024,"format":"JPEG","size_bytes":211956,"dpi":[120,120]}},{"image_id":"1940716163452190722","CropLeft":237,"CropTop":255,"CropRight":203,"CropBottom":258,"CropLeftPercent":0.308594,"CropTopPercent":0.249023,"CropRightPercent":0.264323,"CropBottomPercent":0.251953,"center_x_percent":0.522135,"center_y_percent":0.498535,"width_percent":0.427083,"height_percent":0.499023,"confidence":0.9025000333786011,"image_info":{"image_id":"1940716163452190722","width":768,"height":1024,"format":"JPEG","size_bytes":104158,"dpi":[120,120]}},{"image_id":"1940716171899518978","CropLeft":0,"CropTop":109,"CropRight":180,"CropBottom":161,"CropLeftPercent":0.0,"CropTopPercent":0.106445,"CropRightPercent":0.234375,"CropBottomPercent":0.157227,"center_x_percent":0.382812,"center_y_percent":0.474609,"width_percent":0.765625,"height_percent":0.736328,"confidence":0.727225124835968,"image_info":{"image_id":"1940716171899518978","width":768,"height":1024,"format":"JPEG","size_bytes":109320,"dpi":[120,120]}},{"image_id":"1940716180531396610","CropLeft":306,"CropTop":54,"CropRight":223,"CropBottom":82,"CropLeftPercent":0.398438,"CropTopPercent":0.052734,"CropRightPercent":0.290365,"CropBottomPercent":0.080078,"center_x_percent":0.554036,"center_y_percent":0.486328,"width_percent":0.311198,"height_percent":0.867188,"confidence":0.792181134223938,"image_info":{"image_id":"1940716180531396610","width":768,"height":1024,"format":"JPEG","size_bytes":99973,"dpi":[120,120]}},{"image_id":"1940716212726874114","CropLeft":163,"CropTop":11,"CropRight":0,"CropBottom":85,"CropLeftPercent":0.21224,"CropTopPercent":0.010742,"CropRightPercent":0.0,"CropBottomPercent":0.083008,"center_x_percent":0.60612,"center_y_percent":0.463867,"width_percent":0.78776,"height_percent":0.90625,"confidence":0.8581046462059021,"image_info":{"image_id":"1940716212726874114","width":768,"height":1024,"format":"JPEG","size_bytes":183152,"dpi":[120,120]}},{"image_id":"1940716204195659777","CropLeft":83,"CropTop":0,"CropRight":0,"CropBottom":132,"CropLeftPercent":0.108073,"CropTopPercent":0.0,"CropRightPercent":0.0,"CropBottomPercent":0.128906,"center_x_percent":0.554036,"center_y_percent":0.435547,"width_percent":0.891927,"height_percent":0.871094,"confidence":0.8996822834014893,"image_info":{"image_id":"1940716204195659777","width":768,"height":1024,"format":"JPEG","size_bytes":177362,"dpi":[120,120]}},{"image_id":"1940716386140372994","CropLeft":182,"CropTop":194,"CropRight":81,"CropBottom":10,"CropLeftPercent":0.236979,"CropTopPercent":0.189453,"CropRightPercent":0.105469,"CropBottomPercent":0.009766,"center_x_percent":0.565755,"center_y_percent":0.589844,"width_percent":0.657552,"height_percent":0.800781,"confidence":0.781963586807251,"image_info":{"image_id":"1940716386140372994","width":768,"height":1024,"format":"JPEG","size_bytes":127055,"dpi":[120,120]}},{"image_id":"1940716397460799490","CropLeft":178,"CropTop":100,"CropRight":224,"CropBottom":51,"CropLeftPercent":0.231771,"CropTopPercent":0.097656,"CropRightPercent":0.291667,"CropBottomPercent":0.049805,"center_x_percent":0.470052,"center_y_percent":0.523926,"width_percent":0.476562,"height_percent":0.852539,"confidence":0.824988842010498,"image_info":{"image_id":"1940716397460799490","width":768,"height":1024,"format":"JPEG","size_bytes":155139,"dpi":[120,120]}},{"image_id":"1940716406784737282","CropLeft":251,"CropTop":98,"CropRight":0,"CropBottom":103,"CropLeftPercent":0.326823,"CropTopPercent":0.095703,"CropRightPercent":0.0,"CropBottomPercent":0.100586,"center_x_percent":0.663411,"center_y_percent":0.497559,"width_percent":0.673177,"height_percent":0.803711,"confidence":0.8921602368354797,"image_info":{"image_id":"1940716406784737282","width":768,"height":1024,"format":"JPEG","size_bytes":157999,"dpi":[120,120]}},{"image_id":"1940716436128088065","CropLeft":96,"CropTop":0,"CropRight":0,"CropBottom":138,"CropLeftPercent":0.125,"CropTopPercent":0.0,"CropRightPercent":0.0,"CropBottomPercent":0.134766,"center_x_percent":0.5625,"center_y_percent":0.432617,"width_percent":0.875,"height_percent":0.865234,"confidence":0.9023276567459106,"image_info":{"image_id":"1940716436128088065","width":768,"height":1024,"format":"JPEG","size_bytes":192895,"dpi":[120,120]}},{"image_id":"1940716427282300930","CropLeft":125,"CropTop":0,"CropRight":0,"CropBottom":143,"CropLeftPercent":0.16276,"CropTopPercent":0.0,"CropRightPercent":0.0,"CropBottomPercent":0.139648,"center_x_percent":0.58138,"center_y_percent":0.430176,"width_percent":0.83724,"height_percent":0.860352,"confidence":0.9010236859321594,"image_info":{"image_id":"1940716427282300930","width":768,"height":1024,"format":"JPEG","size_bytes":191763,"dpi":[120,120]}},{"image_id":"1945407610642325505","CropLeft":0,"CropTop":0,"CropRight":0,"CropBottom":0,"CropLeftPercent":0.0,"CropTopPercent":0.0,"CropRightPercent":0.0,"CropBottomPercent":0.0,"center_x_percent":0.5,"center_y_percent":0.5,"width_percent":1.0,"height_percent":1.0,"confidence":0.0,"image_info":{"image_id":"1945407610642325505","width":1024,"height":768,"format":"JPEG","size_bytes":69790,"dpi":[120,120]}},{"image_id":"1945407620649934850","CropLeft":0,"CropTop":0,"CropRight":0,"CropBottom":0,"CropLeftPercent":0.0,"CropTopPercent":0.0,"CropRightPercent":0.0,"CropBottomPercent":0.0,"center_x_percent":0.5,"center_y_percent":0.5,"width_percent":1.0,"height_percent":1.0,"confidence":0.0,"image_info":{"image_id":"1945407620649934850","width":1024,"height":768,"format":"JPEG","size_bytes":99420,"dpi":[120,120]}},{"image_id":"1945407630208753665","CropLeft":391,"CropTop":315,"CropRight":352,"CropBottom":238,"CropLeftPercent":0.381836,"CropTopPercent":0.410156,"CropRightPercent":0.34375,"CropBottomPercent":0.309896,"center_x_percent":0.519043,"center_y_percent":0.55013,"width_percent":0.274414,"height_percent":0.279948,"confidence":0.6180332899093628,"image_info":{"image_id":"1945407630208753665","width":1024,"height":768,"format":"JPEG","size_bytes":86366,"dpi":[120,120]}},{"image_id":"1945407654158229505","CropLeft":82,"CropTop":8,"CropRight":0,"CropBottom":83,"CropLeftPercent":0.106771,"CropTopPercent":0.007812,"CropRightPercent":0.0,"CropBottomPercent":0.081055,"center_x_percent":0.553385,"center_y_percent":0.463379,"width_percent":0.893229,"height_percent":0.911133,"confidence":0.8725566864013672,"image_info":{"image_id":"1945407654158229505","width":768,"height":1024,"format":"JPEG","size_bytes":184475,"dpi":[120,120]}},{"image_id":"1945407641915056130","CropLeft":117,"CropTop":0,"CropRight":0,"CropBottom":141,"CropLeftPercent":0.152344,"CropTopPercent":0.0,"CropRightPercent":0.0,"CropBottomPercent":0.137695,"center_x_percent":0.576172,"center_y_percent":0.431152,"width_percent":0.847656,"height_percent":0.862305,"confidence":0.8894518613815308,"image_info":{"image_id":"1945407641915056130","width":768,"height":1024,"format":"JPEG","size_bytes":196260,"dpi":[120,120]}},{"image_id":"1945407695237242882","CropLeft":0,"CropTop":0,"CropRight":0,"CropBottom":0,"CropLeftPercent":0.0,"CropTopPercent":0.0,"CropRightPercent":0.0,"CropBottomPercent":0.0,"center_x_percent":0.5,"center_y_percent":0.5,"width_percent":1.0,"height_percent":1.0,"confidence":0.0,"image_info":{"image_id":"1945407695237242882","width":1024,"height":768,"format":"JPEG","size_bytes":110797,"dpi":[120,120]}},{"image_id":"1945407704611512321","CropLeft":260,"CropTop":35,"CropRight":271,"CropBottom":206,"CropLeftPercent":0.253906,"CropTopPercent":0.045573,"CropRightPercent":0.264648,"CropBottomPercent":0.268229,"center_x_percent":0.494629,"center_y_percent":0.388672,"width_percent":0.481445,"height_percent":0.686198,"confidence":0.8975179195404053,"image_info":{"image_id":"1945407704611512321","width":1024,"height":768,"format":"JPEG","size_bytes":105732,"dpi":[120,120]}},{"image_id":"1945407734936330242","CropLeft":329,"CropTop":58,"CropRight":278,"CropBottom":206,"CropLeftPercent":0.321289,"CropTopPercent":0.075521,"CropRightPercent":0.271484,"CropBottomPercent":0.268229,"center_x_percent":0.524902,"center_y_percent":0.403646,"width_percent":0.407227,"height_percent":0.65625,"confidence":0.7023972868919373,"image_info":{"image_id":"1945407734936330242","width":1024,"height":768,"format":"JPEG","size_bytes":103127,"dpi":[120,120]}},{"image_id":"1945407769153462273","CropLeft":178,"CropTop":11,"CropRight":0,"CropBottom":83,"CropLeftPercent":0.231771,"CropTopPercent":0.010742,"CropRightPercent":0.0,"CropBottomPercent":0.081055,"center_x_percent":0.615885,"center_y_percent":0.464844,"width_percent":0.768229,"height_percent":0.908203,"confidence":0.8423284292221069,"image_info":{"image_id":"1945407769153462273","width":768,"height":1024,"format":"JPEG","size_bytes":209144,"dpi":[120,120]}},{"image_id":"1945407758789337089","CropLeft":197,"CropTop":6,"CropRight":0,"CropBottom":80,"CropLeftPercent":0.25651,"CropTopPercent":0.005859,"CropRightPercent":0.0,"CropBottomPercent":0.078125,"center_x_percent":0.628255,"center_y_percent":0.463867,"width_percent":0.74349,"height_percent":0.916016,"confidence":0.8676471710205078,"image_info":{"image_id":"1945407758789337089","width":768,"height":1024,"format":"JPEG","size_bytes":202455,"dpi":[120,120]}}]}
  === API响应内容结束 ===
  解析image_info - 图片ID: 1947918863608123393, 尺寸: 1024x768
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1947918863608123393, 裁剪区域: (354, 356, 496, 238), 百分比: (0.346, 0.464, 0.484, 0.310), DPI: 120x120
  解析image_info - 图片ID: 1940716094942429185, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716094942429185, 裁剪区域: (305, 94, 222, 111), 百分比: (0.397, 0.092, 0.289, 0.108), DPI: 120x120
  解析image_info - 图片ID: 1940716103809187841, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716103809187841, 裁剪区域: (321, 127, 180, 90), 百分比: (0.418, 0.124, 0.234, 0.088), DPI: 120x120
  解析image_info - 图片ID: 1940716128824016897, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716128824016897, 裁剪区域: (41, 0, 0, 139), 百分比: (0.053, 0.000, 0.000, 0.136), DPI: 120x120
  解析image_info - 图片ID: 1940716120393465857, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716120393465857, 裁剪区域: (101, 0, 0, 139), 百分比: (0.132, 0.000, 0.000, 0.136), DPI: 120x120
  解析image_info - 图片ID: 1940716292083105793, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716292083105793, 裁剪区域: (155, 7, 0, 180), 百分比: (0.202, 0.007, 0.000, 0.176), DPI: 120x120
  解析image_info - 图片ID: 1940716311947329538, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716311947329538, 裁剪区域: (241, 123, 216, 36), 百分比: (0.314, 0.120, 0.281, 0.035), DPI: 120x120
  解析image_info - 图片ID: 1940716321992687617, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716321992687617, 裁剪区域: (238, 81, 147, 86), 百分比: (0.310, 0.079, 0.191, 0.084), DPI: 120x120
  解析image_info - 图片ID: 1940716353542242305, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716353542242305, 裁剪区域: (127, 0, 0, 143), 百分比: (0.165, 0.000, 0.000, 0.140), DPI: 120x120
  解析image_info - 图片ID: 1940716344088281089, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716344088281089, 裁剪区域: (139, 0, 0, 140), 百分比: (0.181, 0.000, 0.000, 0.137), DPI: 120x120
  解析image_info - 图片ID: 1940716163452190722, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716163452190722, 裁剪区域: (237, 255, 203, 258), 百分比: (0.309, 0.249, 0.264, 0.252), DPI: 120x120
  解析image_info - 图片ID: 1940716171899518978, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716171899518978, 裁剪区域: (0, 109, 180, 161), 百分比: (0.000, 0.106, 0.234, 0.157), DPI: 120x120
  解析image_info - 图片ID: 1940716180531396610, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716180531396610, 裁剪区域: (306, 54, 223, 82), 百分比: (0.398, 0.053, 0.290, 0.080), DPI: 120x120
  解析image_info - 图片ID: 1940716212726874114, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716212726874114, 裁剪区域: (163, 11, 0, 85), 百分比: (0.212, 0.011, 0.000, 0.083), DPI: 120x120
  解析image_info - 图片ID: 1940716204195659777, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716204195659777, 裁剪区域: (83, 0, 0, 132), 百分比: (0.108, 0.000, 0.000, 0.129), DPI: 120x120
  解析image_info - 图片ID: 1940716386140372994, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716386140372994, 裁剪区域: (182, 194, 81, 10), 百分比: (0.237, 0.189, 0.105, 0.010), DPI: 120x120
  解析image_info - 图片ID: 1940716397460799490, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716397460799490, 裁剪区域: (178, 100, 224, 51), 百分比: (0.232, 0.098, 0.292, 0.050), DPI: 120x120
  解析image_info - 图片ID: 1940716406784737282, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716406784737282, 裁剪区域: (251, 98, 0, 103), 百分比: (0.327, 0.096, 0.000, 0.101), DPI: 120x120
  解析image_info - 图片ID: 1940716436128088065, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716436128088065, 裁剪区域: (96, 0, 0, 138), 百分比: (0.125, 0.000, 0.000, 0.135), DPI: 120x120
  解析image_info - 图片ID: 1940716427282300930, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1940716427282300930, 裁剪区域: (125, 0, 0, 143), 百分比: (0.163, 0.000, 0.000, 0.140), DPI: 120x120
  解析image_info - 图片ID: 1945407610642325505, 尺寸: 1024x768
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1945407610642325505, 裁剪区域: (0, 0, 0, 0), 百分比: (0.000, 0.000, 0.000, 0.000), DPI: 120x120
  解析image_info - 图片ID: 1945407620649934850, 尺寸: 1024x768
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1945407620649934850, 裁剪区域: (0, 0, 0, 0), 百分比: (0.000, 0.000, 0.000, 0.000), DPI: 120x120
  解析image_info - 图片ID: 1945407630208753665, 尺寸: 1024x768
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1945407630208753665, 裁剪区域: (391, 315, 352, 238), 百分比: (0.382, 0.410, 0.344, 0.310), DPI: 120x120
  解析image_info - 图片ID: 1945407654158229505, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1945407654158229505, 裁剪区域: (82, 8, 0, 83), 百分比: (0.107, 0.008, 0.000, 0.081), DPI: 120x120
  解析image_info - 图片ID: 1945407641915056130, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1945407641915056130, 裁剪区域: (117, 0, 0, 141), 百分比: (0.152, 0.000, 0.000, 0.138), DPI: 120x120
  解析image_info - 图片ID: 1945407695237242882, 尺寸: 1024x768
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1945407695237242882, 裁剪区域: (0, 0, 0, 0), 百分比: (0.000, 0.000, 0.000, 0.000), DPI: 120x120
  解析image_info - 图片ID: 1945407704611512321, 尺寸: 1024x768
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1945407704611512321, 裁剪区域: (260, 35, 271, 206), 百分比: (0.254, 0.046, 0.265, 0.268), DPI: 120x120
  解析image_info - 图片ID: 1945407734936330242, 尺寸: 1024x768
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1945407734936330242, 裁剪区域: (329, 58, 278, 206), 百分比: (0.321, 0.076, 0.271, 0.268), DPI: 120x120
  解析image_info - 图片ID: 1945407769153462273, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1945407769153462273, 裁剪区域: (178, 11, 0, 83), 百分比: (0.232, 0.011, 0.000, 0.081), DPI: 120x120
  解析image_info - 图片ID: 1945407758789337089, 尺寸: 768x1024
  解析到DPI信息: 120x120
  解析检测结果 - ImageId: 1945407758789337089, 裁剪区域: (197, 6, 0, 80), 百分比: (0.257, 0.006, 0.000, 0.078), DPI: 120x120
  批量检测完成，成功: True, 结果数量: 30
  2025-07-29 10:07:13 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
  2025-07-29 10:07:13 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:13 [INFO] 验证通过，保持API原始坐标: Left=354, Top=356, Right=496, Bottom=238
  2025-07-29 10:07:13 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:13 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:13 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:13 [INFO] 验证通过，保持API原始坐标: Left=305, Top=94, Right=222, Bottom=111
  2025-07-29 10:07:13 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:13 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:13 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:13 [INFO] 验证通过，保持API原始坐标: Left=321, Top=127, Right=180, Bottom=90
  2025-07-29 10:07:13 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:13 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:13 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:13 [INFO] 验证通过，保持API原始坐标: Left=41, Top=0, Right=0, Bottom=139
  2025-07-29 10:07:14 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:14 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:14 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:14 [INFO] 验证通过，保持API原始坐标: Left=101, Top=0, Right=0, Bottom=139
  2025-07-29 10:07:14 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:14 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:14 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:14 [INFO] 验证通过，保持API原始坐标: Left=155, Top=7, Right=0, Bottom=180
  2025-07-29 10:07:14 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:14 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:14 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:14 [INFO] 验证通过，保持API原始坐标: Left=241, Top=123, Right=216, Bottom=36
  2025-07-29 10:07:14 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:14 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:14 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:14 [INFO] 验证通过，保持API原始坐标: Left=238, Top=81, Right=147, Bottom=86
  2025-07-29 10:07:14 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:14 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:14 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:14 [INFO] 验证通过，保持API原始坐标: Left=127, Top=0, Right=0, Bottom=143
  2025-07-29 10:07:14 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:14 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:14 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:14 [INFO] 验证通过，保持API原始坐标: Left=139, Top=0, Right=0, Bottom=140
  2025-07-29 10:07:14 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:14 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:14 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:14 [INFO] 验证通过，保持API原始坐标: Left=237, Top=255, Right=203, Bottom=258
  2025-07-29 10:07:14 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:14 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:14 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:14 [INFO] 验证通过，保持API原始坐标: Left=0, Top=109, Right=180, Bottom=161
  2025-07-29 10:07:14 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:14 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:14 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:14 [INFO] 验证通过，保持API原始坐标: Left=306, Top=54, Right=223, Bottom=82
  2025-07-29 10:07:14 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:14 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:14 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:14 [INFO] 验证通过，保持API原始坐标: Left=163, Top=11, Right=0, Bottom=85
  2025-07-29 10:07:14 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:14 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:14 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:14 [INFO] 验证通过，保持API原始坐标: Left=83, Top=0, Right=0, Bottom=132
  2025-07-29 10:07:14 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:14 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:14 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:14 [INFO] 验证通过，保持API原始坐标: Left=182, Top=194, Right=81, Bottom=10
  2025-07-29 10:07:14 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:14 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:14 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:14 [INFO] 验证通过，保持API原始坐标: Left=178, Top=100, Right=224, Bottom=51
  2025-07-29 10:07:14 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:14 [INFO] 📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  2025-07-29 10:07:14 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:14 [INFO] 验证通过，保持API原始坐标: Left=251, Top=98, Right=0, Bottom=103
  2025-07-29 10:07:14 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:14 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:14 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:14 [INFO] 验证通过，保持API原始坐标: Left=96, Top=0, Right=0, Bottom=138
  2025-07-29 10:07:14 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:14 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:14 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:14 [INFO] 验证通过，保持API原始坐标: Left=125, Top=0, Right=0, Bottom=143
  2025-07-29 10:07:14 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
  2025-07-29 10:07:14 [INFO] 📐 图片尺寸: 5712x4284 (无EXIF旋转)
  2025-07-29 10:07:15 [INFO] 计算百分比坐标: Left=0.000, Top=0.000, Right=0.000, Bottom=0.000
  2025-07-29 10:07:15 [INFO] 验证通过，保持API原始坐标: Left=0, Top=0, Right=0, Bottom=0
  2025-07-29 10:07:15 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
  2025-07-29 10:07:15 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
  2025-07-29 10:07:15 [INFO] 计算百分比坐标: Left=0.000, Top=0.000, Right=0.000, Bottom=0.000
  2025-07-29 10:07:15 [INFO] 验证通过，保持API原始坐标: Left=0, Top=0, Right=0, Bottom=0
  2025-07-29 10:07:15 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
  2025-07-29 10:07:15 [INFO] 📐 图片尺寸: 5712x4284 (无EXIF旋转)
  2025-07-29 10:07:15 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:15 [INFO] 验证通过，保持API原始坐标: Left=391, Top=315, Right=352, Bottom=238
  2025-07-29 10:07:15 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:15 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:15 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:15 [INFO] 验证通过，保持API原始坐标: Left=82, Top=8, Right=0, Bottom=83
  2025-07-29 10:07:15 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:15 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:15 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:15 [INFO] 验证通过，保持API原始坐标: Left=117, Top=0, Right=0, Bottom=141
  2025-07-29 10:07:15 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
  2025-07-29 10:07:15 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
  2025-07-29 10:07:15 [INFO] 计算百分比坐标: Left=0.000, Top=0.000, Right=0.000, Bottom=0.000
  2025-07-29 10:07:15 [INFO] 验证通过，保持API原始坐标: Left=0, Top=0, Right=0, Bottom=0
  2025-07-29 10:07:15 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
  2025-07-29 10:07:15 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
  2025-07-29 10:07:15 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:15 [INFO] 验证通过，保持API原始坐标: Left=260, Top=35, Right=271, Bottom=206
  2025-07-29 10:07:15 [INFO] 📐 EXIF Orientation: 1, 需要旋转: False
  2025-07-29 10:07:15 [INFO] 📐 图片尺寸: 4032x3024 (无EXIF旋转)
  2025-07-29 10:07:15 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:15 [INFO] 验证通过，保持API原始坐标: Left=329, Top=58, Right=278, Bottom=206
  2025-07-29 10:07:15 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:15 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:15 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:15 [INFO] 验证通过，保持API原始坐标: Left=178, Top=11, Right=0, Bottom=83
  2025-07-29 10:07:15 [INFO] 📐 EXIF Orientation: 6, 需要旋转: True
  2025-07-29 10:07:15 [INFO] 📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  2025-07-29 10:07:15 [INFO] 使用API返回的百分比坐标
  2025-07-29 10:07:15 [INFO] 验证通过，保持API原始坐标: Left=197, Top=6, Right=0, Bottom=80
  🔄 开始串行插入 30 张图片到PPT
  🔄 开始处理图片: 1947918863608123393 (批量模式) - 电芯1-202507230001-0001-vent面-before
  📐 图片尺寸: 4032x3024 (无EXIF旋转)
  原始图片尺寸: 4032x3024, 宽高比: 1.33
  📐 图片尺寸: 4032x3024 (无EXIF旋转)
  原始图片尺寸: 4032x3024, 宽高比: 1.33
  优先高度策略: 高度57.9，对应宽度77.2
  计算后尺寸: 77.2x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 77.2x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1947918863608123393
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1947918863608123393_638893804357008836.jpg (1086639 bytes)
  ✅ 内存流图片插入成功: 1947918863608123393
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1947918863608123393_638893804357008836.jpg
  ✅ 检测成功: Left=354, Top=356, Right=496, Bottom=238, 置信度=0.9106836
  📐 使用API返回的图片尺寸: 1024x768
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 1045.41, Top: 1051.31, Right: 1464.75, Bottom: 702.84
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 1045.41, Top: 1051.31, Right: 1464.75, Bottom: 702.84
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 1045.41, Top: 1051.31, Right: 1464.75, Bottom: 702.84
  “POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Dynamic\v4.0_4.0.0.0__b03f5f7f11d50a3a\System.Dynamic.dll”。包含/排除设置已禁用符号加载。
  “POWERPNT.EXE”(CLR v4.0.30319: PBIppt.vsto|vstolocal): 已加载“System.Dynamic.DynamicAssembly”。
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 513.85x513.85点, 宽高比: 1.000
  当前位置: Left=1206.44, Top=1269.41
  调整后尺寸: 59.54x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Left位置，避免超出右边界
  调整Top位置，避免超出下边界
  调整后位置: Left=890.46, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸59.54x59.54点, 位置(890.46,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1947918863608123393 (电芯1-202507230001-0001-vent面-before)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  ✅ 无EXIF旋转，直接使用API判断
  🎯 基于电池区域宽高比判断旋转 (电芯1-202507230001-0001-vent面-before):
  📊 API图片尺寸: 1024x768
  📊 电池区域像素尺寸: width=174.0, height=174.0 (API检测: 横向)
  📊 电池区域宽高比: 1.00 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: False (基于电池区域宽高比 1.00)
  🔄 EXIF需要旋转: False
  ✅ 最终旋转判断: False (考虑EXIF旋转影响)
  ⚠️ PPT图片无需旋转: 1947918863608123393 (电芯1-202507230001-0001-vent面-before)
  调整前图片尺寸: 59.5x59.5, 旋转: 0.0°
  实际显示尺寸: 59.5x59.5, 宽高比: 1.00
  单元格尺寸: 99.3x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 59.5x59.5
  📊 实际显示尺寸: 59.5x59.5 (旋转0/180度)
  📊 单元格尺寸: 99.3x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 59.5x59.5 -> 59.5x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 59.5x59.5 -> 59.5x59.5
  📐 实际显示效果: 59.5x59.5 -> 59.5x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽59.5 vs 高59.5)
  ✅ 批量模式图片处理完成: 1947918863608123393 (电芯1-202507230001-0001-vent面-before)
  单元格位置: (150.0, 215.6), 尺寸: 99.3x62.9
  图片位置: (169.9, 217.3), 尺寸: 59.5x59.5
  ✅ 插入完成 (1/30): 1947918863608123393 -> 行3列3
  🔄 开始处理图片: 1940716094942429185 (批量模式) - 电芯1-202507230001-0001-大面1-before
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716094942429185
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716094942429185_638893804365410590.jpg (2754378 bytes)
  ✅ 内存流图片插入成功: 1940716094942429185
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716094942429185_638893804365410590.jpg
  ✅ 检测成功: Left=305, Top=94, Right=222, Bottom=111, 置信度=0.7270834
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 285.94, Top: 49.57, Right: 208.12, Bottom: 58.53
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 285.94, Top: 49.57, Right: 208.12, Bottom: 58.53
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 285.94, Top: 49.57, Right: 208.12, Bottom: 58.53
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 225.94x431.89点, 宽高比: 0.523
  当前位置: Left=-64.82, Top=401.05
  调整后尺寸: 31.14x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Top位置，避免超出下边界
  调整后位置: Left=32.58, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸31.14x59.54点, 位置(32.58,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716094942429185 (电芯1-202507230001-0001-大面1-before)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯1-202507230001-0001-大面1-before):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=241.0, height=819.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.29 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.29)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716094942429185
  旋转前图片属性: Width=31.1, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=31.1x59.5
  🎯 实际显示尺寸: 59.5x31.1, 当前是否横向: True
  🎯 目标角度: 90° (已经是横向展示，保持不变)
  旋转角度: 90° → 90° (优化为横向展示)
  旋转后图片属性: Width=31.1, Height=59.5, Rotation=90
  ✅ PPT图片旋转为横向展示成功: 1940716094942429185 (电芯1-202507230001-0001-大面1-before)
  调整前图片尺寸: 31.1x59.5, 旋转: 90.0°
  实际显示尺寸: 59.5x31.1, 宽高比: 1.91
  单元格尺寸: 85.2x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 59.5x113.8
  📊 实际显示尺寸: 113.8x59.5 (旋转90/270度)
  📊 单元格尺寸: 85.2x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 31.1x59.5 -> 59.5x113.8 (放大1.91倍)
  ✅ 图片尺寸调整完成: 31.1x59.5 -> 59.5x113.8
  📐 实际显示效果: 59.5x31.1 -> 113.8x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽113.8 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716094942429185 (电芯1-202507230001-0001-大面1-before)
  单元格位置: (249.3, 215.6), 尺寸: 85.2x62.9
  图片位置: (262.2, 190.2), 尺寸: 59.5x113.8
  ✅ 插入完成 (2/30): 1940716094942429185 -> 行3列4
  🔄 开始处理图片: 1940716103809187841 (批量模式) - 电芯1-202507230001-0001-大面2-before
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716103809187841
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716103809187841_638893804373647305.jpg (2411058 bytes)
  ✅ 内存流图片插入成功: 1940716103809187841
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716103809187841_638893804373647305.jpg
  ✅ 检测成功: Left=321, Top=127, Right=180, Bottom=90, 置信度=0.8227368
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 300.94, Top: 66.97, Right: 168.75, Bottom: 47.46
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 300.94, Top: 66.97, Right: 168.75, Bottom: 47.46
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 300.94, Top: 66.97, Right: 168.75, Bottom: 47.46
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 250.31x425.57点, 宽高比: 0.588
  当前位置: Left=53.67, Top=431.40
  调整后尺寸: 35.02x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Top位置，避免超出下边界
  调整后位置: Left=161.32, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸35.02x59.54点, 位置(161.32,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716103809187841 (电芯1-202507230001-0001-大面2-before)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯1-202507230001-0001-大面2-before):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=267.0, height=807.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.33 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.33)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716103809187841
  旋转前图片属性: Width=35.0, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=35.0x59.5
  🎯 实际显示尺寸: 59.5x35.0, 当前是否横向: True
  🎯 目标角度: 90° (已经是横向展示，保持不变)
  旋转角度: 90° → 90° (优化为横向展示)
  旋转后图片属性: Width=35.0, Height=59.5, Rotation=90
  ✅ PPT图片旋转为横向展示成功: 1940716103809187841 (电芯1-202507230001-0001-大面2-before)
  调整前图片尺寸: 35.0x59.5, 旋转: 90.0°
  实际显示尺寸: 59.5x35.0, 宽高比: 1.70
  单元格尺寸: 204.6x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 59.5x101.2
  📊 实际显示尺寸: 101.2x59.5 (旋转90/270度)
  📊 单元格尺寸: 204.6x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 35.0x59.5 -> 59.5x101.2 (放大1.70倍)
  ✅ 图片尺寸调整完成: 35.0x59.5 -> 59.5x101.2
  📐 实际显示效果: 59.5x35.0 -> 101.2x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽101.2 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716103809187841 (电芯1-202507230001-0001-大面2-before)
  单元格位置: (334.6, 215.6), 尺寸: 204.6x62.9
  图片位置: (407.1, 196.5), 尺寸: 59.5x101.2
  ✅ 插入完成 (3/30): 1940716103809187841 -> 行3列5
  🔄 开始处理图片: 1940716128824016897 (批量模式) - 电芯1-202507230001-0001-工装装配整体-before
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716128824016897
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716128824016897_638893804381195395.jpg (6272730 bytes)
  ✅ 内存流图片插入成功: 1940716128824016897
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716128824016897_638893804381195395.jpg
  ✅ 检测成功: Left=41, Top=0, Right=0, Bottom=139, 置信度=0.8460705
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 38.44, Top: 0.00, Right: 0.00, Bottom: 73.30
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 38.44, Top: 0.00, Right: 0.00, Bottom: 73.30
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 38.44, Top: 0.00, Right: 0.00, Bottom: 73.30
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 681.56x466.70点, 宽高比: 1.460
  当前位置: Left=34.45, Top=363.96
  调整后尺寸: 86.94x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Top位置，避免超出下边界
  调整后位置: Left=331.76, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸86.94x59.54点, 位置(331.76,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716128824016897 (电芯1-202507230001-0001-工装装配整体-before)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯1-202507230001-0001-工装装配整体-before):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=727.0, height=885.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.82 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.82)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716128824016897
  旋转前图片属性: Width=86.9, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=86.9x59.5
  🎯 实际显示尺寸: 59.5x86.9, 当前是否横向: False
  🎯 目标角度: 180° (旋转90度实现横向展示)
  旋转角度: 90° → 180° (优化为横向展示)
  旋转后图片属性: Width=86.9, Height=59.5, Rotation=180
  ✅ PPT图片旋转为横向展示成功: 1940716128824016897 (电芯1-202507230001-0001-工装装配整体-before)
  调整前图片尺寸: 86.9x59.5, 旋转: 180.0°
  实际显示尺寸: 86.9x59.5, 宽高比: 1.46
  单元格尺寸: 95.4x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 86.9x59.5
  📊 实际显示尺寸: 86.9x59.5 (旋转0/180度)
  📊 单元格尺寸: 95.4x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 86.9x59.5 -> 86.9x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 86.9x59.5 -> 86.9x59.5
  📐 实际显示效果: 86.9x59.5 -> 86.9x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽86.9 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716128824016897 (电芯1-202507230001-0001-工装装配整体-before)
  单元格位置: (539.1, 215.6), 尺寸: 95.4x62.9
  图片位置: (543.4, 217.3), 尺寸: 86.9x59.5
  ✅ 插入完成 (4/30): 1940716128824016897 -> 行3列6
  🔄 开始处理图片: 1940716120393465857 (批量模式) - 电芯1-202507230001-0001-工装装配正面-before
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716120393465857
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716120393465857_638893804394547834.jpg (6367768 bytes)
  ✅ 内存流图片插入成功: 1940716120393465857
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716120393465857_638893804394547834.jpg
  ✅ 检测成功: Left=101, Top=0, Right=0, Bottom=139, 置信度=0.8960006
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 94.69, Top: 0.00, Right: 0.00, Bottom: 73.30
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 94.69, Top: 0.00, Right: 0.00, Bottom: 73.30
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 94.69, Top: 0.00, Right: 0.00, Bottom: 73.30
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 625.31x466.70点, 宽高比: 1.340
  当前位置: Left=153.49, Top=392.09
  调整后尺寸: 79.77x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Top位置，避免超出下边界
  调整后位置: Left=426.27, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸79.77x59.54点, 位置(426.27,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716120393465857 (电芯1-202507230001-0001-工装装配正面-before)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯1-202507230001-0001-工装装配正面-before):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=667.0, height=885.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.75 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.75)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716120393465857
  旋转前图片属性: Width=79.8, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=79.8x59.5
  🎯 实际显示尺寸: 59.5x79.8, 当前是否横向: False
  🎯 目标角度: 180° (旋转90度实现横向展示)
  旋转角度: 90° → 180° (优化为横向展示)
  旋转后图片属性: Width=79.8, Height=59.5, Rotation=180
  ✅ PPT图片旋转为横向展示成功: 1940716120393465857 (电芯1-202507230001-0001-工装装配正面-before)
  调整前图片尺寸: 79.8x59.5, 旋转: 180.0°
  实际显示尺寸: 79.8x59.5, 宽高比: 1.34
  单元格尺寸: 86.4x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 79.8x59.5
  📊 实际显示尺寸: 79.8x59.5 (旋转0/180度)
  📊 单元格尺寸: 86.4x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 79.8x59.5 -> 79.8x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 79.8x59.5 -> 79.8x59.5
  📐 实际显示效果: 79.8x59.5 -> 79.8x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽79.8 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716120393465857 (电芯1-202507230001-0001-工装装配正面-before)
  单元格位置: (634.6, 215.6), 尺寸: 86.4x62.9
  图片位置: (637.9, 217.3), 尺寸: 79.8x59.5
  ✅ 插入完成 (5/30): 1940716120393465857 -> 行3列7
  🔄 开始处理图片: 1940716292083105793 (批量模式) - 电芯1-202507230001-0001-vent面-after
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716292083105793
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716292083105793_638893804410205179.jpg (2537982 bytes)
  ✅ 内存流图片插入成功: 1940716292083105793
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716292083105793_638893804410205179.jpg
  ✅ 检测成功: Left=155, Top=7, Right=0, Bottom=180, 置信度=0.7423937
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 145.31, Top: 3.69, Right: 0.00, Bottom: 94.92
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 145.31, Top: 3.69, Right: 0.00, Bottom: 94.92
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 145.31, Top: 3.69, Right: 0.00, Bottom: 94.92
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 574.69x441.39点, 宽高比: 1.302
  当前位置: Left=276.33, Top=430.06
  调整后尺寸: 77.51x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Top位置，避免超出下边界
  调整后位置: Left=524.91, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸77.51x59.54点, 位置(524.91,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716292083105793 (电芯1-202507230001-0001-vent面-after)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯1-202507230001-0001-vent面-after):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=613.0, height=837.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.73 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.73)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716292083105793
  旋转前图片属性: Width=77.5, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=77.5x59.5
  🎯 实际显示尺寸: 59.5x77.5, 当前是否横向: False
  🎯 目标角度: 180° (旋转90度实现横向展示)
  旋转角度: 90° → 180° (优化为横向展示)
  旋转后图片属性: Width=77.5, Height=59.5, Rotation=180
  ✅ PPT图片旋转为横向展示成功: 1940716292083105793 (电芯1-202507230001-0001-vent面-after)
  调整前图片尺寸: 77.5x59.5, 旋转: 180.0°
  实际显示尺寸: 77.5x59.5, 宽高比: 1.30
  单元格尺寸: 90.7x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 77.5x59.5
  📊 实际显示尺寸: 77.5x59.5 (旋转0/180度)
  📊 单元格尺寸: 90.7x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 77.5x59.5 -> 77.5x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 77.5x59.5 -> 77.5x59.5
  📐 实际显示效果: 77.5x59.5 -> 77.5x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽77.5 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716292083105793 (电芯1-202507230001-0001-vent面-after)
  单元格位置: (721.0, 215.6), 尺寸: 90.7x62.9
  图片位置: (727.6, 217.3), 尺寸: 77.5x59.5
  ✅ 插入完成 (6/30): 1940716292083105793 -> 行3列8
  🔄 开始处理图片: 1940716311947329538 (批量模式) - 电芯1-202507230001-0001-大面1-after
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716311947329538
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716311947329538_638893804423484595.jpg (2507662 bytes)
  ✅ 内存流图片插入成功: 1940716311947329538
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716311947329538_638893804423484595.jpg
  ✅ 检测成功: Left=241, Top=123, Right=216, Bottom=36, 置信度=0.8601301
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 225.94, Top: 64.86, Right: 202.50, Bottom: 18.98
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 225.94, Top: 64.86, Right: 202.50, Bottom: 18.98
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 225.94, Top: 64.86, Right: 202.50, Bottom: 18.98
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 291.56x456.15点, 宽高比: 0.639
  当前位置: Left=485.02, Top=361.74
  调整后尺寸: 38.05x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Top位置，避免超出下边界
  调整后位置: Left=611.77, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸38.05x59.54点, 位置(611.77,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716311947329538 (电芯1-202507230001-0001-大面1-after)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯1-202507230001-0001-大面1-after):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=311.0, height=865.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.36 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.36)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716311947329538
  旋转前图片属性: Width=38.1, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=38.1x59.5
  🎯 实际显示尺寸: 59.5x38.1, 当前是否横向: True
  🎯 目标角度: 90° (已经是横向展示，保持不变)
  旋转角度: 90° → 90° (优化为横向展示)
  旋转后图片属性: Width=38.1, Height=59.5, Rotation=90
  ✅ PPT图片旋转为横向展示成功: 1940716311947329538 (电芯1-202507230001-0001-大面1-after)
  调整前图片尺寸: 38.1x59.5, 旋转: 90.0°
  实际显示尺寸: 59.5x38.1, 宽高比: 1.56
  单元格尺寸: 180.7x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 59.5x93.1
  📊 实际显示尺寸: 93.1x59.5 (旋转90/270度)
  📊 单元格尺寸: 180.7x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 38.1x59.5 -> 59.5x93.1 (放大1.56倍)
  ✅ 图片尺寸调整完成: 38.1x59.5 -> 59.5x93.1
  📐 实际显示效果: 59.5x38.1 -> 93.1x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽93.1 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716311947329538 (电芯1-202507230001-0001-大面1-after)
  单元格位置: (811.7, 215.6), 尺寸: 180.7x62.9
  图片位置: (872.2, 200.5), 尺寸: 59.5x93.1
  ✅ 插入完成 (7/30): 1940716311947329538 -> 行3列9
  🔄 开始处理图片: 1940716321992687617 (批量模式) - 电芯1-202507230001-0001-大面2-after
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716321992687617
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716321992687617_638893804438532376.jpg (3154743 bytes)
  ✅ 内存流图片插入成功: 1940716321992687617
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716321992687617_638893804438532376.jpg
  ✅ 检测成功: Left=238, Top=81, Right=147, Bottom=86, 置信度=0.8269151
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 223.13, Top: 42.72, Right: 137.81, Bottom: 45.35
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 223.13, Top: 42.72, Right: 137.81, Bottom: 45.35
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 223.13, Top: 42.72, Right: 137.81, Bottom: 45.35
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 359.06x451.93点, 宽高比: 0.795
  当前位置: Left=617.05, Top=394.78
  调整后尺寸: 47.30x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Top位置，避免超出下边界
  调整后位置: Left=772.93, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸47.30x59.54点, 位置(772.93,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716321992687617 (电芯1-202507230001-0001-大面2-after)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯1-202507230001-0001-大面2-after):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=383.0, height=857.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.45 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.45)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716321992687617
  旋转前图片属性: Width=47.3, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=47.3x59.5
  🎯 实际显示尺寸: 59.5x47.3, 当前是否横向: True
  🎯 目标角度: 90° (已经是横向展示，保持不变)
  旋转角度: 90° → 90° (优化为横向展示)
  旋转后图片属性: Width=47.3, Height=59.5, Rotation=90
  ✅ PPT图片旋转为横向展示成功: 1940716321992687617 (电芯1-202507230001-0001-大面2-after)
  调整前图片尺寸: 47.3x59.5, 旋转: 90.0°
  实际显示尺寸: 59.5x47.3, 宽高比: 1.26
  单元格尺寸: 102.4x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 59.5x74.9
  📊 实际显示尺寸: 74.9x59.5 (旋转90/270度)
  📊 单元格尺寸: 102.4x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 47.3x59.5 -> 59.5x74.9 (放大1.26倍)
  ✅ 图片尺寸调整完成: 47.3x59.5 -> 59.5x74.9
  📐 实际显示效果: 59.5x47.3 -> 74.9x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽74.9 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716321992687617 (电芯1-202507230001-0001-大面2-after)
  单元格位置: (992.3, 215.6), 尺寸: 102.4x62.9
  图片位置: (1013.8, 209.6), 尺寸: 59.5x74.9
  ✅ 插入完成 (8/30): 1940716321992687617 -> 行3列10
  🔄 开始处理图片: 1940716353542242305 (批量模式) - 电芯1-202507230001-0001-工装装配整体-after
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716353542242305
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716353542242305_638893804454868290.jpg (6162389 bytes)
  ✅ 内存流图片插入成功: 1940716353542242305
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716353542242305_638893804454868290.jpg
  ✅ 检测成功: Left=127, Top=0, Right=0, Bottom=143, 置信度=0.9084041
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 119.06, Top: 0.00, Right: 0.00, Bottom: 75.41
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 119.06, Top: 0.00, Right: 0.00, Bottom: 75.41
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 119.06, Top: 0.00, Right: 0.00, Bottom: 75.41
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 600.94x464.59点, 宽高比: 1.293
  当前位置: Left=629.17, Top=405.33
  调整后尺寸: 77.01x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Left位置，避免超出右边界
  调整Top位置，避免超出下边界
  调整后位置: Left=872.99, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸77.01x59.54点, 位置(872.99,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716353542242305 (电芯1-202507230001-0001-工装装配整体-after)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯1-202507230001-0001-工装装配整体-after):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=641.0, height=881.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.73 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.73)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716353542242305
  旋转前图片属性: Width=77.0, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=77.0x59.5
  🎯 实际显示尺寸: 59.5x77.0, 当前是否横向: False
  🎯 目标角度: 180° (旋转90度实现横向展示)
  旋转角度: 90° → 180° (优化为横向展示)
  旋转后图片属性: Width=77.0, Height=59.5, Rotation=180
  ✅ PPT图片旋转为横向展示成功: 1940716353542242305 (电芯1-202507230001-0001-工装装配整体-after)
  调整前图片尺寸: 77.0x59.5, 旋转: 180.0°
  实际显示尺寸: 77.0x59.5, 宽高比: 1.29
  单元格尺寸: 91.0x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 77.0x59.5
  📊 实际显示尺寸: 77.0x59.5 (旋转0/180度)
  📊 单元格尺寸: 91.0x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 77.0x59.5 -> 77.0x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 77.0x59.5 -> 77.0x59.5
  📐 实际显示效果: 77.0x59.5 -> 77.0x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽77.0 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716353542242305 (电芯1-202507230001-0001-工装装配整体-after)
  单元格位置: (1094.7, 215.6), 尺寸: 91.0x62.9
  图片位置: (1101.7, 217.3), 尺寸: 77.0x59.5
  ✅ 插入完成 (9/30): 1940716353542242305 -> 行3列11
  🔄 开始处理图片: 1940716344088281089 (批量模式) - 电芯1-202507230001-0001-工装装配正面-after
  线程 12852 已退出，返回值为 0 (0x0)。
  线程 32528 已退出，返回值为 0 (0x0)。
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716344088281089
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716344088281089_638893804485589170.jpg (6478536 bytes)
  线程 27524 已退出，返回值为 0 (0x0)。
  ✅ 内存流图片插入成功: 1940716344088281089
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716344088281089_638893804485589170.jpg
  ✅ 检测成功: Left=139, Top=0, Right=0, Bottom=140, 置信度=0.8579599
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 130.31, Top: 0.00, Right: 0.00, Bottom: 73.83
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 130.31, Top: 0.00, Right: 0.00, Bottom: 73.83
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 130.31, Top: 0.00, Right: 0.00, Bottom: 73.83
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 589.69x466.17点, 宽高比: 1.265
  当前位置: Left=724.94, Top=410.16
  调整后尺寸: 75.31x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Left位置，避免超出右边界
  调整Top位置，避免超出下边界
  调整后位置: Left=874.69, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸75.31x59.54点, 位置(874.69,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716344088281089 (电芯1-202507230001-0001-工装装配正面-after)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯1-202507230001-0001-工装装配正面-after):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=629.0, height=884.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.71 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.71)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716344088281089
  旋转前图片属性: Width=75.3, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=75.3x59.5
  🎯 实际显示尺寸: 59.5x75.3, 当前是否横向: False
  🎯 目标角度: 180° (旋转90度实现横向展示)
  旋转角度: 90° → 180° (优化为横向展示)
  旋转后图片属性: Width=75.3, Height=59.5, Rotation=180
  ✅ PPT图片旋转为横向展示成功: 1940716344088281089 (电芯1-202507230001-0001-工装装配正面-after)
  调整前图片尺寸: 75.3x59.5, 旋转: 180.0°
  实际显示尺寸: 75.3x59.5, 宽高比: 1.26
  单元格尺寸: 90.9x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 75.3x59.5
  📊 实际显示尺寸: 75.3x59.5 (旋转0/180度)
  📊 单元格尺寸: 90.9x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 75.3x59.5 -> 75.3x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 75.3x59.5 -> 75.3x59.5
  📐 实际显示效果: 75.3x59.5 -> 75.3x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽75.3 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716344088281089 (电芯1-202507230001-0001-工装装配正面-after)
  单元格位置: (1185.7, 215.6), 尺寸: 90.9x62.9
  图片位置: (1193.5, 217.3), 尺寸: 75.3x59.5
  ✅ 插入完成 (10/30): 1940716344088281089 -> 行3列12
  🔄 开始处理图片: 1940716163452190722 (批量模式) - 电芯2-202507230001-0002-vent面-before
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716163452190722
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716163452190722_638893804510660463.jpg (2858967 bytes)
  ✅ 内存流图片插入成功: 1940716163452190722
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716163452190722_638893804510660463.jpg
  ✅ 检测成功: Left=237, Top=255, Right=203, Bottom=258, 置信度=0.9025000
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 222.19, Top: 134.47, Right: 190.31, Bottom: 136.05
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 222.19, Top: 134.47, Right: 190.31, Bottom: 136.05
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 222.19, Top: 134.47, Right: 190.31, Bottom: 136.05
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 307.50x269.47点, 宽高比: 1.141
  当前位置: Left=-201.57, Top=522.23
  调整后尺寸: 67.94x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Left位置，避免超出左边界
  调整Top位置，避免超出下边界
  调整后位置: Left=10.00, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸67.94x59.54点, 位置(10.00,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716163452190722 (电芯2-202507230001-0002-vent面-before)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯2-202507230001-0002-vent面-before):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=328.0, height=511.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.64 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.64)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716163452190722
  旋转前图片属性: Width=67.9, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=67.9x59.5
  🎯 实际显示尺寸: 59.5x67.9, 当前是否横向: False
  🎯 目标角度: 180° (旋转90度实现横向展示)
  旋转角度: 90° → 180° (优化为横向展示)
  旋转后图片属性: Width=67.9, Height=59.5, Rotation=180
  ✅ PPT图片旋转为横向展示成功: 1940716163452190722 (电芯2-202507230001-0002-vent面-before)
  调整前图片尺寸: 67.9x59.5, 旋转: 180.0°
  实际显示尺寸: 67.9x59.5, 宽高比: 1.14
  单元格尺寸: 99.3x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 67.9x59.5
  📊 实际显示尺寸: 67.9x59.5 (旋转0/180度)
  📊 单元格尺寸: 99.3x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 67.9x59.5 -> 67.9x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 67.9x59.5 -> 67.9x59.5
  📐 实际显示效果: 67.9x59.5 -> 67.9x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽67.9 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716163452190722 (电芯2-202507230001-0002-vent面-before)
  单元格位置: (150.0, 278.5), 尺寸: 99.3x62.9
  图片位置: (165.7, 280.2), 尺寸: 67.9x59.5
  ✅ 插入完成 (11/30): 1940716163452190722 -> 行4列3
  🔄 开始处理图片: 1940716171899518978 (批量模式) - 电芯2-202507230001-0002-大面1-before
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716171899518978
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716171899518978_638893804523285921.jpg (2562759 bytes)
  线程 36836 已退出，返回值为 0 (0x0)。
  ✅ 内存流图片插入成功: 1940716171899518978
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716171899518978_638893804523285921.jpg
  ✅ 检测成功: Left=0, Top=109, Right=180, Bottom=161, 置信度=0.7272251
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 0.00, Top: 57.48, Right: 168.75, Bottom: 84.90
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 0.00, Top: 57.48, Right: 168.75, Bottom: 84.90
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 0.00, Top: 57.48, Right: 168.75, Bottom: 84.90
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 551.25x397.62点, 宽高比: 1.386
  当前位置: Left=-218.24, Top=357.85
  调整后尺寸: 82.54x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Top位置，避免超出下边界
  调整后位置: Left=16.11, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸82.54x59.54点, 位置(16.11,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716171899518978 (电芯2-202507230001-0002-大面1-before)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯2-202507230001-0002-大面1-before):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=588.0, height=754.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.78 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.78)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716171899518978
  旋转前图片属性: Width=82.5, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=82.5x59.5
  🎯 实际显示尺寸: 59.5x82.5, 当前是否横向: False
  🎯 目标角度: 180° (旋转90度实现横向展示)
  旋转角度: 90° → 180° (优化为横向展示)
  旋转后图片属性: Width=82.5, Height=59.5, Rotation=180
  ✅ PPT图片旋转为横向展示成功: 1940716171899518978 (电芯2-202507230001-0002-大面1-before)
  调整前图片尺寸: 82.5x59.5, 旋转: 180.0°
  实际显示尺寸: 82.5x59.5, 宽高比: 1.39
  单元格尺寸: 85.2x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 82.5x59.5
  📊 实际显示尺寸: 82.5x59.5 (旋转0/180度)
  📊 单元格尺寸: 85.2x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 82.5x59.5 -> 82.5x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 82.5x59.5 -> 82.5x59.5
  📐 实际显示效果: 82.5x59.5 -> 82.5x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽82.5 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716171899518978 (电芯2-202507230001-0002-大面1-before)
  单元格位置: (249.3, 278.5), 尺寸: 85.2x62.9
  图片位置: (250.7, 280.2), 尺寸: 82.5x59.5
  ✅ 插入完成 (12/30): 1940716171899518978 -> 行4列4
  🔄 开始处理图片: 1940716180531396610 (批量模式) - 电芯2-202507230001-0002-大面2-before
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716180531396610
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716180531396610_638893804536360469.jpg (2273619 bytes)
  ✅ 内存流图片插入成功: 1940716180531396610
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716180531396610_638893804536360469.jpg
  ✅ 检测成功: Left=306, Top=54, Right=223, Bottom=82, 置信度=0.7921811
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 286.88, Top: 28.48, Right: 209.06, Bottom: 43.24
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 286.88, Top: 28.48, Right: 209.06, Bottom: 43.24
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 286.88, Top: 28.48, Right: 209.06, Bottom: 43.24
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 224.06x468.28点, 宽高比: 0.478
  当前位置: Left=83.94, Top=445.80
  调整后尺寸: 28.49x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Top位置，避免超出下边界
  调整后位置: Left=181.72, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸28.49x59.54点, 位置(181.72,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716180531396610 (电芯2-202507230001-0002-大面2-before)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯2-202507230001-0002-大面2-before):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=239.0, height=888.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.27 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.27)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716180531396610
  旋转前图片属性: Width=28.5, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=28.5x59.5
  🎯 实际显示尺寸: 59.5x28.5, 当前是否横向: True
  🎯 目标角度: 90° (已经是横向展示，保持不变)
  旋转角度: 90° → 90° (优化为横向展示)
  旋转后图片属性: Width=28.5, Height=59.5, Rotation=90
  ✅ PPT图片旋转为横向展示成功: 1940716180531396610 (电芯2-202507230001-0002-大面2-before)
  调整前图片尺寸: 28.5x59.5, 旋转: 90.0°
  实际显示尺寸: 59.5x28.5, 宽高比: 2.09
  单元格尺寸: 204.6x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 59.5x124.4
  📊 实际显示尺寸: 124.4x59.5 (旋转90/270度)
  📊 单元格尺寸: 204.6x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 28.5x59.5 -> 59.5x124.4 (放大2.09倍)
  ✅ 图片尺寸调整完成: 28.5x59.5 -> 59.5x124.4
  📐 实际显示效果: 59.5x28.5 -> 124.4x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽124.4 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716180531396610 (电芯2-202507230001-0002-大面2-before)
  单元格位置: (334.6, 278.5), 尺寸: 204.6x62.9
  图片位置: (407.1, 247.8), 尺寸: 59.5x124.4
  ✅ 插入完成 (13/30): 1940716180531396610 -> 行4列5
  🔄 开始处理图片: 1940716212726874114 (批量模式) - 电芯2-202507230001-0002-工装装配整体-before
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716212726874114
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716212726874114_638893804550808361.jpg (6843868 bytes)
  ✅ 内存流图片插入成功: 1940716212726874114
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716212726874114_638893804550808361.jpg
  ✅ 检测成功: Left=163, Top=11, Right=0, Bottom=85, 置信度=0.8581046
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 152.81, Top: 5.80, Right: 0.00, Bottom: 44.82
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 152.81, Top: 5.80, Right: 0.00, Bottom: 44.82
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 152.81, Top: 5.80, Right: 0.00, Bottom: 44.82
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 567.19x489.38点, 宽高比: 1.159
  当前位置: Left=74.50, Top=472.75
  调整后尺寸: 69.00x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Top位置，避免超出下边界
  调整后位置: Left=323.59, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸69.00x59.54点, 位置(323.59,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716212726874114 (电芯2-202507230001-0002-工装装配整体-before)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯2-202507230001-0002-工装装配整体-before):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=605.0, height=928.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.65 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.65)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716212726874114
  旋转前图片属性: Width=69.0, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=69.0x59.5
  🎯 实际显示尺寸: 59.5x69.0, 当前是否横向: False
  🎯 目标角度: 180° (旋转90度实现横向展示)
  旋转角度: 90° → 180° (优化为横向展示)
  旋转后图片属性: Width=69.0, Height=59.5, Rotation=180
  ✅ PPT图片旋转为横向展示成功: 1940716212726874114 (电芯2-202507230001-0002-工装装配整体-before)
  调整前图片尺寸: 69.0x59.5, 旋转: 180.0°
  实际显示尺寸: 69.0x59.5, 宽高比: 1.16
  单元格尺寸: 95.4x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 69.0x59.5
  📊 实际显示尺寸: 69.0x59.5 (旋转0/180度)
  📊 单元格尺寸: 95.4x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 69.0x59.5 -> 69.0x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 69.0x59.5 -> 69.0x59.5
  📐 实际显示效果: 69.0x59.5 -> 69.0x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽69.0 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716212726874114 (电芯2-202507230001-0002-工装装配整体-before)
  单元格位置: (539.1, 278.5), 尺寸: 95.4x62.9
  图片位置: (552.4, 280.2), 尺寸: 69.0x59.5
  ✅ 插入完成 (14/30): 1940716212726874114 -> 行4列6
  🔄 开始处理图片: 1940716204195659777 (批量模式) - 电芯2-202507230001-0002-工装装配正面-before
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716204195659777
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716204195659777_638893804572243430.jpg (6776476 bytes)
  ✅ 内存流图片插入成功: 1940716204195659777
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716204195659777_638893804572243430.jpg
  ✅ 检测成功: Left=83, Top=0, Right=0, Bottom=132, 置信度=0.8996823
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 77.81, Top: 0.00, Right: 0.00, Bottom: 69.61
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 77.81, Top: 0.00, Right: 0.00, Bottom: 69.61
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 77.81, Top: 0.00, Right: 0.00, Bottom: 69.61
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 642.19x470.39点, 宽高比: 1.365
  当前位置: Left=143.21, Top=444.74
  调整后尺寸: 81.28x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Top位置，避免超出下边界
  调整后位置: Left=423.66, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸81.28x59.54点, 位置(423.66,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716204195659777 (电芯2-202507230001-0002-工装装配正面-before)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯2-202507230001-0002-工装装配正面-before):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=685.0, height=892.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.77 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.77)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716204195659777
  旋转前图片属性: Width=81.3, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=81.3x59.5
  🎯 实际显示尺寸: 59.5x81.3, 当前是否横向: False
  🎯 目标角度: 180° (旋转90度实现横向展示)
  旋转角度: 90° → 180° (优化为横向展示)
  旋转后图片属性: Width=81.3, Height=59.5, Rotation=180
  ✅ PPT图片旋转为横向展示成功: 1940716204195659777 (电芯2-202507230001-0002-工装装配正面-before)
  调整前图片尺寸: 81.3x59.5, 旋转: 180.0°
  实际显示尺寸: 81.3x59.5, 宽高比: 1.37
  单元格尺寸: 86.4x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 81.3x59.5
  📊 实际显示尺寸: 81.3x59.5 (旋转0/180度)
  📊 单元格尺寸: 86.4x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 81.3x59.5 -> 81.3x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 81.3x59.5 -> 81.3x59.5
  📐 实际显示效果: 81.3x59.5 -> 81.3x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽81.3 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716204195659777 (电芯2-202507230001-0002-工装装配正面-before)
  单元格位置: (634.6, 278.5), 尺寸: 86.4x62.9
  图片位置: (637.1, 280.2), 尺寸: 81.3x59.5
  ✅ 插入完成 (15/30): 1940716204195659777 -> 行4列7
  🔄 开始处理图片: 1940716386140372994 (批量模式) - 电芯2-202507230001-0002-vent面-after
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716386140372994
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716386140372994_638893804591146869.jpg (2656620 bytes)
  ✅ 内存流图片插入成功: 1940716386140372994
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716386140372994_638893804591146869.jpg
  ✅ 检测成功: Left=182, Top=194, Right=81, Bottom=10, 置信度=0.7819636
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 170.62, Top: 102.30, Right: 75.94, Bottom: 5.27
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 170.62, Top: 102.30, Right: 75.94, Bottom: 5.27
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 170.62, Top: 102.30, Right: 75.94, Bottom: 5.27
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 473.44x432.42点, 宽高比: 1.095
  当前位置: Left=232.82, Top=472.16
  调整后尺寸: 65.18x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Top位置，避免超出下边界
  调整后位置: Left=436.95, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸65.18x59.54点, 位置(436.95,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716386140372994 (电芯2-202507230001-0002-vent面-after)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯2-202507230001-0002-vent面-after):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=505.0, height=820.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.62 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.62)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716386140372994
  旋转前图片属性: Width=65.2, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=65.2x59.5
  🎯 实际显示尺寸: 59.5x65.2, 当前是否横向: False
  🎯 目标角度: 180° (旋转90度实现横向展示)
  旋转角度: 90° → 180° (优化为横向展示)
  旋转后图片属性: Width=65.2, Height=59.5, Rotation=180
  ✅ PPT图片旋转为横向展示成功: 1940716386140372994 (电芯2-202507230001-0002-vent面-after)
  调整前图片尺寸: 65.2x59.5, 旋转: 180.0°
  实际显示尺寸: 65.2x59.5, 宽高比: 1.09
  单元格尺寸: 90.7x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 65.2x59.5
  📊 实际显示尺寸: 65.2x59.5 (旋转0/180度)
  📊 单元格尺寸: 90.7x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 65.2x59.5 -> 65.2x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 65.2x59.5 -> 65.2x59.5
  📐 实际显示效果: 65.2x59.5 -> 65.2x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽65.2 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716386140372994 (电芯2-202507230001-0002-vent面-after)
  单元格位置: (721.0, 278.5), 尺寸: 90.7x62.9
  图片位置: (733.7, 280.2), 尺寸: 65.2x59.5
  ✅ 插入完成 (16/30): 1940716386140372994 -> 行4列8
  🔄 开始处理图片: 1940716397460799490 (批量模式) - 电芯2-202507230001-0002-大面1-after
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716397460799490
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716397460799490_638893804606436768.jpg (2602866 bytes)
  ✅ 内存流图片插入成功: 1940716397460799490
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716397460799490_638893804606436768.jpg
  ✅ 检测成功: Left=178, Top=100, Right=224, Bottom=51, 置信度=0.8249888
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 166.88, Top: 52.73, Right: 210.00, Bottom: 26.89
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 166.88, Top: 52.73, Right: 210.00, Bottom: 26.89
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 166.88, Top: 52.73, Right: 210.00, Bottom: 26.89
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 343.13x460.37点, 宽高比: 0.745
  当前位置: Left=469.26, Top=389.28
  调整后尺寸: 44.37x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Top位置，避免超出下边界
  调整后位置: Left=618.63, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸44.37x59.54点, 位置(618.63,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716397460799490 (电芯2-202507230001-0002-大面1-after)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯2-202507230001-0002-大面1-after):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=366.0, height=873.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.42 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.42)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716397460799490
  旋转前图片属性: Width=44.4, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=44.4x59.5
  🎯 实际显示尺寸: 59.5x44.4, 当前是否横向: True
  🎯 目标角度: 90° (已经是横向展示，保持不变)
  旋转角度: 90° → 90° (优化为横向展示)
  旋转后图片属性: Width=44.4, Height=59.5, Rotation=90
  ✅ PPT图片旋转为横向展示成功: 1940716397460799490 (电芯2-202507230001-0002-大面1-after)
  调整前图片尺寸: 44.4x59.5, 旋转: 90.0°
  实际显示尺寸: 59.5x44.4, 宽高比: 1.34
  单元格尺寸: 180.7x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 59.5x79.9
  📊 实际显示尺寸: 79.9x59.5 (旋转90/270度)
  📊 单元格尺寸: 180.7x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 44.4x59.5 -> 59.5x79.9 (放大1.34倍)
  ✅ 图片尺寸调整完成: 44.4x59.5 -> 59.5x79.9
  📐 实际显示效果: 59.5x44.4 -> 79.9x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽79.9 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716397460799490 (电芯2-202507230001-0002-大面1-after)
  单元格位置: (811.7, 278.5), 尺寸: 180.7x62.9
  图片位置: (872.2, 270.1), 尺寸: 59.5x79.9
  ✅ 插入完成 (17/30): 1940716397460799490 -> 行4列9
  🔄 开始处理图片: 1940716406784737282 (批量模式) - 电芯2-202507230001-0002-大面2-after
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始4032x3024 -> 实际显示3024x4032
  原始图片尺寸: 3024x4032, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716406784737282
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716406784737282_638893804621035756.jpg (2623239 bytes)
  ✅ 内存流图片插入成功: 1940716406784737282
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716406784737282_638893804621035756.jpg
  ✅ 检测成功: Left=251, Top=98, Right=0, Bottom=103, 置信度=0.8921602
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 235.31, Top: 51.68, Right: 0.00, Bottom: 54.32
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 235.31, Top: 51.68, Right: 0.00, Bottom: 54.32
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 235.31, Top: 51.68, Right: 0.00, Bottom: 54.32
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 484.69x434.00点, 宽高比: 1.117
  当前位置: Left=554.23, Top=541.69
  调整后尺寸: 66.49x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Top位置，避免超出下边界
  调整后位置: Left=763.33, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸66.49x59.54点, 位置(763.33,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716406784737282 (电芯2-202507230001-0002-大面2-after)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯2-202507230001-0002-大面2-after):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=517.0, height=823.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.63 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.63)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716406784737282
  旋转前图片属性: Width=66.5, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=66.5x59.5
  🎯 实际显示尺寸: 59.5x66.5, 当前是否横向: False
  🎯 目标角度: 180° (旋转90度实现横向展示)
  旋转角度: 90° → 180° (优化为横向展示)
  旋转后图片属性: Width=66.5, Height=59.5, Rotation=180
  ✅ PPT图片旋转为横向展示成功: 1940716406784737282 (电芯2-202507230001-0002-大面2-after)
  调整前图片尺寸: 66.5x59.5, 旋转: 180.0°
  实际显示尺寸: 66.5x59.5, 宽高比: 1.12
  单元格尺寸: 102.4x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 66.5x59.5
  📊 实际显示尺寸: 66.5x59.5 (旋转0/180度)
  📊 单元格尺寸: 102.4x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 66.5x59.5 -> 66.5x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 66.5x59.5 -> 66.5x59.5
  📐 实际显示效果: 66.5x59.5 -> 66.5x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽66.5 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716406784737282 (电芯2-202507230001-0002-大面2-after)
  单元格位置: (992.3, 278.5), 尺寸: 102.4x62.9
  图片位置: (1010.3, 280.2), 尺寸: 66.5x59.5
  ✅ 插入完成 (18/30): 1940716406784737282 -> 行4列10
  🔄 开始处理图片: 1940716436128088065 (批量模式) - 电芯2-202507230001-0002-工装装配整体-after
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716436128088065
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716436128088065_638893804636874576.jpg (7107485 bytes)
  ✅ 内存流图片插入成功: 1940716436128088065
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716436128088065_638893804636874576.jpg
  ✅ 检测成功: Left=96, Top=0, Right=0, Bottom=138, 置信度=0.9023277
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 90.00, Top: 0.00, Right: 0.00, Bottom: 72.77
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 90.00, Top: 0.00, Right: 0.00, Bottom: 72.77
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 90.00, Top: 0.00, Right: 0.00, Bottom: 72.77
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 630.00x467.23点, 宽高比: 1.348
  当前位置: Left=613.32, Top=452.42
  调整后尺寸: 80.28x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Left位置，避免超出右边界
  调整Top位置，避免超出下边界
  调整后位置: Left=869.72, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸80.28x59.54点, 位置(869.72,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716436128088065 (电芯2-202507230001-0002-工装装配整体-after)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯2-202507230001-0002-工装装配整体-after):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=672.0, height=886.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.76 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.76)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716436128088065
  旋转前图片属性: Width=80.3, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=80.3x59.5
  🎯 实际显示尺寸: 59.5x80.3, 当前是否横向: False
  🎯 目标角度: 180° (旋转90度实现横向展示)
  旋转角度: 90° → 180° (优化为横向展示)
  旋转后图片属性: Width=80.3, Height=59.5, Rotation=180
  ✅ PPT图片旋转为横向展示成功: 1940716436128088065 (电芯2-202507230001-0002-工装装配整体-after)
  调整前图片尺寸: 80.3x59.5, 旋转: 180.0°
  实际显示尺寸: 80.3x59.5, 宽高比: 1.35
  单元格尺寸: 91.0x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 80.3x59.5
  📊 实际显示尺寸: 80.3x59.5 (旋转0/180度)
  📊 单元格尺寸: 91.0x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 80.3x59.5 -> 80.3x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 80.3x59.5 -> 80.3x59.5
  📐 实际显示效果: 80.3x59.5 -> 80.3x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽80.3 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716436128088065 (电芯2-202507230001-0002-工装装配整体-after)
  单元格位置: (1094.7, 278.5), 尺寸: 91.0x62.9
  图片位置: (1100.1, 280.2), 尺寸: 80.3x59.5
  ✅ 插入完成 (19/30): 1940716436128088065 -> 行4列11
  🔄 开始处理图片: 1940716427282300930 (批量模式) - 电芯2-202507230001-0002-工装装配正面-after
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1940716427282300930
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716427282300930_638893804661332741.jpg (7150308 bytes)
  ✅ 内存流图片插入成功: 1940716427282300930
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1940716427282300930_638893804661332741.jpg
  ✅ 检测成功: Left=125, Top=0, Right=0, Bottom=143, 置信度=0.9010237
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 117.19, Top: 0.00, Right: 0.00, Bottom: 75.41
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 117.19, Top: 0.00, Right: 0.00, Bottom: 75.41
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 117.19, Top: 0.00, Right: 0.00, Bottom: 75.41
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 602.81x464.59点, 宽高比: 1.298
  当前位置: Left=719.17, Top=467.33
  调整后尺寸: 77.25x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Left位置，避免超出右边界
  调整Top位置，避免超出下边界
  调整后位置: Left=872.75, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸77.25x59.54点, 位置(872.75,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1940716427282300930 (电芯2-202507230001-0002-工装装配正面-after)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯2-202507230001-0002-工装装配正面-after):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=643.0, height=881.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.73 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.73)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1940716427282300930
  旋转前图片属性: Width=77.2, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=77.2x59.5
  🎯 实际显示尺寸: 59.5x77.2, 当前是否横向: False
  🎯 目标角度: 180° (旋转90度实现横向展示)
  旋转角度: 90° → 180° (优化为横向展示)
  旋转后图片属性: Width=77.2, Height=59.5, Rotation=180
  ✅ PPT图片旋转为横向展示成功: 1940716427282300930 (电芯2-202507230001-0002-工装装配正面-after)
  调整前图片尺寸: 77.2x59.5, 旋转: 180.0°
  实际显示尺寸: 77.2x59.5, 宽高比: 1.30
  单元格尺寸: 90.9x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 77.2x59.5
  📊 实际显示尺寸: 77.2x59.5 (旋转0/180度)
  📊 单元格尺寸: 90.9x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 77.2x59.5 -> 77.2x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 77.2x59.5 -> 77.2x59.5
  📐 实际显示效果: 77.2x59.5 -> 77.2x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽77.2 vs 高59.5)
  ✅ 批量模式图片处理完成: 1940716427282300930 (电芯2-202507230001-0002-工装装配正面-after)
  单元格位置: (1185.7, 278.5), 尺寸: 90.9x62.9
  图片位置: (1192.5, 280.2), 尺寸: 77.2x59.5
  ✅ 插入完成 (20/30): 1940716427282300930 -> 行4列12
  🔄 开始处理图片: 1945407610642325505 (批量模式) - 电芯3-202507230001-0003-vent面-before
  📐 EXIF Orientation: 1, 需要旋转: False
  📐 图片尺寸: 5712x4284 (无EXIF旋转)
  原始图片尺寸: 5712x4284, 宽高比: 1.33
  📐 EXIF Orientation: 1, 需要旋转: False
  📐 图片尺寸: 5712x4284 (无EXIF旋转)
  原始图片尺寸: 5712x4284, 宽高比: 1.33
  优先高度策略: 高度57.9，对应宽度77.2
  计算后尺寸: 77.2x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 77.2x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1945407610642325505
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407610642325505_638893804685377640.jpg (6062866 bytes)
  ✅ 内存流图片插入成功: 1945407610642325505
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407610642325505_638893804685377640.jpg
  ⚠️ 检测结果无有效裁剪区域: Left=0, Top=0, Right=0, Bottom=0
  ⚠️ 无有效检测结果，跳过PPT裁剪: 1945407610642325505
  🔍 开始基于API数据计算旋转: 1945407610642325505 (电芯3-202507230001-0003-vent面-before)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 1, 需要旋转: False
  ✅ 无EXIF旋转，直接使用API判断
  🎯 基于电池区域宽高比判断旋转 (电芯3-202507230001-0003-vent面-before):
  📊 API图片尺寸: 1024x768
  📊 电池区域像素尺寸: width=1024.0, height=768.0 (API检测: 横向)
  📊 电池区域宽高比: 1.33 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: False (基于电池区域宽高比 1.33)
  🔄 EXIF需要旋转: False
  ✅ 最终旋转判断: False (考虑EXIF旋转影响)
  ⚠️ PPT图片无需旋转: 1945407610642325505 (电芯3-202507230001-0003-vent面-before)
  调整前图片尺寸: 77.2x57.9, 旋转: 0.0°
  实际显示尺寸: 77.2x57.9, 宽高比: 1.33
  单元格尺寸: 99.3x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 79.4x59.5
  📊 实际显示尺寸: 79.4x59.5 (旋转0/180度)
  📊 单元格尺寸: 99.3x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 77.2x57.9 -> 79.4x59.5 (放大1.03倍)
  ✅ 图片尺寸调整完成: 77.2x57.9 -> 79.4x59.5
  📐 实际显示效果: 77.2x57.9 -> 79.4x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽79.4 vs 高59.5)
  ✅ 批量模式图片处理完成: 1945407610642325505 (电芯3-202507230001-0003-vent面-before)
  单元格位置: (150.0, 341.5), 尺寸: 99.3x62.9
  图片位置: (160.0, 343.2), 尺寸: 79.4x59.5
  ✅ 插入完成 (21/30): 1945407610642325505 -> 行5列3
  🔄 开始处理图片: 1945407620649934850 (批量模式) - 电芯3-202507230001-0003-大面1-before
  📐 EXIF Orientation: 1, 需要旋转: False
  📐 图片尺寸: 4032x3024 (无EXIF旋转)
  原始图片尺寸: 4032x3024, 宽高比: 1.33
  📐 EXIF Orientation: 1, 需要旋转: False
  📐 图片尺寸: 4032x3024 (无EXIF旋转)
  原始图片尺寸: 4032x3024, 宽高比: 1.33
  优先高度策略: 高度57.9，对应宽度77.2
  计算后尺寸: 77.2x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 77.2x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1945407620649934850
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407620649934850_638893804699373501.jpg (3292502 bytes)
  ✅ 内存流图片插入成功: 1945407620649934850
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407620649934850_638893804699373501.jpg
  ⚠️ 检测结果无有效裁剪区域: Left=0, Top=0, Right=0, Bottom=0
  ⚠️ 无有效检测结果，跳过PPT裁剪: 1945407620649934850
  🔍 开始基于API数据计算旋转: 1945407620649934850 (电芯3-202507230001-0003-大面1-before)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 1, 需要旋转: False
  ✅ 无EXIF旋转，直接使用API判断
  🎯 基于电池区域宽高比判断旋转 (电芯3-202507230001-0003-大面1-before):
  📊 API图片尺寸: 1024x768
  📊 电池区域像素尺寸: width=1024.0, height=768.0 (API检测: 横向)
  📊 电池区域宽高比: 1.33 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: False (基于电池区域宽高比 1.33)
  🔄 EXIF需要旋转: False
  ✅ 最终旋转判断: False (考虑EXIF旋转影响)
  ⚠️ PPT图片无需旋转: 1945407620649934850 (电芯3-202507230001-0003-大面1-before)
  调整前图片尺寸: 77.2x57.9, 旋转: 0.0°
  实际显示尺寸: 77.2x57.9, 宽高比: 1.33
  单元格尺寸: 85.2x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 79.4x59.5
  📊 实际显示尺寸: 79.4x59.5 (旋转0/180度)
  📊 单元格尺寸: 85.2x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 77.2x57.9 -> 79.4x59.5 (放大1.03倍)
  ✅ 图片尺寸调整完成: 77.2x57.9 -> 79.4x59.5
  📐 实际显示效果: 77.2x57.9 -> 79.4x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽79.4 vs 高59.5)
  ✅ 批量模式图片处理完成: 1945407620649934850 (电芯3-202507230001-0003-大面1-before)
  单元格位置: (249.3, 341.5), 尺寸: 85.2x62.9
  图片位置: (252.3, 343.2), 尺寸: 79.4x59.5
  ✅ 插入完成 (22/30): 1945407620649934850 -> 行5列4
  🔄 开始处理图片: 1945407630208753665 (批量模式) - 电芯3-202507230001-0003-大面2-before
  📐 EXIF Orientation: 1, 需要旋转: False
  📐 图片尺寸: 5712x4284 (无EXIF旋转)
  原始图片尺寸: 5712x4284, 宽高比: 1.33
  📐 EXIF Orientation: 1, 需要旋转: False
  📐 图片尺寸: 5712x4284 (无EXIF旋转)
  原始图片尺寸: 5712x4284, 宽高比: 1.33
  优先高度策略: 高度57.9，对应宽度77.2
  计算后尺寸: 77.2x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 77.2x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1945407630208753665
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407630208753665_638893804707849708.jpg (6551448 bytes)
  ✅ 内存流图片插入成功: 1945407630208753665
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407630208753665_638893804707849708.jpg
  ✅ 检测成功: Left=391, Top=315, Right=352, Bottom=238, 置信度=0.6180333
  📐 使用API返回的图片尺寸: 1024x768
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 274.92, Top: 221.48, Right: 247.50, Bottom: 167.34
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 274.92, Top: 221.48, Right: 247.50, Bottom: 167.34
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 274.92, Top: 221.48, Right: 247.50, Bottom: 167.34
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 197.58x151.17点, 宽高比: 1.307
  当前位置: Left=673.15, Top=565.45
  调整后尺寸: 77.81x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Top位置，避免超出下边界
  调整后位置: Left=733.04, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸77.81x59.54点, 位置(733.04,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1945407630208753665 (电芯3-202507230001-0003-大面2-before)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 1, 需要旋转: False
  ✅ 无EXIF旋转，直接使用API判断
  🎯 基于电池区域宽高比判断旋转 (电芯3-202507230001-0003-大面2-before):
  📊 API图片尺寸: 1024x768
  📊 电池区域像素尺寸: width=281.0, height=215.0 (API检测: 横向)
  📊 电池区域宽高比: 1.31 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: False (基于电池区域宽高比 1.31)
  🔄 EXIF需要旋转: False
  ✅ 最终旋转判断: False (考虑EXIF旋转影响)
  ⚠️ PPT图片无需旋转: 1945407630208753665 (电芯3-202507230001-0003-大面2-before)
  调整前图片尺寸: 77.8x59.5, 旋转: 0.0°
  实际显示尺寸: 77.8x59.5, 宽高比: 1.31
  单元格尺寸: 204.6x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 77.8x59.5
  📊 实际显示尺寸: 77.8x59.5 (旋转0/180度)
  📊 单元格尺寸: 204.6x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 77.8x59.5 -> 77.8x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 77.8x59.5 -> 77.8x59.5
  📐 实际显示效果: 77.8x59.5 -> 77.8x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽77.8 vs 高59.5)
  ✅ 批量模式图片处理完成: 1945407630208753665 (电芯3-202507230001-0003-大面2-before)
  单元格位置: (334.6, 341.5), 尺寸: 204.6x62.9
  图片位置: (398.0, 343.2), 尺寸: 77.8x59.5
  ✅ 插入完成 (23/30): 1945407630208753665 -> 行5列5
  🔄 开始处理图片: 1945407654158229505 (批量模式) - 电芯3-202507230001-0003-工装装配整体-before
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1945407654158229505
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407654158229505_638893804723573018.jpg (5415092 bytes)
  ✅ 内存流图片插入成功: 1945407654158229505
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407654158229505_638893804723573018.jpg
  ✅ 检测成功: Left=82, Top=8, Right=0, Bottom=83, 置信度=0.8725567
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 76.88, Top: 4.22, Right: 0.00, Bottom: 43.77
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 76.88, Top: 4.22, Right: 0.00, Bottom: 43.77
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 76.88, Top: 4.22, Right: 0.00, Bottom: 43.77
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 643.12x492.01点, 宽高比: 1.307
  当前位置: Left=36.80, Top=496.40
  调整后尺寸: 77.82x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Top位置，避免超出下边界
  调整后位置: Left=319.45, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸77.82x59.54点, 位置(319.45,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1945407654158229505 (电芯3-202507230001-0003-工装装配整体-before)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯3-202507230001-0003-工装装配整体-before):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=686.0, height=933.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.74 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.74)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1945407654158229505
  旋转前图片属性: Width=77.8, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=77.8x59.5
  🎯 实际显示尺寸: 59.5x77.8, 当前是否横向: False
  🎯 目标角度: 180° (旋转90度实现横向展示)
  旋转角度: 90° → 180° (优化为横向展示)
  旋转后图片属性: Width=77.8, Height=59.5, Rotation=180
  ✅ PPT图片旋转为横向展示成功: 1945407654158229505 (电芯3-202507230001-0003-工装装配整体-before)
  调整前图片尺寸: 77.8x59.5, 旋转: 180.0°
  实际显示尺寸: 77.8x59.5, 宽高比: 1.31
  单元格尺寸: 95.4x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 77.8x59.5
  📊 实际显示尺寸: 77.8x59.5 (旋转0/180度)
  📊 单元格尺寸: 95.4x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 77.8x59.5 -> 77.8x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 77.8x59.5 -> 77.8x59.5
  📐 实际显示效果: 77.8x59.5 -> 77.8x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽77.8 vs 高59.5)
  ✅ 批量模式图片处理完成: 1945407654158229505 (电芯3-202507230001-0003-工装装配整体-before)
  单元格位置: (539.1, 341.5), 尺寸: 95.4x62.9
  图片位置: (547.9, 343.2), 尺寸: 77.8x59.5
  ✅ 插入完成 (24/30): 1945407654158229505 -> 行5列6
  🔄 开始处理图片: 1945407641915056130 (批量模式) - 电芯3-202507230001-0003-工装装配正面-before
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1945407641915056130
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407641915056130_638893804744343398.jpg (5800689 bytes)
  ✅ 内存流图片插入成功: 1945407641915056130
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407641915056130_638893804744343398.jpg
  ✅ 检测成功: Left=117, Top=0, Right=0, Bottom=141, 置信度=0.8894519
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 109.69, Top: 0.00, Right: 0.00, Bottom: 74.36
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 109.69, Top: 0.00, Right: 0.00, Bottom: 74.36
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 109.69, Top: 0.00, Right: 0.00, Bottom: 74.36
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 610.31x465.64点, 宽高比: 1.311
  当前位置: Left=161.52, Top=525.99
  调整后尺寸: 78.03x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Top位置，避免超出下边界
  调整后位置: Left=427.66, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸78.03x59.54点, 位置(427.66,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1945407641915056130 (电芯3-202507230001-0003-工装装配正面-before)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯3-202507230001-0003-工装装配正面-before):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=651.0, height=883.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.74 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.74)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1945407641915056130
  旋转前图片属性: Width=78.0, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=78.0x59.5
  🎯 实际显示尺寸: 59.5x78.0, 当前是否横向: False
  🎯 目标角度: 180° (旋转90度实现横向展示)
  旋转角度: 90° → 180° (优化为横向展示)
  旋转后图片属性: Width=78.0, Height=59.5, Rotation=180
  ✅ PPT图片旋转为横向展示成功: 1945407641915056130 (电芯3-202507230001-0003-工装装配正面-before)
  调整前图片尺寸: 78.0x59.5, 旋转: 180.0°
  实际显示尺寸: 78.0x59.5, 宽高比: 1.31
  单元格尺寸: 86.4x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 78.0x59.5
  📊 实际显示尺寸: 78.0x59.5 (旋转0/180度)
  📊 单元格尺寸: 86.4x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 78.0x59.5 -> 78.0x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 78.0x59.5 -> 78.0x59.5
  📐 实际显示效果: 78.0x59.5 -> 78.0x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽78.0 vs 高59.5)
  ✅ 批量模式图片处理完成: 1945407641915056130 (电芯3-202507230001-0003-工装装配正面-before)
  单元格位置: (634.6, 341.5), 尺寸: 86.4x62.9
  图片位置: (638.8, 343.2), 尺寸: 78.0x59.5
  ✅ 插入完成 (25/30): 1945407641915056130 -> 行5列7
  🔄 开始处理图片: 1945407695237242882 (批量模式) - 电芯3-202507230001-0003-vent面-after
  📐 EXIF Orientation: 1, 需要旋转: False
  📐 图片尺寸: 4032x3024 (无EXIF旋转)
  原始图片尺寸: 4032x3024, 宽高比: 1.33
  📐 EXIF Orientation: 1, 需要旋转: False
  📐 图片尺寸: 4032x3024 (无EXIF旋转)
  原始图片尺寸: 4032x3024, 宽高比: 1.33
  优先高度策略: 高度57.9，对应宽度77.2
  计算后尺寸: 77.2x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 77.2x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1945407695237242882
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407695237242882_638893804767384433.jpg (3329415 bytes)
  ✅ 内存流图片插入成功: 1945407695237242882
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407695237242882_638893804767384433.jpg
  ⚠️ 检测结果无有效裁剪区域: Left=0, Top=0, Right=0, Bottom=0
  ⚠️ 无有效检测结果，跳过PPT裁剪: 1945407695237242882
  🔍 开始基于API数据计算旋转: 1945407695237242882 (电芯3-202507230001-0003-vent面-after)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 1, 需要旋转: False
  ✅ 无EXIF旋转，直接使用API判断
  🎯 基于电池区域宽高比判断旋转 (电芯3-202507230001-0003-vent面-after):
  📊 API图片尺寸: 1024x768
  📊 电池区域像素尺寸: width=1024.0, height=768.0 (API检测: 横向)
  📊 电池区域宽高比: 1.33 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: False (基于电池区域宽高比 1.33)
  🔄 EXIF需要旋转: False
  ✅ 最终旋转判断: False (考虑EXIF旋转影响)
  ⚠️ PPT图片无需旋转: 1945407695237242882 (电芯3-202507230001-0003-vent面-after)
  调整前图片尺寸: 77.2x57.9, 旋转: 0.0°
  实际显示尺寸: 77.2x57.9, 宽高比: 1.33
  单元格尺寸: 90.7x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 79.4x59.5
  📊 实际显示尺寸: 79.4x59.5 (旋转0/180度)
  📊 单元格尺寸: 90.7x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 77.2x57.9 -> 79.4x59.5 (放大1.03倍)
  ✅ 图片尺寸调整完成: 77.2x57.9 -> 79.4x59.5
  📐 实际显示效果: 77.2x57.9 -> 79.4x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽79.4 vs 高59.5)
  ✅ 批量模式图片处理完成: 1945407695237242882 (电芯3-202507230001-0003-vent面-after)
  单元格位置: (721.0, 341.5), 尺寸: 90.7x62.9
  图片位置: (726.6, 343.2), 尺寸: 79.4x59.5
  ✅ 插入完成 (26/30): 1945407695237242882 -> 行5列8
  🔄 开始处理图片: 1945407704611512321 (批量模式) - 电芯3-202507230001-0003-大面1-after
  📐 EXIF Orientation: 1, 需要旋转: False
  📐 图片尺寸: 4032x3024 (无EXIF旋转)
  原始图片尺寸: 4032x3024, 宽高比: 1.33
  📐 EXIF Orientation: 1, 需要旋转: False
  📐 图片尺寸: 4032x3024 (无EXIF旋转)
  原始图片尺寸: 4032x3024, 宽高比: 1.33
  优先高度策略: 高度57.9，对应宽度77.2
  计算后尺寸: 77.2x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 77.2x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1945407704611512321
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407704611512321_638893804780918604.jpg (2974562 bytes)
  ✅ 内存流图片插入成功: 1945407704611512321
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407704611512321_638893804780918604.jpg
  ✅ 检测成功: Left=260, Top=35, Right=271, Bottom=206, 置信度=0.8975179
  📐 使用API返回的图片尺寸: 1024x768
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 182.81, Top: 24.61, Right: 190.55, Bottom: 144.84
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 182.81, Top: 24.61, Right: 190.55, Bottom: 144.84
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 182.81, Top: 24.61, Right: 190.55, Bottom: 144.84
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 346.64x370.55点, 宽高比: 0.935
  当前位置: Left=1046.20, Top=368.58
  调整后尺寸: 55.69x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Left位置，避免超出右边界
  调整Top位置，避免超出下边界
  调整后位置: Left=894.31, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸55.69x59.54点, 位置(894.31,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1945407704611512321 (电芯3-202507230001-0003-大面1-after)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 1, 需要旋转: False
  ✅ 无EXIF旋转，直接使用API判断
  🎯 基于电池区域宽高比判断旋转 (电芯3-202507230001-0003-大面1-after):
  📊 API图片尺寸: 1024x768
  📊 电池区域像素尺寸: width=493.0, height=527.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.94 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.94)
  🔄 EXIF需要旋转: False
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1945407704611512321
  旋转前图片属性: Width=55.7, Height=59.5, Rotation=0
  🎯 横向旋转计算: 当前角度=0°, 当前尺寸=55.7x59.5
  🎯 实际显示尺寸: 55.7x59.5, 当前是否横向: False
  🎯 目标角度: 90° (旋转90度实现横向展示)
  旋转角度: 0° → 90° (优化为横向展示)
  旋转后图片属性: Width=55.7, Height=59.5, Rotation=90
  ✅ PPT图片旋转为横向展示成功: 1945407704611512321 (电芯3-202507230001-0003-大面1-after)
  调整前图片尺寸: 55.7x59.5, 旋转: 90.0°
  实际显示尺寸: 59.5x55.7, 宽高比: 1.07
  单元格尺寸: 180.7x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 59.5x63.6
  📊 实际显示尺寸: 63.6x59.5 (旋转90/270度)
  📊 单元格尺寸: 180.7x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 55.7x59.5 -> 59.5x63.6 (放大1.07倍)
  ✅ 图片尺寸调整完成: 55.7x59.5 -> 59.5x63.6
  📐 实际显示效果: 59.5x55.7 -> 63.6x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽63.6 vs 高59.5)
  ✅ 批量模式图片处理完成: 1945407704611512321 (电芯3-202507230001-0003-大面1-after)
  单元格位置: (811.7, 341.5), 尺寸: 180.7x62.9
  图片位置: (872.2, 341.1), 尺寸: 59.5x63.6
  ✅ 插入完成 (27/30): 1945407704611512321 -> 行5列9
  🔄 开始处理图片: 1945407734936330242 (批量模式) - 电芯3-202507230001-0003-大面2-after
  📐 EXIF Orientation: 1, 需要旋转: False
  📐 图片尺寸: 4032x3024 (无EXIF旋转)
  原始图片尺寸: 4032x3024, 宽高比: 1.33
  📐 EXIF Orientation: 1, 需要旋转: False
  📐 图片尺寸: 4032x3024 (无EXIF旋转)
  原始图片尺寸: 4032x3024, 宽高比: 1.33
  优先高度策略: 高度57.9，对应宽度77.2
  计算后尺寸: 77.2x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 77.2x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1945407734936330242
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407734936330242_638893804794687938.jpg (2988806 bytes)
  ✅ 内存流图片插入成功: 1945407734936330242
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407734936330242_638893804794687938.jpg
  ✅ 检测成功: Left=329, Top=58, Right=278, Bottom=206, 置信度=0.7023973
  📐 使用API返回的图片尺寸: 1024x768
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 231.33, Top: 40.78, Right: 195.47, Bottom: 144.84
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 231.33, Top: 40.78, Right: 195.47, Bottom: 144.84
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 231.33, Top: 40.78, Right: 195.47, Bottom: 144.84
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 293.20x354.38点, 宽高比: 0.827
  当前位置: Left=1236.24, Top=384.75
  调整后尺寸: 49.26x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Left位置，避免超出右边界
  调整Top位置，避免超出下边界
  调整后位置: Left=900.74, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸49.26x59.54点, 位置(900.74,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1945407734936330242 (电芯3-202507230001-0003-大面2-after)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 1, 需要旋转: False
  ✅ 无EXIF旋转，直接使用API判断
  🎯 基于电池区域宽高比判断旋转 (电芯3-202507230001-0003-大面2-after):
  📊 API图片尺寸: 1024x768
  📊 电池区域像素尺寸: width=417.0, height=504.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.83 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.83)
  🔄 EXIF需要旋转: False
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1945407734936330242
  旋转前图片属性: Width=49.3, Height=59.5, Rotation=0
  🎯 横向旋转计算: 当前角度=0°, 当前尺寸=49.3x59.5
  🎯 实际显示尺寸: 49.3x59.5, 当前是否横向: False
  🎯 目标角度: 90° (旋转90度实现横向展示)
  旋转角度: 0° → 90° (优化为横向展示)
  旋转后图片属性: Width=49.3, Height=59.5, Rotation=90
  ✅ PPT图片旋转为横向展示成功: 1945407734936330242 (电芯3-202507230001-0003-大面2-after)
  调整前图片尺寸: 49.3x59.5, 旋转: 90.0°
  实际显示尺寸: 59.5x49.3, 宽高比: 1.21
  单元格尺寸: 102.4x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 59.5x72.0
  📊 实际显示尺寸: 72.0x59.5 (旋转90/270度)
  📊 单元格尺寸: 102.4x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 49.3x59.5 -> 59.5x72.0 (放大1.21倍)
  ✅ 图片尺寸调整完成: 49.3x59.5 -> 59.5x72.0
  📐 实际显示效果: 59.5x49.3 -> 72.0x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽72.0 vs 高59.5)
  ✅ 批量模式图片处理完成: 1945407734936330242 (电芯3-202507230001-0003-大面2-after)
  单元格位置: (992.3, 341.5), 尺寸: 102.4x62.9
  图片位置: (1013.8, 337.0), 尺寸: 59.5x72.0
  ✅ 插入完成 (28/30): 1945407734936330242 -> 行5列10
  🔄 开始处理图片: 1945407769153462273 (批量模式) - 电芯3-202507230001-0003-工装装配整体-after
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1945407769153462273
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407769153462273_638893804808989541.jpg (6086975 bytes)
  ✅ 内存流图片插入成功: 1945407769153462273
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407769153462273_638893804808989541.jpg
  ✅ 检测成功: Left=178, Top=11, Right=0, Bottom=83, 置信度=0.8423284
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 166.88, Top: 5.80, Right: 0.00, Bottom: 43.77
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 166.88, Top: 5.80, Right: 0.00, Bottom: 43.77
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 166.88, Top: 5.80, Right: 0.00, Bottom: 43.77
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 553.12x490.43点, 宽高比: 1.128
  当前位置: Left=634.36, Top=542.19
  调整后尺寸: 67.15x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Top位置，避免超出下边界
  调整后位置: Left=877.35, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸67.15x59.54点, 位置(877.35,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1945407769153462273 (电芯3-202507230001-0003-工装装配整体-after)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯3-202507230001-0003-工装装配整体-after):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=590.0, height=930.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.63 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.63)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1945407769153462273
  旋转前图片属性: Width=67.1, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=67.1x59.5
  🎯 实际显示尺寸: 59.5x67.1, 当前是否横向: False
  🎯 目标角度: 180° (旋转90度实现横向展示)
  旋转角度: 90° → 180° (优化为横向展示)
  旋转后图片属性: Width=67.1, Height=59.5, Rotation=180
  ✅ PPT图片旋转为横向展示成功: 1945407769153462273 (电芯3-202507230001-0003-工装装配整体-after)
  调整前图片尺寸: 67.1x59.5, 旋转: 180.0°
  实际显示尺寸: 67.1x59.5, 宽高比: 1.13
  单元格尺寸: 91.0x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 67.1x59.5
  📊 实际显示尺寸: 67.1x59.5 (旋转0/180度)
  📊 单元格尺寸: 91.0x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 67.1x59.5 -> 67.1x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 67.1x59.5 -> 67.1x59.5
  📐 实际显示效果: 67.1x59.5 -> 67.1x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽67.1 vs 高59.5)
  ✅ 批量模式图片处理完成: 1945407769153462273 (电芯3-202507230001-0003-工装装配整体-after)
  单元格位置: (1094.7, 341.5), 尺寸: 91.0x62.9
  图片位置: (1106.6, 343.2), 尺寸: 67.1x59.5
  ✅ 插入完成 (29/30): 1945407769153462273 -> 行5列11
  🔄 开始处理图片: 1945407758789337089 (批量模式) - 电芯3-202507230001-0003-工装装配正面-after
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  📐 EXIF Orientation: 6, 需要旋转: True
  📐 EXIF旋转检测: 原始5712x4284 -> 实际显示4284x5712
  原始图片尺寸: 4284x5712, 宽高比: 0.75
  优先高度策略: 高度57.9，对应宽度43.5
  计算后尺寸: 43.5x57.9 (最大: 340282300000000000000000000000000000000.0x57.9)
  计算后尺寸: 43.5x57.9 (可用高度: 57.9)
  🔄 开始内存流图片插入: 1945407758789337089
  📁 创建临时图片文件: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407758789337089_638893804834522248.jpg (5884911 bytes)
  ✅ 内存流图片插入成功: 1945407758789337089
  🗑️ 临时图片文件已清理: C:\Users\<USER>\AppData\Local\Temp\ppt_img_1945407758789337089_638893804834522248.jpg
  ✅ 检测成功: Left=197, Top=6, Right=0, Bottom=80, 置信度=0.8676472
  📐 使用API返回的图片尺寸: 768x1024
  BatteryDetectionApiClient初始化，服务器地址: http://************:8011
  开始应用裁剪到PowerPoint形状
  === 测试PowerPoint坐标映射修复 ===
  原始坐标 - Left: 184.69, Top: 3.16, Right: 0.00, Bottom: 42.19
  === 尝试坐标映射修正 ===
  使用传入的裁剪坐标
  准备设置裁剪属性 - Left: 184.69, Top: 3.16, Right: 0.00, Bottom: 42.19
  设置CropLeft...
  设置CropTop...
  设置CropRight...
  设置CropBottom...
  已设置PPT裁剪属性 - Left: 184.69, Top: 3.16, Right: 0.00, Bottom: 42.19
  已使用GotoSlide方法刷新PowerPoint界面
  === 开始调整图片尺寸为2.1cm高度 ===
  目标高度: 2.1cm = 59.54点
  当前尺寸: 535.31x494.65点, 宽高比: 1.082
  当前位置: Left=734.72, Top=548.99
  调整后尺寸: 64.43x59.54点
  幻灯片尺寸: 960.00x540.00点
  调整Left位置，避免超出右边界
  调整Top位置，避免超出下边界
  调整后位置: Left=885.57, Top=470.47
  ✅ 图片尺寸和位置已调整: 尺寸64.43x59.54点, 位置(885.57,470.47)
  实际高度: 2.10cm
  选中图片展示裁剪结果
  已选中图片，展示裁剪结果
  已触发PowerPoint界面刷新
  图片已选中，用户可以看到裁剪结果。如需调整，可以手动点击图片进入裁剪模式
  🔍 开始基于API数据计算旋转: 1945407758789337089 (电芯3-202507230001-0003-工装装配正面-after)
  🎯 检测策略: 直接基于API电池区域宽高比判断
  📐 EXIF Orientation: 6, 需要旋转: True
  🔄 检测到EXIF旋转，但图片尺寸已经考虑EXIF影响
  ℹ️ API判断基于修正后的图片尺寸，无需额外处理
  🎯 基于电池区域宽高比判断旋转 (电芯3-202507230001-0003-工装装配正面-after):
  📊 API图片尺寸: 768x1024
  📊 电池区域像素尺寸: width=571.0, height=938.0 (API检测: 竖向)
  📊 电池区域宽高比: 0.61 (寽高比 < 1.0 时为竖向，需要旋转)
  🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度
  🎯 API判断结果: True (基于电池区域宽高比 0.61)
  🔄 EXIF需要旋转: True
  ✅ 最终旋转判断: True (考虑EXIF旋转影响)
  🔄 开始执行PPT图片旋转: 1945407758789337089
  旋转前图片属性: Width=64.4, Height=59.5, Rotation=90
  🎯 横向旋转计算: 当前角度=90°, 当前尺寸=64.4x59.5
  🎯 实际显示尺寸: 59.5x64.4, 当前是否横向: False
  🎯 目标角度: 180° (旋转90度实现横向展示)
  旋转角度: 90° → 180° (优化为横向展示)
  旋转后图片属性: Width=64.4, Height=59.5, Rotation=180
  ✅ PPT图片旋转为横向展示成功: 1945407758789337089 (电芯3-202507230001-0003-工装装配正面-after)
  调整前图片尺寸: 64.4x59.5, 旋转: 180.0°
  实际显示尺寸: 64.4x59.5, 宽高比: 1.08
  单元格尺寸: 90.9x62.9
  🎯 目标高度: 59.5点 = 2.10cm
  ✅ 完全信任列宽计算，图片铺满可用高度: 64.4x59.5
  📊 实际显示尺寸: 64.4x59.5 (旋转0/180度)
  📊 单元格尺寸: 90.9x62.9, 可用高度: 59.5
  📊 高度利用率: 100.0%
  ✅ 放大以达到精确2.1cm高度: 64.4x59.5 -> 64.4x59.5 (放大1.00倍)
  ✅ 图片尺寸调整完成: 64.4x59.5 -> 64.4x59.5
  📐 实际显示效果: 64.4x59.5 -> 64.4x59.5
  🎯 显示高度: 59.5点 = 2.10cm (目标2.1cm: True)
  ✅ 宽度>高度: True (宽64.4 vs 高59.5)
  ✅ 批量模式图片处理完成: 1945407758789337089 (电芯3-202507230001-0003-工装装配正面-after)
  单元格位置: (1185.7, 341.5), 尺寸: 90.9x62.9
  图片位置: (1198.9, 343.2), 尺寸: 64.4x59.5
  ✅ 插入完成 (30/30): 1945407758789337089 -> 行5列12
  🎯 批量插入完成
  数据行填充成功
  PPT表格生成完成
  🗑️ 缓存已清理: 尺寸缓存30项, 数据缓存30项, 检测缓存30项
  线程 30380 已退出，返回值为 0 (0x0)。
  线程 17392 已退出，返回值为 0 (0x0)。
