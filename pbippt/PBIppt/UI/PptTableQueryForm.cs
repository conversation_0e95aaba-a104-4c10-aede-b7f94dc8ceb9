using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using PBIppt.ApiClient;
using PBIppt.Models;
using PBIppt.Utils;
using PowerPoint = Microsoft.Office.Interop.PowerPoint;

namespace PBIppt.UI
{
    /// <summary>
    /// 测试项目数据模型
    /// </summary>
    public class TestItemModel
    {
        public string id { get; set; }
        public string name { get; set; }

        public override string ToString()
        {
            return name;
        }
    }

    /// <summary>
    /// PPT表格生成查询表单
    /// </summary>
    public class PptTableQueryForm : Form
    {
        private readonly IBatteryImageApiClient _imageApiClient;
        private readonly PowerPoint.Slide _slide;
        private readonly ProgressManager _progressManager;
        
        // UI控件
        private TextBox txtDelegateNumber;
        private Button btnValidateDelegate;
        private ComboBox cmbTestItems;
        private CheckedListBox clbPhotoPositions;
        private CheckedListBox clbSampleNumbers;
        private CheckBox chkBeforeTest;
        private CheckBox chkAfterTest;
        private Button btnGenerate;
        private Label lblStatus;
        private Button btnSelectAll;
        private Button btnSelectNone;
        private Button btnSelectAllSamples;
        private Button btnSelectNoneSamples;
        
        // 数据存储
        private string _selectedDelegateNumber;
        private string _selectedTestItemId;
        private List<string> _selectedPhotoPositions = new List<string>();
        private List<string> _selectedSampleNumbers = new List<string>();
        private List<string> _selectedTestStages = new List<string>();
        
        /// <summary>
        /// 创建PPT表格查询表单
        /// </summary>
        public PptTableQueryForm(IBatteryImageApiClient apiClient, PowerPoint.Slide slide, ProgressManager progressManager)
        {
            _imageApiClient = apiClient;
            _slide = slide;
            _progressManager = progressManager;
            
            InitializeComponent();
            InitializeFormData();
            
            // 加载后检查登录状态
            this.Load += (s, e) => CheckLoginStatus();
        }
        
        /// <summary>
        /// 初始化表单组件
        /// </summary>
        private void InitializeComponent()
        {
            this.Text = "PPT表格生成";
            this.Size = new Size(500, 750);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            
            // 创建控件
            CreateControls();
            LayoutControls();
            BindEvents();
        }
        
        /// <summary>
        /// 创建控件
        /// </summary>
        private void CreateControls()
        {
            // 委托单号输入
            var lblDelegate = new Label { Text = "委托单号:", Location = new Point(20, 20), Size = new Size(80, 23) };
            txtDelegateNumber = new TextBox { Location = new Point(110, 20), Size = new Size(200, 23) };
            btnValidateDelegate = new Button { Text = "验证", Location = new Point(320, 19), Size = new Size(60, 25) };
            
            // 测试项目选择
            var lblTestItem = new Label { Text = "测试项目:", Location = new Point(20, 60), Size = new Size(80, 23) };
            cmbTestItems = new ComboBox { 
                Location = new Point(110, 60), 
                Size = new Size(270, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Enabled = false
            };
            
            // 照片部位选择
            var lblPhotoPositions = new Label { Text = "照片部位:", Location = new Point(20, 100), Size = new Size(80, 23) };
            clbPhotoPositions = new CheckedListBox { 
                Location = new Point(110, 100), 
                Size = new Size(270, 120),
                CheckOnClick = true,
                Enabled = false
            };
            
            // 批量操作按钮
            btnSelectAll = new Button { Text = "全选", Location = new Point(110, 230), Size = new Size(60, 25), Enabled = false };
            btnSelectNone = new Button { Text = "清空", Location = new Point(180, 230), Size = new Size(60, 25), Enabled = false };

            // 样品编号选择
            var lblSampleNumbers = new Label { Text = "样品编号:", Location = new Point(20, 270), Size = new Size(80, 23) };
            clbSampleNumbers = new CheckedListBox {
                Location = new Point(110, 270),
                Size = new Size(270, 120),
                CheckOnClick = true,
                Enabled = false
            };

            // 样品编号批量操作按钮
            btnSelectAllSamples = new Button { Text = "全选", Location = new Point(110, 400), Size = new Size(60, 25), Enabled = false };
            btnSelectNoneSamples = new Button { Text = "清空", Location = new Point(180, 400), Size = new Size(60, 25), Enabled = false };

            // 测试阶段选择
            var lblTestStage = new Label { Text = "测试阶段:", Location = new Point(20, 440), Size = new Size(80, 23) };
            chkBeforeTest = new CheckBox { Text = "测试前", Location = new Point(110, 440), Size = new Size(80, 23), Checked = true };
            chkAfterTest = new CheckBox { Text = "测试后", Location = new Point(200, 440), Size = new Size(80, 23), Checked = true };
            
            // 状态标签
            lblStatus = new Label {
                Location = new Point(20, 480),
                Size = new Size(360, 23),
                ForeColor = Color.Blue,
                Text = "请输入委托单号并验证"
            };

            // 生成按钮
            btnGenerate = new Button {
                Text = "生成表格",
                Location = new Point(200, 520),
                Size = new Size(100, 35),
                Enabled = false,
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            
            // 添加控件到表单
            this.Controls.AddRange(new Control[] {
                lblDelegate, txtDelegateNumber, btnValidateDelegate,
                lblTestItem, cmbTestItems,
                lblPhotoPositions, clbPhotoPositions,
                btnSelectAll, btnSelectNone,
                lblSampleNumbers, clbSampleNumbers,
                btnSelectAllSamples, btnSelectNoneSamples,
                lblTestStage, chkBeforeTest, chkAfterTest,
                lblStatus, btnGenerate
            });
        }
        
        /// <summary>
        /// 布局控件
        /// </summary>
        private void LayoutControls()
        {
            // 控件已在CreateControls中设置位置
        }
        
        /// <summary>
        /// 绑定事件
        /// </summary>
        private void BindEvents()
        {
            btnValidateDelegate.Click += BtnValidateDelegate_Click;
            cmbTestItems.SelectedIndexChanged += CmbTestItems_SelectedIndexChanged;
            clbPhotoPositions.ItemCheck += ClbPhotoPositions_ItemCheck;
            btnSelectAll.Click += BtnSelectAll_Click;
            btnSelectNone.Click += BtnSelectNone_Click;
            clbSampleNumbers.ItemCheck += ClbSampleNumbers_ItemCheck;
            btnSelectAllSamples.Click += BtnSelectAllSamples_Click;
            btnSelectNoneSamples.Click += BtnSelectNoneSamples_Click;
            chkBeforeTest.CheckedChanged += TestStage_CheckedChanged;
            chkAfterTest.CheckedChanged += TestStage_CheckedChanged;
            btnGenerate.Click += BtnGenerate_Click;
            
            // 委托单号输入框回车事件
            txtDelegateNumber.KeyPress += (s, e) => {
                if (e.KeyChar == (char)Keys.Enter)
                {
                    BtnValidateDelegate_Click(s, e);
                }
            };
        }
        
        /// <summary>
        /// 初始化表单数据
        /// </summary>
        private void InitializeFormData()
        {
            // 初始化测试阶段选择
            _selectedTestStages.Add("before");
            _selectedTestStages.Add("after");
        }
        
        /// <summary>
        /// 检查登录状态
        /// </summary>
        private void CheckLoginStatus()
        {
            try
            {
                // 只使用AuthManager检查登录状态，不再额外验证token
                bool needLogin = !AuthManager.IsLoggedIn;

                if (needLogin)
                {
                    if (MessageBox.Show("您尚未登录或登录已过期，需要先登录才能使用表格生成功能。是否现在登录？",
                            "需要登录", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        var authService = new PBIppt.Services.AuthService();
                        var loginForm = new LoginForm(authService);
                        if (loginForm.ShowDialog() == DialogResult.OK)
                        {
                            // 登录成功后，确保BatteryImageApiClient的token同步
                            if (AuthManager.IsLoggedIn && !string.IsNullOrEmpty(AuthManager.CurrentAuth?.AccessToken))
                            {
                                _imageApiClient.SetAuthToken(AuthManager.CurrentAuth.AccessToken);
                            }

                            lblStatus.Text = "登录成功，请输入委托单号";
                            lblStatus.ForeColor = Color.Green;
                        }
                        else
                        {
                            this.Close();
                        }
                    }
                    else
                    {
                        this.Close();
                    }
                }
                else
                {
                    // 已登录状态，确保BatteryImageApiClient的token同步
                    if (!string.IsNullOrEmpty(AuthManager.CurrentAuth?.AccessToken))
                    {
                        _imageApiClient.SetAuthToken(AuthManager.CurrentAuth.AccessToken);
                    }

                    lblStatus.Text = "已登录，请输入委托单号";
                    lblStatus.ForeColor = Color.Green;
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"检查登录状态失败: {ex.Message}";
                lblStatus.ForeColor = Color.Red;
            }
        }
        
        /// <summary>
        /// 验证委托单号按钮点击事件
        /// </summary>
        private async void BtnValidateDelegate_Click(object sender, EventArgs e)
        {
            string delegateNumber = txtDelegateNumber.Text.Trim();
            if (string.IsNullOrEmpty(delegateNumber))
            {
                lblStatus.Text = "请输入委托单号";
                lblStatus.ForeColor = Color.Red;
                return;
            }
            
            try
            {
                btnValidateDelegate.Enabled = false;
                lblStatus.Text = "正在验证委托单号...";
                lblStatus.ForeColor = Color.Blue;
                
                // 调用新的PPT专用接口验证委托单号
                var url = $"{_imageApiClient.BaseUrl}/ppt/validateDelegateNumber";
                var param = new { delegateNumber = delegateNumber };
                
                var response = await _imageApiClient.SendWithTokenAuthAsync<bool>(url, System.Net.Http.HttpMethod.Post, param);
                
                if (response)
                {
                    _selectedDelegateNumber = delegateNumber;
                    lblStatus.Text = "委托单号验证成功，正在加载测试项目...";
                    lblStatus.ForeColor = Color.Green;
                    
                    // 加载测试项目
                    await LoadTestItems();
                }
                else
                {
                    lblStatus.Text = "委托单号不存在，请检查输入";
                    lblStatus.ForeColor = Color.Red;
                    ResetForm();
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"验证委托单号失败: {ex.Message}";
                lblStatus.ForeColor = Color.Red;
                ResetForm();
            }
            finally
            {
                btnValidateDelegate.Enabled = true;
            }
        }
        
        /// <summary>
        /// 重置表单状态
        /// </summary>
        private void ResetForm()
        {
            _selectedDelegateNumber = null;
            _selectedTestItemId = null;
            _selectedPhotoPositions.Clear();
            _selectedSampleNumbers.Clear();

            cmbTestItems.Items.Clear();
            cmbTestItems.Enabled = false;
            clbPhotoPositions.Items.Clear();
            clbPhotoPositions.Enabled = false;
            btnSelectAll.Enabled = false;
            btnSelectNone.Enabled = false;
            clbSampleNumbers.Items.Clear();
            clbSampleNumbers.Enabled = false;
            btnSelectAllSamples.Enabled = false;
            btnSelectNoneSamples.Enabled = false;

            UpdateGenerateButtonState();
        }

        /// <summary>
        /// 加载测试项目列表
        /// </summary>
        private async Task LoadTestItems()
        {
            try
            {
                var url = $"{_imageApiClient.BaseUrl}/ppt/getTestItemsByDelegate";
                var param = new { delegateNumber = _selectedDelegateNumber };

                var response = await _imageApiClient.SendWithTokenAuthAsync<List<Dictionary<string, object>>>(url, System.Net.Http.HttpMethod.Post, param);

                cmbTestItems.Items.Clear();
                cmbTestItems.DisplayMember = "name";
                cmbTestItems.ValueMember = "id";

                if (response != null && response.Count > 0)
                {
                    foreach (var item in response)
                    {
                        cmbTestItems.Items.Add(new TestItemModel
                        {
                            id = item["id"].ToString(),
                            name = item["name"].ToString()
                        });
                    }

                    cmbTestItems.Enabled = true;
                    lblStatus.Text = "请选择测试项目";
                    lblStatus.ForeColor = Color.Blue;
                }
                else
                {
                    lblStatus.Text = "该委托单号下没有找到测试项目";
                    lblStatus.ForeColor = Color.Orange;
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"加载测试项目失败: {ex.Message}";
                lblStatus.ForeColor = Color.Red;
            }
        }

        /// <summary>
        /// 测试项目选择变化事件
        /// </summary>
        private async void CmbTestItems_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbTestItems.SelectedItem == null) return;

            try
            {
                var selectedItem = cmbTestItems.SelectedItem as TestItemModel;
                if (selectedItem == null) return;

                _selectedTestItemId = selectedItem.id;

                lblStatus.Text = "正在加载照片部位和样品编号...";
                lblStatus.ForeColor = Color.Blue;

                // 加载照片部位
                await LoadPhotoPositions();

                // 加载样品编号
                await LoadSampleNumbers();
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"选择测试项目失败: {ex.Message}";
                lblStatus.ForeColor = Color.Red;
            }
        }

        /// <summary>
        /// 加载照片部位列表
        /// </summary>
        private async Task LoadPhotoPositions()
        {
            try
            {
                var url = $"{_imageApiClient.BaseUrl}/ppt/getPhotoPositionsByTestItem";
                var param = new { testItemId = _selectedTestItemId };

                var response = await _imageApiClient.SendWithTokenAuthAsync<List<string>>(url, System.Net.Http.HttpMethod.Post, param);

                clbPhotoPositions.Items.Clear();
                _selectedPhotoPositions.Clear();

                if (response != null && response.Count > 0)
                {
                    foreach (var position in response)
                    {
                        clbPhotoPositions.Items.Add(position);
                    }

                    clbPhotoPositions.Enabled = true;
                    btnSelectAll.Enabled = true;
                    btnSelectNone.Enabled = true;

                    lblStatus.Text = "请选择照片部位和测试阶段";
                    lblStatus.ForeColor = Color.Blue;
                }
                else
                {
                    lblStatus.Text = "该测试项目没有可用的照片部位";
                    lblStatus.ForeColor = Color.Orange;
                }

                UpdateGenerateButtonState();
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"加载照片部位失败: {ex.Message}";
                lblStatus.ForeColor = Color.Red;
            }
        }

        /// <summary>
        /// 加载样品编号列表
        /// </summary>
        private async Task LoadSampleNumbers()
        {
            try
            {
                var url = $"{_imageApiClient.BaseUrl}/ppt/getSampleNumbersByDelegate";
                var param = new {
                    delegateNumber = _selectedDelegateNumber,
                    testItemId = _selectedTestItemId
                };

                var response = await _imageApiClient.SendWithTokenAuthAsync<List<string>>(url, System.Net.Http.HttpMethod.Post, param);

                clbSampleNumbers.Items.Clear();
                _selectedSampleNumbers.Clear();

                if (response != null && response.Count > 0)
                {
                    foreach (var sampleNumber in response)
                    {
                        clbSampleNumbers.Items.Add(sampleNumber, true); // 默认全选
                        _selectedSampleNumbers.Add(sampleNumber);
                    }

                    clbSampleNumbers.Enabled = true;
                    btnSelectAllSamples.Enabled = true;
                    btnSelectNoneSamples.Enabled = true;

                    lblStatus.Text = "请选择照片部位、样品编号和测试阶段";
                    lblStatus.ForeColor = Color.Blue;
                }
                else
                {
                    lblStatus.Text = "该测试项目没有可用的样品编号";
                    lblStatus.ForeColor = Color.Orange;
                }

                UpdateGenerateButtonState();
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"加载样品编号失败: {ex.Message}";
                lblStatus.ForeColor = Color.Red;
            }
        }

        /// <summary>
        /// 照片部位勾选变化事件
        /// </summary>
        private void ClbPhotoPositions_ItemCheck(object sender, ItemCheckEventArgs e)
        {
            // 使用BeginInvoke确保在UI更新后执行
            this.BeginInvoke(new Action(() => {
                _selectedPhotoPositions.Clear();
                for (int i = 0; i < clbPhotoPositions.Items.Count; i++)
                {
                    if (clbPhotoPositions.GetItemChecked(i))
                    {
                        _selectedPhotoPositions.Add(clbPhotoPositions.Items[i].ToString());
                    }
                }
                UpdateGenerateButtonState();
            }));
        }

        /// <summary>
        /// 全选按钮点击事件
        /// </summary>
        private void BtnSelectAll_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < clbPhotoPositions.Items.Count; i++)
            {
                clbPhotoPositions.SetItemChecked(i, true);
            }
        }

        /// <summary>
        /// 清空按钮点击事件
        /// </summary>
        private void BtnSelectNone_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < clbPhotoPositions.Items.Count; i++)
            {
                clbPhotoPositions.SetItemChecked(i, false);
            }
        }

        /// <summary>
        /// 样品编号勾选变化事件
        /// </summary>
        private void ClbSampleNumbers_ItemCheck(object sender, ItemCheckEventArgs e)
        {
            // 使用BeginInvoke确保在UI更新后执行
            this.BeginInvoke(new Action(() => {
                _selectedSampleNumbers.Clear();
                for (int i = 0; i < clbSampleNumbers.Items.Count; i++)
                {
                    if (clbSampleNumbers.GetItemChecked(i))
                    {
                        _selectedSampleNumbers.Add(clbSampleNumbers.Items[i].ToString());
                    }
                }
                UpdateGenerateButtonState();
            }));
        }

        /// <summary>
        /// 样品编号全选按钮点击事件
        /// </summary>
        private void BtnSelectAllSamples_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < clbSampleNumbers.Items.Count; i++)
            {
                clbSampleNumbers.SetItemChecked(i, true);
            }
        }

        /// <summary>
        /// 样品编号清空按钮点击事件
        /// </summary>
        private void BtnSelectNoneSamples_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < clbSampleNumbers.Items.Count; i++)
            {
                clbSampleNumbers.SetItemChecked(i, false);
            }
        }

        /// <summary>
        /// 测试阶段勾选变化事件
        /// </summary>
        private void TestStage_CheckedChanged(object sender, EventArgs e)
        {
            _selectedTestStages.Clear();
            if (chkBeforeTest.Checked)
                _selectedTestStages.Add("before");
            if (chkAfterTest.Checked)
                _selectedTestStages.Add("after");

            // 更新生成按钮状态
            UpdateGenerateButtonState();
        }

        /// <summary>
        /// 更新生成按钮状态
        /// </summary>
        private void UpdateGenerateButtonState()
        {
            btnGenerate.Enabled = !string.IsNullOrEmpty(_selectedDelegateNumber) &&
                                 !string.IsNullOrEmpty(_selectedTestItemId) &&
                                 _selectedPhotoPositions.Count > 0 &&
                                 _selectedSampleNumbers.Count > 0 &&
                                 _selectedTestStages.Count > 0;
        }

        /// <summary>
        /// 生成表格按钮点击事件
        /// </summary>
        private async void BtnGenerate_Click(object sender, EventArgs e)
        {
            try
            {
                // 禁用生成按钮
                btnGenerate.Enabled = false;
                lblStatus.Text = "正在生成表格...";
                lblStatus.ForeColor = Color.Blue;


                // 获取PPT表格数据
                var tableData = await GetPptTableDataAsync();

                // 检查结果
                if (tableData != null)
                {
                    // 生成表格
                    if (_slide != null)
                    {
                        var tableGenerator = new PptTableGenerator(_slide, _imageApiClient,_progressManager);
                        var tableShape = await tableGenerator.GeneratePptTableAsync(tableData);

                        // 移除进度更新
                        // 不在这里关闭进度窗口，让TableGenerator在所有图片处理完成后关闭
                    }

                    // 关闭表单
                    DialogResult = DialogResult.OK;
                    Close();
                }
                else
                {
                    lblStatus.Text = "未获取到表格数据";
                    // 移除进度完成通知
                }
            }
            catch (UnauthorizedAccessException)
            {
                // 处理未授权异常
                lblStatus.Text = "登录已过期，请重新登录";
                lblStatus.ForeColor = Color.Red;

                // 显示登录表单
                var authService = new PBIppt.Services.AuthService();
                var loginForm = new LoginForm(authService);
                loginForm.ShowDialog();

                // 移除进度完成通知
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"生成表格失败: {ex.Message}";
                lblStatus.ForeColor = Color.Red;
                // 移除进度完成通知
            }
            finally
            {
                // 启用生成按钮
                btnGenerate.Enabled = true;
            }
        }

        /// <summary>
        /// 获取PPT表格数据
        /// </summary>
        private async Task<PptTableData> GetPptTableDataAsync()
        {
            try
            {
                var url = $"{_imageApiClient.BaseUrl}/ppt/getTableData";
                var param = new
                {
                    delegateNumber = _selectedDelegateNumber,
                    testItemId = _selectedTestItemId,
                    photoPositions = _selectedPhotoPositions,
                    selectedSampleNumbers = _selectedSampleNumbers,
                    testStages = _selectedTestStages
                };

                var response = await _imageApiClient.SendWithTokenAuthAsync<object>(url, System.Net.Http.HttpMethod.Post, param);

                if (response != null)
                {
                    // 将响应转换为PptTableData对象
                    return ConvertResponseToPptTableData(response);
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取PPT表格数据失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 将API响应转换为PptTableData对象
        /// </summary>
        private PptTableData ConvertResponseToPptTableData(object response)
        {
            try
            {
                if (response is Newtonsoft.Json.Linq.JObject jObject)
                {
                    var  tableData = new PptTableData
                    {
                        DelegateNumber = jObject["delegateNumber"]?.ToString() ?? "",
                        TestItemId = jObject["testItemId"]?.ToString() ?? "",
                        TestItemName = jObject["testItemName"]?.ToString() ?? "",
                        PhotoPositions = jObject["photoPositions"]?.ToObject<List<string>>() ?? new List<string>(),
                        TestStages = jObject["testStages"]?.ToObject<List<string>>() ?? new List<string>()
                    };
                    // 转换电芯行数据
                    if (jObject["batteryRows"] is Newtonsoft.Json.Linq.JArray batteryRowsArray)
                    {
                        foreach (var rowToken in batteryRowsArray)
                        {
                            if (rowToken is Newtonsoft.Json.Linq.JObject rowObj)
                            {

                                var batteryRow = new BatteryRowData
                                {
                                    CellNumber = rowObj["cellNumber"]?.ToString() ?? "",
                                    Soc = rowObj["soc"]?.ToString() ?? "",
                                    TestResult = rowObj["testResult"]?.ToString() ?? ""
                                };

                                // 转换测试前照片
                                if (rowObj["beforeTestPhotos"] is Newtonsoft.Json.Linq.JObject beforePhotosObj)
                                {
                                    batteryRow.BeforeTestPhotos = beforePhotosObj.ToObject<Dictionary<string, string>>() ?? new Dictionary<string, string>();
                                }

                                // 转换测试后照片
                                if (rowObj["afterTestPhotos"] is Newtonsoft.Json.Linq.JObject afterPhotosObj)
                                {
                                    batteryRow.AfterTestPhotos = afterPhotosObj.ToObject<Dictionary<string, string>>() ?? new Dictionary<string, string>();
                                }

                                // 后端已经按电芯拆分，直接添加
                                tableData.BatteryRows.Add(batteryRow);
                            }
                        }
                    }
                    return tableData;
                }

                // 处理Dictionary类型（备用）
                var responseDict = response as Dictionary<string, object>;
                if (responseDict == null)
                {
                    System.Diagnostics.Debug.WriteLine("响应既不是JObject也不是Dictionary<string, object>类型");
                    return null;
                }

                var dictTableData = new PptTableData
                {
                    DelegateNumber = responseDict.ContainsKey("delegateNumber") ? responseDict["delegateNumber"]?.ToString() : "",
                    TestItemId = responseDict.ContainsKey("testItemId") ? responseDict["testItemId"]?.ToString() : "",
                    TestItemName = responseDict.ContainsKey("testItemName") ? responseDict["testItemName"]?.ToString() : "",
                    PhotoPositions = responseDict.ContainsKey("photoPositions") ?
                        (responseDict["photoPositions"] as List<object>)?.Cast<string>().ToList() ?? new List<string>() :
                        new List<string>(),
                    TestStages = responseDict.ContainsKey("testStages") ?
                        (responseDict["testStages"] as List<object>)?.Cast<string>().ToList() ?? new List<string>() :
                        new List<string>()
                };

                // 转换电芯行数据
                if (responseDict.ContainsKey("batteryRows"))
                {
                    if (responseDict["batteryRows"] is List<object> batteryRowsList)
                    {
                        foreach (var rowObj in batteryRowsList)
                        {
                            if (rowObj is Dictionary<string, object> rowDict)
                            {
                                var batteryRow = new BatteryRowData
                                {
                                    CellNumber = rowDict.ContainsKey("cellNumber") ? rowDict["cellNumber"]?.ToString() : "",
                                    Soc = rowDict.ContainsKey("soc") ? rowDict["soc"]?.ToString() : "",
                                    TestResult = rowDict.ContainsKey("testResult") ? rowDict["testResult"]?.ToString() : ""
                                };

                                // 转换测试前照片
                                if (rowDict.ContainsKey("beforeTestPhotos"))
                                {

                                    if (rowDict["beforeTestPhotos"] is Dictionary<string, object> beforePhotos)
                                    {
                                        batteryRow.BeforeTestPhotos = beforePhotos.ToDictionary(
                                            kvp => kvp.Key,
                                            kvp => kvp.Value?.ToString() ?? ""
                                        );
                                    }
                                }

                                // 转换测试后照片
                                if (rowDict.ContainsKey("afterTestPhotos"))
                                {

                                    if (rowDict["afterTestPhotos"] is Dictionary<string, object> afterPhotos)
                                    {
                                        batteryRow.AfterTestPhotos = afterPhotos.ToDictionary(
                                            kvp => kvp.Key,
                                            kvp => kvp.Value?.ToString() ?? ""
                                        );
                                    }
                                }

                                // 后端已经按电芯拆分，直接添加
                                dictTableData.BatteryRows.Add(batteryRow);                            }
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("batteryRows不是List<object>类型");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("响应中没有找到batteryRows字段");
                }
                return dictTableData;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"转换响应数据失败: {ex.Message}");
                return null;
            }
        }


    }
}
