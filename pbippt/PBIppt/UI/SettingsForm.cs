using System;
using System.Windows.Forms;

namespace PBIppt.UI
{
    /// <summary>
    /// 设置表单
    /// </summary>
    public partial class SettingsForm : Form
    {
        private string _apiBaseUrl;
        
        /// <summary>
        /// 创建设置表单
        /// </summary>
        /// <param name="currentApiBaseUrl">当前API基础URL</param>
        public SettingsForm(string currentApiBaseUrl)
        {
            InitializeComponent();
            _apiBaseUrl = currentApiBaseUrl;
            txtApiBaseUrl.Text = _apiBaseUrl;
        }
        
        /// <summary>
        /// API基础URL
        /// </summary>
        public string ApiBaseUrl => _apiBaseUrl;
        
        /// <summary>
        /// 保存按钮点击事件
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            // 验证URL格式
            string url = txtApiBaseUrl.Text.Trim();
            if (string.IsNullOrWhiteSpace(url))
            {
                lblStatus.Text = "请输入API服务器地址";
                lblStatus.Visible = true;
                return;
            }
            
            try
            {
                // 简单验证URL格式
                if (!url.StartsWith("http://") && !url.StartsWith("https://"))
                {
                    url = "http://" + url;
                }
                
                // 创建URI对象验证格式
                var uri = new Uri(url);
                
                // 保存设置
                _apiBaseUrl = url;
                
                // 关闭表单
                DialogResult = DialogResult.OK;
                Close();
            }
            catch
            {
                lblStatus.Text = "无效的服务器地址，请使用格式: http://域名或IP:端口";
                lblStatus.Visible = true;
            }
        }
        
        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            this.lblApiBaseUrl = new System.Windows.Forms.Label();
            this.txtApiBaseUrl = new System.Windows.Forms.TextBox();
            this.btnSave = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.lblStatus = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // lblApiBaseUrl
            // 
            this.lblApiBaseUrl.AutoSize = true;
            this.lblApiBaseUrl.Location = new System.Drawing.Point(24, 28);
            this.lblApiBaseUrl.Name = "lblApiBaseUrl";
            this.lblApiBaseUrl.Size = new System.Drawing.Size(113, 13);
            this.lblApiBaseUrl.TabIndex = 0;
            this.lblApiBaseUrl.Text = "API服务器地址：";
            // 
            // txtApiBaseUrl
            // 
            this.txtApiBaseUrl.Location = new System.Drawing.Point(143, 25);
            this.txtApiBaseUrl.Name = "txtApiBaseUrl";
            this.txtApiBaseUrl.Size = new System.Drawing.Size(250, 20);
            this.txtApiBaseUrl.TabIndex = 1;
            // 
            // btnSave
            // 
            this.btnSave.Location = new System.Drawing.Point(143, 70);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(75, 23);
            this.btnSave.TabIndex = 2;
            this.btnSave.Text = "保存";
            this.btnSave.UseVisualStyleBackColor = true;
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(318, 70);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 3;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // lblStatus
            // 
            this.lblStatus.AutoSize = true;
            this.lblStatus.ForeColor = System.Drawing.Color.Red;
            this.lblStatus.Location = new System.Drawing.Point(143, 105);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(0, 13);
            this.lblStatus.TabIndex = 4;
            this.lblStatus.Visible = false;
            // 
            // SettingsForm
            // 
            this.AcceptButton = this.btnSave;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(420, 130);
            this.Controls.Add(this.lblStatus);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.txtApiBaseUrl);
            this.Controls.Add(this.lblApiBaseUrl);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "SettingsForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "API设置";
            this.ResumeLayout(false);
            this.PerformLayout();
        }
        
        private System.Windows.Forms.Label lblApiBaseUrl;
        private System.Windows.Forms.TextBox txtApiBaseUrl;
        private System.Windows.Forms.Button btnSave;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Label lblStatus;
    }
} 