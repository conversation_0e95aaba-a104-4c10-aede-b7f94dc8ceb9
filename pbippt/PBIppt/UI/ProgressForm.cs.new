using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace PBIppt.UI
{
    /// <summary>
    /// 进度显示表单
    /// </summary>
    public partial class ProgressForm : Form
    {
        /// <summary>
        /// 创建进度表单
        /// </summary>
        public ProgressForm()
        {
            InitializeComponent();
        }
        
        /// <summary>
        /// 创建进度表单
        /// </summary>
        public ProgressForm(string title)
        {
            InitializeComponent();
            this.Text = title;
        }
        
        /// <summary>
        /// 更新进度信息
        /// </summary>
        /// <param name="progress">进度值(0-100)</param>
        /// <param name="message">进度消息</param>
        public void UpdateProgress(int progress, string message)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action<int, string>(UpdateProgress), progress, message);
                return;
            }
            
            progressBar.Value = Math.Min(100, Math.Max(0, progress));
            lblMessage.Text = message;
            Application.DoEvents();
        }
        
        #region Windows Form Designer generated code
        
        private ProgressBar progressBar;
        private Label lblMessage;
        
        private void InitializeComponent()
        {
            this.progressBar = new System.Windows.Forms.ProgressBar();
            this.lblMessage = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // progressBar
            // 
            this.progressBar.Location = new System.Drawing.Point(30, 30);
            this.progressBar.Name = "progressBar";
            this.progressBar.Size = new System.Drawing.Size(340, 23);
            this.progressBar.TabIndex = 0;
            // 
            // lblMessage
            // 
            this.lblMessage.AutoSize = true;
            this.lblMessage.Location = new System.Drawing.Point(30, 70);
            this.lblMessage.Name = "lblMessage";
            this.lblMessage.Size = new System.Drawing.Size(80, 17);
            this.lblMessage.TabIndex = 1;
            this.lblMessage.Text = "正在处理...";
            // 
            // ProgressForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 17F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(400, 120);
            this.Controls.Add(this.lblMessage);
            this.Controls.Add(this.progressBar);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ProgressForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "处理中...";
            this.ResumeLayout(false);
            this.PerformLayout();
        }
        
        #endregion
    }
} 