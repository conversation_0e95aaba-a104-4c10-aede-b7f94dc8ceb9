using System;
using System.Windows.Forms;
using PBIppt.Services;
using PBIppt.Models;
using System.Threading.Tasks;

namespace PBIppt.UI
{
    /// <summary>
    /// 登录表单
    /// </summary>
    public partial class LoginForm : Form
    {
        private readonly IAuthService _authService;

        /// <summary>
        /// 创建登录表单
        /// </summary>
        public LoginForm(IAuthService authService = null)
        {
            _authService = authService ?? new AuthService();
            InitializeComponent();
        }
        
        /// <summary>
        /// 登录按钮点击事件
        /// </summary>
        private async void btnLogin_Click(object sender, EventArgs e)
        {
            // 禁用按钮防止重复点击
            btnLogin.Enabled = false;
            
            try
            {
                // 显示加载状态
                lblStatus.Text = "正在登录...";
                lblStatus.Visible = true;
                
                // 验证输入
                if (string.IsNullOrWhiteSpace(txtAccount.Text))
                {
                    lblStatus.Text = "请输入账号";
                    return;
                }
                
                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    lblStatus.Text = "请输入密码";
                    return;
                }
                
                // 创建登录请求
                var loginRequest = new LoginRequest
                {
                    Account = txtAccount.Text.Trim(),
                    Password = txtPassword.Text
                };
                
                // 调用专门的认证服务进行登录
                var response = await _authService.LoginAsync(loginRequest.Account, loginRequest.Password);

                // 处理登录结果
                if (response.Success)
                {
                    // AuthService.LoginAsync 已经正确设置了认证信息和角色信息到 AuthManager
                    System.Diagnostics.Debug.WriteLine("LoginForm: 登录成功，认证信息和角色信息已正确设置");

                    // 关闭登录窗口
                    DialogResult = DialogResult.OK;
                    Close();
                }
                else
                {
                    // 显示错误消息
                    lblStatus.Text = response.ErrorMessage ?? "登录失败，账号或密码错误";
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"登录失败: {ex.Message}";
            }
            finally
            {
                // 重新启用按钮
                btnLogin.Enabled = true;
            }
        }
        
        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            this.lblAccount = new System.Windows.Forms.Label();
            this.txtAccount = new System.Windows.Forms.TextBox();
            this.lblPassword = new System.Windows.Forms.Label();
            this.txtPassword = new System.Windows.Forms.TextBox();
            this.btnLogin = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.lblStatus = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // lblAccount
            // 
            this.lblAccount.AutoSize = true;
            this.lblAccount.Location = new System.Drawing.Point(24, 28);
            this.lblAccount.Name = "lblAccount";
            this.lblAccount.Size = new System.Drawing.Size(65, 13);
            this.lblAccount.TabIndex = 0;
            this.lblAccount.Text = "账号：";
            // 
            // txtAccount
            // 
            this.txtAccount.Location = new System.Drawing.Point(95, 25);
            this.txtAccount.Name = "txtAccount";
            this.txtAccount.Size = new System.Drawing.Size(200, 20);
            this.txtAccount.TabIndex = 1;
            // 
            // lblPassword
            // 
            this.lblPassword.AutoSize = true;
            this.lblPassword.Location = new System.Drawing.Point(24, 68);
            this.lblPassword.Name = "lblPassword";
            this.lblPassword.Size = new System.Drawing.Size(65, 13);
            this.lblPassword.TabIndex = 2;
            this.lblPassword.Text = "密码：";
            // 
            // txtPassword
            // 
            this.txtPassword.Location = new System.Drawing.Point(95, 65);
            this.txtPassword.Name = "txtPassword";
            this.txtPassword.PasswordChar = '*';
            this.txtPassword.Size = new System.Drawing.Size(200, 20);
            this.txtPassword.TabIndex = 3;
            // 
            // btnLogin
            // 
            this.btnLogin.Location = new System.Drawing.Point(95, 110);
            this.btnLogin.Name = "btnLogin";
            this.btnLogin.Size = new System.Drawing.Size(75, 23);
            this.btnLogin.TabIndex = 4;
            this.btnLogin.Text = "登录";
            this.btnLogin.UseVisualStyleBackColor = true;
            this.btnLogin.Click += new System.EventHandler(this.btnLogin_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(220, 110);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 5;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // lblStatus
            // 
            this.lblStatus.AutoSize = true;
            this.lblStatus.ForeColor = System.Drawing.Color.Red;
            this.lblStatus.Location = new System.Drawing.Point(24, 150);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(0, 13);
            this.lblStatus.TabIndex = 6;
            this.lblStatus.Visible = false;
            // 
            // LoginForm
            // 
            this.AcceptButton = this.btnLogin;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(320, 180);
            this.Controls.Add(this.lblStatus);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnLogin);
            this.Controls.Add(this.txtPassword);
            this.Controls.Add(this.lblPassword);
            this.Controls.Add(this.txtAccount);
            this.Controls.Add(this.lblAccount);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "LoginForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "登录";
            this.ResumeLayout(false);
            this.PerformLayout();
        }
        
        private System.Windows.Forms.Label lblAccount;
        private System.Windows.Forms.TextBox txtAccount;
        private System.Windows.Forms.Label lblPassword;
        private System.Windows.Forms.TextBox txtPassword;
        private System.Windows.Forms.Button btnLogin;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Label lblStatus;
    }
} 