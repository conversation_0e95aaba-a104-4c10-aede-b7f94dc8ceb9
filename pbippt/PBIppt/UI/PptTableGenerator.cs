using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using PBIppt.ApiClient;
using PBIppt.Models;
using PBIppt.Utils;
using PBIppt.ImageProcessing;
using PowerPoint = Microsoft.Office.Interop.PowerPoint;

namespace PBIppt.UI
{
    /// <summary>
    /// 表格单元格样式配置
    /// </summary>
    public class TableCellStyle
    {
        public Color BackgroundColor { get; set; }
        public string FontName { get; set; }
        public string FontNameFarEast { get; set; }
        public int FontSize { get; set; }
        public bool IsBold { get; set; }
        public Color FontColor { get; set; }
        public PowerPoint.PpParagraphAlignment HorizontalAlignment { get; set; }
        public Microsoft.Office.Core.MsoVerticalAnchor VerticalAlignment { get; set; }
    }

    /// <summary>
    /// 图片处理信息类
    /// </summary>
    public class ImageProcessingInfo
    {
        public string FileId { get; set; }
        public PowerPoint.Cell Cell { get; set; }
        public int RowIndex { get; set; }
        public int ColIndex { get; set; }
        public string Stage { get; set; }
        public string Position { get; set; }
        public string CellNumber { get; set; }
    }

    /// <summary>
    /// PPT表格生成器
    /// </summary>
    public class PptTableGenerator
    {
        private readonly PowerPoint.Slide _slide;
        private readonly IBatteryImageApiClient _imageApiClient;
        private readonly ProgressManager _progressManager;
        private readonly BatteryDetectionProcessor _batteryDetectionProcessor;

        // 图片尺寸缓存，避免重复计算
        private readonly Dictionary<string, (float Width, float Height)> _imageSizeCache = new Dictionary<string, (float Width, float Height)>();

        // 图片数据缓存（避免重复下载）
        private readonly Dictionary<string, byte[]> _imageDataCache = new Dictionary<string, byte[]>();

        // 电池检测结果缓存（避免重复API调用）
        private readonly Dictionary<string, BatteryDetectionApiResult> _detectionResultCache = new Dictionary<string, BatteryDetectionApiResult>();

        public PptTableGenerator(PowerPoint.Slide slide, IBatteryImageApiClient imageApiClient, ProgressManager progressManager)
        {
            _slide = slide;
            _imageApiClient = imageApiClient;
            _progressManager = progressManager;
            _batteryDetectionProcessor = new BatteryDetectionProcessor();
        }
        
        /// <summary>
        /// 生成PPT表格
        /// </summary>
        public async Task<PowerPoint.Shape> GeneratePptTableAsync(PptTableData tableData)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始生成PPT表格");

                // 计算表格尺寸
                int headerRows = 2; // 主标题行 + 子标题行
                int dataRows = tableData.BatteryRows.Count;
                int totalRows = headerRows + dataRows;

                // 计算列数：Cell No. + SOC + 测试前照片列 + 测试后照片列 + Test Result
                int beforeCols = tableData.TestStages.Contains("before") ? tableData.PhotoPositions.Count : 0;
                int afterCols = tableData.TestStages.Contains("after") ? tableData.PhotoPositions.Count : 0;
                int totalCols = 2 + beforeCols + afterCols + 1; // Cell No. + SOC + 照片列 + Test Result

                System.Diagnostics.Debug.WriteLine($"表格尺寸: {totalRows}行 x {totalCols}列");

                // 获取幻灯片尺寸
                float slideWidth = _slide.Master.Width;
                float slideHeight = _slide.Master.Height;

                // 预处理所有图片，获取准确的列宽需求
                System.Diagnostics.Debug.WriteLine("开始预处理图片数据以计算表格宽度");
                await PreprocessAllImages(tableData);

                // 计算实际需要的表格宽度
                float headerRowHeight = 40; // 表头行高度
                float dataRowHeight = 2.22f * 28.35f; // 数据行高度：2.22cm转换为点数 ≈ 62.9点

                // 先计算列宽需求
                var tempColumnWidths = await CalculateOptimalColumnWidths(tableData, slideWidth, dataRowHeight);
                float requiredTableWidth = tempColumnWidths.Values.Sum();

                // 确保表格宽度不超过幻灯片宽度的95%，但优先满足列宽需求
                float maxTableWidth = slideWidth * 0.95f;
                float tableWidth = Math.Min(requiredTableWidth, maxTableWidth);

                // 如果需要的宽度超过最大宽度，记录警告但仍使用需要的宽度
                if (requiredTableWidth > maxTableWidth)
                {
                    tableWidth = requiredTableWidth; // 使用实际需要的宽度
                    System.Diagnostics.Debug.WriteLine($"⚠️ 表格宽度{requiredTableWidth:F1}超出幻灯片95%宽度{maxTableWidth:F1}，但优先保证图片质量");
                }

                float tableHeight = Math.Min(slideHeight * 0.80f, headerRows * headerRowHeight + dataRows * dataRowHeight);
                float tableLeft = Math.Max(10, (slideWidth - tableWidth) / 2); // 水平居中，但确保不超出边界
                float tableTop = Math.Max(30, (slideHeight - tableHeight) / 2); // 确保有足够的上边距

                System.Diagnostics.Debug.WriteLine($"📊 表格尺寸计算: 需求宽度={requiredTableWidth:F1}, 实际宽度={tableWidth:F1}");
                System.Diagnostics.Debug.WriteLine($"📍 表格位置: Left={tableLeft:F1}, Top={tableTop:F1}, Width={tableWidth:F1}, Height={tableHeight:F1}");

                // 创建表格
                System.Diagnostics.Debug.WriteLine("开始创建表格");
                var table = _slide.Shapes.AddTable(totalRows, totalCols, tableLeft, tableTop, tableWidth, tableHeight);
                var tableObj = table.Table;
                System.Diagnostics.Debug.WriteLine("表格创建成功");

                // 设置表格样式
                System.Diagnostics.Debug.WriteLine("开始设置表格样式");
                try
                {
                    SetTableStyle(tableObj);

                    // 设置行高和列宽
                    for (int i = 1; i <= headerRows; i++)
                    {
                        try
                        {
                            tableObj.Rows[i].Height = headerRowHeight;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"设置表头行{i}高度失败: {ex.Message}");
                        }
                    }

                    for (int i = headerRows + 1; i <= totalRows; i++)
                    {
                        try
                        {
                            tableObj.Rows[i].Height = dataRowHeight;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"设置数据行{i}高度失败: {ex.Message}");
                        }
                    }

                    // 使用已计算的列宽设置表格列宽
                    try
                    {
                        System.Diagnostics.Debug.WriteLine("开始设置列宽（使用预计算结果）");

                        for (int i = 1; i <= tableObj.Columns.Count; i++)
                        {
                            if (tempColumnWidths.ContainsKey(i))
                            {
                                tableObj.Columns[i].Width = tempColumnWidths[i];
                                System.Diagnostics.Debug.WriteLine($"列{i}宽度设置为: {tempColumnWidths[i]:F1}");
                            }
                        }
                        System.Diagnostics.Debug.WriteLine("列宽设置完成");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"列宽自适应设置失败: {ex.Message}，使用默认宽度");
                        // 降级处理：设置Cell No列的固定宽度
                        try
                        {
                            tableObj.Columns[1].Width = 80; // Cell No列较窄
                        }
                        catch (Exception) { }
                    }

                    System.Diagnostics.Debug.WriteLine("表格样式设置成功");
                }
                catch (NotImplementedException ex)
                {
                    System.Diagnostics.Debug.WriteLine($"表格样式设置未实现，跳过: {ex.Message}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"表格样式设置失败，跳过: {ex.Message}");
                }

                // 填充表头
                System.Diagnostics.Debug.WriteLine("开始填充表头");
                try
                {
                    FillTableHeader(tableObj, tableData);
                    System.Diagnostics.Debug.WriteLine("表头填充成功");
                }
                catch (NotImplementedException ex)
                {
                    System.Diagnostics.Debug.WriteLine($"表头填充未实现，跳过: {ex.Message}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"表头填充失败，跳过: {ex.Message}");
                }

                // 填充数据行
                System.Diagnostics.Debug.WriteLine("开始填充数据行");
                try
                {
                    await FillTableDataAsync(tableObj, tableData, headerRows);
                    System.Diagnostics.Debug.WriteLine("数据行填充成功");
                }
                catch (NotImplementedException ex)
                {
                    System.Diagnostics.Debug.WriteLine($"数据行填充未实现，跳过: {ex.Message}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"数据行填充失败，跳过: {ex.Message}");
                }

                // 移除进度完成通知
                System.Diagnostics.Debug.WriteLine("PPT表格生成完成");

                // 清理缓存，避免内存泄漏
                ClearImageCache();

                return table;
            }
            catch (NotImplementedException ex)
            {
                System.Diagnostics.Debug.WriteLine($"表格生成功能未实现: {ex.Message}");
                // 移除进度完成通知
                throw;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"表格生成失败: {ex.Message}");
                // 移除进度完成通知
                throw;
            }
        }
        
        /// <summary>
        /// 设置表格样式
        /// </summary>
        private void SetTableStyle(PowerPoint.Table table)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始设置表格样式（使用兼容方法）");

                // 使用PowerPoint内置表格样式，避免单独设置边框
                try
                {
                    // 应用内置的表格样式
                    table.ApplyStyle("{5C22544A-7EE6-4342-B048-85BDC9FD1C3A}"); // 简单表格样式
                    System.Diagnostics.Debug.WriteLine("应用内置表格样式成功");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"应用内置表格样式失败: {ex.Message}");
                }

                // 设置自定义边框：所有框线，灰色#7f7f7f
                try
                {
                    System.Diagnostics.Debug.WriteLine("开始设置表格边框");
                    SetTableBorders(table);
                    System.Diagnostics.Debug.WriteLine("表格边框设置完成");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"设置表格边框失败: {ex.Message}");
                }

                // 使用优化的样式设置方法（行级批量处理）
                try
                {
                    System.Diagnostics.Debug.WriteLine("开始使用优化的样式设置方法");

                    // 设置表头样式（第1-2行）
                    SetTableHeaderStyle(table, headerRows: 2);

                    // 设置内容行样式（第3行开始）
                    SetTableContentStyle(table, startRow: 3);

                    System.Diagnostics.Debug.WriteLine("优化样式设置完成");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"优化样式设置失败: {ex.Message}");
                }

                System.Diagnostics.Debug.WriteLine("表格样式设置完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置表格样式失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 填充表头
        /// </summary>
        private void FillTableHeader(PowerPoint.Table table, PptTableData tableData)
        {
            try
            {
                int colIndex = 1;

                // 第一行主标题（只设置文本，样式已在SetTableStyle中统一设置）
                try
                {
                    table.Cell(1, colIndex).Shape.TextFrame.TextRange.Text = "Cell No.";
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"设置Cell No.标题失败: {ex.Message}");
                }
                colIndex++;

                try
                {
                    table.Cell(1, colIndex).Shape.TextFrame.TextRange.Text = "SOC";
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"设置SOC标题失败: {ex.Message}");
                }
                colIndex++;

                // 测试前照片列
                if (tableData.TestStages.Contains("before"))
                {
                    int beforeStartCol = colIndex;
                    foreach (var position in tableData.PhotoPositions)
                    {
                        try
                        {
                            table.Cell(2, colIndex).Shape.TextFrame.TextRange.Text = position;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"设置测试前照片列{position}失败: {ex.Message}");
                        }
                        colIndex++;
                    }

                    // 合并第一行的"Before Test"标题
                    try
                    {
                        if (tableData.PhotoPositions.Count > 1)
                        {
                            table.Cell(1, beforeStartCol).Merge(table.Cell(1, colIndex - 1));
                        }
                        table.Cell(1, beforeStartCol).Shape.TextFrame.TextRange.Text = "Before Test";
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"设置Before Test标题失败: {ex.Message}");
                    }
                }

                // 测试后照片列
                if (tableData.TestStages.Contains("after"))
                {
                    int afterStartCol = colIndex;
                    foreach (var position in tableData.PhotoPositions)
                    {
                        try
                        {
                            table.Cell(2, colIndex).Shape.TextFrame.TextRange.Text = position;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"设置测试后照片列{position}失败: {ex.Message}");
                        }
                        colIndex++;
                    }

                    // 合并第一行的"After Test"标题
                    try
                    {
                        if (tableData.PhotoPositions.Count > 1)
                        {
                            table.Cell(1, afterStartCol).Merge(table.Cell(1, colIndex - 1));
                        }
                        table.Cell(1, afterStartCol).Shape.TextFrame.TextRange.Text = $"After Test ({tableData.TestItemName})";
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"设置After Test标题失败: {ex.Message}");
                    }
                }

                // Test Result列
                try
                {
                    table.Cell(1, colIndex).Shape.TextFrame.TextRange.Text = "Test Result";
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"设置Test Result标题失败: {ex.Message}");
                }

                // 合并Cell No.和SOC的第二行
                try
                {
                    table.Cell(1, 1).Merge(table.Cell(2, 1));
                    table.Cell(1, 2).Merge(table.Cell(2, 2));
                    table.Cell(1, colIndex).Merge(table.Cell(2, colIndex));
                }
                catch (NotImplementedException)
                {
                    System.Diagnostics.Debug.WriteLine("单元格合并功能未实现，跳过");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"单元格合并失败: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"填充表头失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 填充数据行（批量处理版本）
        /// </summary>
        private async Task FillTableDataAsync(PowerPoint.Table table, PptTableData tableData, int headerRows)
        {
            int totalBatteries = tableData.BatteryRows.Count;
            int processedBatteries = 0;

            // 数据诊断
            System.Diagnostics.Debug.WriteLine("=== PPT表格数据诊断 ===");
            System.Diagnostics.Debug.WriteLine($"委托单号: {tableData.DelegateNumber}");
            System.Diagnostics.Debug.WriteLine($"测试项目: {tableData.TestItemName}");
            System.Diagnostics.Debug.WriteLine($"照片部位: [{string.Join(", ", tableData.PhotoPositions)}]");
            System.Diagnostics.Debug.WriteLine($"测试阶段: [{string.Join(", ", tableData.TestStages)}]");
            System.Diagnostics.Debug.WriteLine($"电芯行数: {tableData.BatteryRows.Count}");

            // 简化的数据映射诊断
            System.Diagnostics.Debug.WriteLine($"数据概览: {tableData.BatteryRows.Count}个电芯, 部位:{string.Join(",", tableData.PhotoPositions)}, 阶段:{string.Join(",", tableData.TestStages)}");

            // 第一步：收集所有需要处理的图片信息
            var imageInfoList = new List<ImageProcessingInfo>();

            for (int i = 0; i < tableData.BatteryRows.Count; i++)
            {
                var batteryRow = tableData.BatteryRows[i];
                int rowIndex = headerRows + 1 + i;
                int colIndex = 1;

                // 只在数据异常时显示警告
                if (batteryRow.BeforeTestPhotos.Count == 0 && batteryRow.AfterTestPhotos.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ 电芯 {batteryRow.CellNumber} 无照片数据");
                }

                // 移除进度更新
                processedBatteries++;

                // Cell No. - 后端已经按电芯拆分，直接显示
                var cellNoCell = table.Cell(rowIndex, colIndex);
                cellNoCell.Shape.TextFrame.TextRange.Text = batteryRow.CellNumber;

                // 设置Cell No单元格样式，防止撑高行
                try
                {
                    cellNoCell.Shape.TextFrame.WordWrap = Microsoft.Office.Core.MsoTriState.msoTrue;
                    cellNoCell.Shape.TextFrame.AutoSize = PowerPoint.PpAutoSize.ppAutoSizeNone;

                    // 设置Cell No列字体样式（遵循内容行规范）
                    var textRange = cellNoCell.Shape.TextFrame.TextRange;
                    textRange.Font.Name = "Times New Roman";
                    textRange.Font.NameFarEast = "思源黑体 CN Regular"; // 中文字体
                    textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
                    textRange.Font.Size = 6; // 内容行字号6
                    textRange.Font.Bold = Microsoft.Office.Core.MsoTriState.msoFalse; // 内容行不加粗

                    // 段落和垂直对齐
                    textRange.ParagraphFormat.Alignment = PowerPoint.PpParagraphAlignment.ppAlignCenter;
                    cellNoCell.Shape.TextFrame.VerticalAnchor = Microsoft.Office.Core.MsoVerticalAnchor.msoAnchorMiddle;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"设置Cell No单元格样式失败: {ex.Message}");
                }
                colIndex++;
                
                // SOC
                var socCell = table.Cell(rowIndex, colIndex);
                socCell.Shape.TextFrame.TextRange.Text = batteryRow.Soc;

                // 确保SOC列字体样式正确（内容行规范）
                try
                {
                    if (socCell.Shape.HasTextFrame == Microsoft.Office.Core.MsoTriState.msoTrue)
                    {
                        var textRange = socCell.Shape.TextFrame.TextRange;
                        textRange.Font.Name = "Times New Roman";
                        textRange.Font.NameFarEast = "思源黑体 CN Regular";
                        textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
                        textRange.Font.Size = 6;
                        textRange.Font.Bold = Microsoft.Office.Core.MsoTriState.msoFalse;
                        textRange.ParagraphFormat.Alignment = PowerPoint.PpParagraphAlignment.ppAlignCenter;

                        // 垂直对齐（中部对齐）
                        socCell.Shape.TextFrame.VerticalAnchor = Microsoft.Office.Core.MsoVerticalAnchor.msoAnchorMiddle;
                    }
                }
                catch (Exception)
                {
                    // 静默处理SOC字体设置失败
                }
                colIndex++;
                
                // 收集测试前照片信息
                if (tableData.TestStages.Contains("before"))
                {
                    int beforeCount = 0;
                    foreach (var position in tableData.PhotoPositions)
                    {
                        if (batteryRow.BeforeTestPhotos.ContainsKey(position))
                        {
                            string fileId = batteryRow.BeforeTestPhotos[position];
                            imageInfoList.Add(new ImageProcessingInfo
                            {
                                FileId = fileId,
                                Cell = table.Cell(rowIndex, colIndex),
                                RowIndex = rowIndex,
                                ColIndex = colIndex,
                                Stage = "before",
                                Position = position,
                                CellNumber = batteryRow.CellNumber
                            });
                            beforeCount++;
                        }
                        colIndex++;
                    }
                }

                // 收集测试后照片信息
                if (tableData.TestStages.Contains("after"))
                {
                    int afterCount = 0;
                    foreach (var position in tableData.PhotoPositions)
                    {
                        if (batteryRow.AfterTestPhotos.ContainsKey(position))
                        {
                            string fileId = batteryRow.AfterTestPhotos[position];
                            imageInfoList.Add(new ImageProcessingInfo
                            {
                                FileId = fileId,
                                Cell = table.Cell(rowIndex, colIndex),
                                RowIndex = rowIndex,
                                ColIndex = colIndex,
                                Stage = "after",
                                Position = position,
                                CellNumber = batteryRow.CellNumber
                            });
                            afterCount++;
                        }
                        colIndex++;
                    }
                }
                
                // Test Result
                var testResultCell = table.Cell(rowIndex, colIndex);
                testResultCell.Shape.TextFrame.TextRange.Text = batteryRow.TestResult;

                // 确保Test Result列字体样式正确（内容行规范）
                try
                {
                    if (testResultCell.Shape.HasTextFrame == Microsoft.Office.Core.MsoTriState.msoTrue)
                    {
                        var textRange = testResultCell.Shape.TextFrame.TextRange;
                        textRange.Font.Name = "Times New Roman";
                        textRange.Font.NameFarEast = "思源黑体 CN Regular";
                        textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
                        textRange.Font.Size = 6;
                        textRange.Font.Bold = Microsoft.Office.Core.MsoTriState.msoFalse;
                        textRange.ParagraphFormat.Alignment = PowerPoint.PpParagraphAlignment.ppAlignCenter;

                        // 垂直对齐（中部对齐）
                        testResultCell.Shape.TextFrame.VerticalAnchor = Microsoft.Office.Core.MsoVerticalAnchor.msoAnchorMiddle;
                    }
                }
                catch (Exception)
                {
                    // 静默处理Test Result字体设置失败
                }
            }

            // 第二步：批量处理所有收集到的图片
            await ProcessImagesInBatch(imageInfoList);
        }

        /// <summary>
        /// 设置表格边框：所有框线，灰色#7f7f7f
        /// </summary>
        /// <param name="table">表格对象</param>
        private void SetTableBorders(PowerPoint.Table table)
        {
            try
            {
                // 定义边框颜色：灰色#7f7f7f
                int borderColor = ColorTranslator.ToOle(ColorTranslator.FromHtml("#7f7f7f"));
                float borderWeight = 0.75f; // 边框粗细

                System.Diagnostics.Debug.WriteLine($"设置边框颜色: #7f7f7f, 表格尺寸: {table.Rows.Count}x{table.Columns.Count}");

                // 遍历所有单元格设置边框
                for (int row = 1; row <= table.Rows.Count; row++)
                {
                    for (int col = 1; col <= table.Columns.Count; col++)
                    {
                        try
                        {
                            var cell = table.Cell(row, col);

                            // 设置所有边框：上、下、左、右
                            SetCellBorder(cell, PowerPoint.PpBorderType.ppBorderTop, borderColor, borderWeight);
                            SetCellBorder(cell, PowerPoint.PpBorderType.ppBorderBottom, borderColor, borderWeight);
                            SetCellBorder(cell, PowerPoint.PpBorderType.ppBorderLeft, borderColor, borderWeight);
                            SetCellBorder(cell, PowerPoint.PpBorderType.ppBorderRight, borderColor, borderWeight);
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"设置单元格({row},{col})边框失败: {ex.Message}");
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine("表格边框设置完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置表格边框失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置单个单元格的边框
        /// </summary>
        /// <param name="cell">单元格</param>
        /// <param name="borderType">边框类型</param>
        /// <param name="color">边框颜色</param>
        /// <param name="weight">边框粗细</param>
        private void SetCellBorder(PowerPoint.Cell cell, PowerPoint.PpBorderType borderType, int color, float weight)
        {
            try
            {
                var border = cell.Borders[borderType];
                border.ForeColor.RGB = color;
                border.Weight = weight;
                border.Visible = Microsoft.Office.Core.MsoTriState.msoTrue;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置边框失败 ({borderType}): {ex.Message}");
            }
        }

        /// <summary>
        /// 向单元格插入图片（内存流处理版本：避免临时文件，减少加密系统影响）
        /// </summary>
        private async Task InsertImageToCell(PowerPoint.Cell cell, string fileId, int row, int col)
        {
            try
            {
                if (string.IsNullOrEmpty(fileId)) return;

                // 1. 下载原始图片数据
                byte[] originalImageData = await _imageApiClient.DownloadImageDataByFileIdAsync(fileId);
                if (originalImageData == null || originalImageData.Length == 0)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ 图片下载失败: {fileId}");
                    return;
                }

                // 2. 进行电池检测（用于PPT内置裁剪，优先使用缓存）
                BatteryDetectionApiResult detectionResult = null;
                try
                {
                    // 检查检测结果缓存
                    if (_detectionResultCache.ContainsKey(fileId))
                    {
                        detectionResult = _detectionResultCache[fileId];
                        System.Diagnostics.Debug.WriteLine($"📦 复用缓存的检测结果: {fileId}");
                    }
                    else
                    {
                        detectionResult = await DetectBatteryInOriginalImage(originalImageData, fileId);
                        // 缓存检测结果
                        if (detectionResult != null)
                        {
                            _detectionResultCache[fileId] = detectionResult;
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ 检测异常: {fileId}, {ex.Message}");
                }

                System.Diagnostics.Debug.WriteLine($"🔄 准备使用内存流处理插入图片: {fileId}");

                try
                {
                    // 3. 获取单元格信息
                    float cellWidth = cell.Shape.Width;
                    float cellHeight = cell.Shape.Height;
                    float cellLeft = cell.Shape.Left;
                    float cellTop = cell.Shape.Top;

                    // 4. 计算初始显示尺寸（优先让高度达到2.1cm，信任列宽计算结果）
                    // 由于列宽已经精确计算过，不再限制宽度，确保图片能达到2.1cm高度
                    float maxHeight = cellHeight * 0.95f; // 高度留5%边距
                    var (initialWidth, initialHeight) = CalculateImageSizeWithAspectRatio(originalImageData, float.MaxValue, maxHeight);

                    // 5. 计算居中位置
                    float left = cellLeft + (cellWidth - initialWidth) / 2;
                    float top = cellTop + (cellHeight - initialHeight) / 2;

                    // 6. 使用内存流方式插入图片到PowerPoint
                    PowerPoint.Shape picture = InsertImageFromMemoryStream(originalImageData, left, top, initialWidth, initialHeight, fileId);

                    // 7. 给图片添加标记，便于后续识别
                    try
                    {
                        picture.Name = $"BatteryImage_{row}_{col}_{DateTime.Now.Ticks}";
                        picture.AlternativeText = $"TableCell_{row}_{col}";
                    }
                    catch (Exception)
                    {
                        // 静默处理标记设置失败
                    }

                    // 8. 清空单元格文本
                    try
                    {
                        cell.Shape.TextFrame.TextRange.Text = "";
                    }
                    catch (Exception) { }

                    // 9. 应用电池检测结果（使用PPT内置裁剪）
                    if (detectionResult != null && HasValidCropData(detectionResult))
                    {
                        try
                        {
                            var (originalWidth, originalHeight) = GetImageDimensionsFromApiData(detectionResult, originalImageData);
                            var tableProcessor = new ImageProcessing.BatteryTableImageProcessor();
                            bool cropSuccess = tableProcessor.ApplyDetectionToPowerPointShape(picture, detectionResult, originalWidth, originalHeight);

                            if (cropSuccess)
                            {
                                System.Diagnostics.Debug.WriteLine($"✅ PPT内置裁剪成功: {fileId}");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"❌ PPT内置裁剪失败: {fileId}");
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ PPT内置裁剪异常: {fileId}, {ex.Message}");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ 无有效检测结果，跳过PPT裁剪: {fileId}");
                    }

                    // 10. 基于API数据计算是否需要旋转（避免加密问题）
                    try
                    {
                        bool needsRotation = CalculateRotationFromApiData(originalImageData, detectionResult, fileId, "旧版处理", "未知位置", "旧版阶段");

                        if (needsRotation)
                        {
                            picture.Rotation = -90f; // 逆时针旋转90度
                            System.Diagnostics.Debug.WriteLine($"✅ PPT图片逆时针旋转90度: {fileId}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ PPT图片无需旋转: {fileId}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ PPT旋转检查异常: {fileId}, {ex.Message}");
                    }

                    // 11. 最终调整图片尺寸和位置
                    AdjustImageSizeToFitCell(picture, cellWidth, cellHeight);
                    CenterImageInCell(picture, cell);

                    System.Diagnostics.Debug.WriteLine($"✅ 内存流图片插入+PPT处理完成: {fileId}, 最终尺寸: {picture.Width:F1}x{picture.Height:F1}");

                    // 裁剪后重新调整图片位置和尺寸到单元格中心（学习旧版本关键步骤）
                    try
                    {
                        // 调整图片尺寸以适应单元格（保持宽高比，只缩小不放大）
                        AdjustImageSizeToFitCell(picture, cellWidth, cellHeight);

                        // 计算单元格中心位置
                        float cellCenterX = cellLeft + cellWidth / 2;
                        float cellCenterY = cellTop + cellHeight / 2;

                        // 将图片中心对齐到单元格中心
                        picture.Left = cellCenterX - picture.Width / 2;
                        picture.Top = cellCenterY - picture.Height / 2;

                        System.Diagnostics.Debug.WriteLine($"裁剪后图片位置调整: ({picture.Left:F1}, {picture.Top:F1}), 尺寸: {picture.Width:F1}x{picture.Height:F1}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"调整裁剪后图片位置失败: {ex.Message}");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ 内存流图片处理失败: {fileId}, {ex.Message}");
                    throw;
                }
            }
            catch (NotImplementedException ex)
            {
                System.Diagnostics.Debug.WriteLine($"图片插入功能未实现，跳过: {ex.Message}");
                // 在单元格中显示文件ID作为替代
                try
                {
                    cell.Shape.TextFrame.TextRange.Text = $"图片ID: {fileId}";
                }
                catch
                {
                    // 如果连文本设置都失败，就忽略
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入图片失败: {ex.Message}");
                // 在单元格中显示错误信息
                try
                {
                    cell.Shape.TextFrame.TextRange.Text = "图片加载失败";
                }
                catch
                {
                    // 如果连文本设置都失败，就忽略
                }
            }
        }

        /// <summary>
        /// 对原始图片进行电池检测（修复版本）
        /// </summary>
        private async Task<BatteryDetectionApiResult> DetectBatteryInOriginalImage(byte[] originalImageData, string imageId)
        {
            try
            {


                // 获取原始图片尺寸
                var (originalWidth, originalHeight) = GetImageDimensionsFromData(originalImageData);
                System.Diagnostics.Debug.WriteLine($"原始图片尺寸: {originalWidth}x{originalHeight}");

                // 使用BatteryTableImageProcessor进行检测
                var tableProcessor = new ImageProcessing.BatteryTableImageProcessor();

                // 创建图片数据列表
                var imageDataList = new List<ApiClient.BatteryDetectionImageData>
                {
                    new ApiClient.BatteryDetectionImageData
                    {
                        ImageId = imageId,
                        FileName = $"{imageId}.jpg",
                        ImageData = originalImageData
                    }
                };

                // 调用批量检测API
                var detectionResults = await tableProcessor.DetectBatteryInMultipleImages(imageDataList);

                if (detectionResults != null && detectionResults.Count > 0)
                {
                    var detection = detectionResults[0];
                    System.Diagnostics.Debug.WriteLine($"✅ 原始图片检测成功: Left={detection.CropLeft}, Top={detection.CropTop}, Right={detection.CropRight}, Bottom={detection.CropBottom}, 置信度={detection.Confidence}");
                    return detection;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ 原始图片检测未找到结果");
                    return null;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 原始图片检测异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从API数据获取图片尺寸（优先使用API数据，避免加密系统影响）
        /// </summary>
        private (int width, int height) GetImageDimensionsFromApiData(BatteryDetectionApiResult detectionResult, byte[] imageData)
        {
            try
            {
                // 优先使用API返回的图片尺寸信息（避免加密系统影响）
                if (detectionResult != null && detectionResult.ImageWidth > 0 && detectionResult.ImageHeight > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"📐 使用API返回的图片尺寸: {detectionResult.ImageWidth}x{detectionResult.ImageHeight}");
                    return (detectionResult.ImageWidth, detectionResult.ImageHeight);
                }

                // 如果API没有返回尺寸信息，尝试从图片数据解析（可能受加密影响）
                System.Diagnostics.Debug.WriteLine($"⚠️ API未返回图片尺寸，尝试从图片数据解析");
                return GetImageDimensionsFromData(imageData);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取图片尺寸失败: {ex.Message}");
                return (800, 600); // 默认尺寸
            }
        }

        /// <summary>
        /// 获取图片的实际显示尺寸（统一处理EXIF旋转）
        /// </summary>

        /// <summary>
        /// 从图片数据获取原始图片尺寸（不考虑EXIF旋转，用于列宽计算）
        /// </summary>
        private (int width, int height) GetRawImageDimensionsFromData(byte[] imageData)
        {
            try
            {
                using (var stream = new MemoryStream(imageData))
                using (var image = System.Drawing.Image.FromStream(stream))
                {
                    // 直接返回原始尺寸，不考虑EXIF旋转
                    int originalWidth = image.Width;
                    int originalHeight = image.Height;
                    System.Diagnostics.Debug.WriteLine($"📐 原始图片尺寸: {originalWidth}x{originalHeight} (忽略EXIF)");
                    return (originalWidth, originalHeight);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ 从图片数据获取原始尺寸失败（可能受加密系统影响）: {ex.Message}");
                // 返回默认尺寸
                return (1024, 768);
            }
        }

        /// <summary>
        /// 获取压缩后的图片尺寸（用于列宽计算，与实际处理保持一致）
        /// </summary>
        private (int width, int height) GetCompressedImageDimensions(byte[] imageData, string imageId)
        {
            try
            {
                // 1. 优先使用检测结果中的API返回尺寸（这是压缩后的尺寸）
                if (_detectionResultCache.ContainsKey(imageId))
                {
                    var detectionResult = _detectionResultCache[imageId];
                    System.Diagnostics.Debug.WriteLine($"📐 检测结果缓存存在: {imageId}, detectionResult != null: {detectionResult != null}");
                    if (detectionResult != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"📐 API尺寸信息: ImageWidth={detectionResult.ImageWidth}, ImageHeight={detectionResult.ImageHeight}");
                        if (detectionResult.ImageWidth > 0 && detectionResult.ImageHeight > 0)
                        {
                            System.Diagnostics.Debug.WriteLine($"📐 使用API压缩后尺寸: {detectionResult.ImageWidth}x{detectionResult.ImageHeight}");
                            return (detectionResult.ImageWidth, detectionResult.ImageHeight);
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"📐 API尺寸无效，ImageWidth={detectionResult.ImageWidth}, ImageHeight={detectionResult.ImageHeight}");
                        }
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"📐 检测结果缓存不存在: {imageId}");
                }

                // 2. 如果没有API尺寸，模拟压缩计算
                var (originalWidth, originalHeight) = GetRawImageDimensionsFromData(imageData);

                // 模拟压缩逻辑（与CompressImageForApi保持一致）
                if (originalWidth > 1024 || originalHeight > 1024)
                {
                    float scale = Math.Min(1024f / originalWidth, 1024f / originalHeight);
                    int compressedWidth = (int)(originalWidth * scale);
                    int compressedHeight = (int)(originalHeight * scale);
                    System.Diagnostics.Debug.WriteLine($"📐 模拟压缩后尺寸: {originalWidth}x{originalHeight} -> {compressedWidth}x{compressedHeight}");
                    return (compressedWidth, compressedHeight);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"📐 图片无需压缩: {originalWidth}x{originalHeight}");
                    return (originalWidth, originalHeight);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ 获取压缩后图片尺寸失败: {ex.Message}");
                // 返回默认压缩尺寸
                return (1024, 768);
            }
        }

        /// <summary>
        /// 从图片数据获取图片尺寸（考虑EXIF旋转信息，可能受加密系统影响）
        /// </summary>
        private (int width, int height) GetImageDimensionsFromData(byte[] imageData)
        {
            try
            {
                using (var stream = new MemoryStream(imageData))
                using (var image = System.Drawing.Image.FromStream(stream))
                {
                    // System.Drawing.Image.FromStream已自动处理EXIF旋转，直接使用显示尺寸
                    System.Diagnostics.Debug.WriteLine($"📐 图片显示尺寸: {image.Width}x{image.Height}");
                    return (image.Width, image.Height);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ 从图片数据获取尺寸失败: {ex.Message}");
                return (800, 600); // 默认尺寸
            }
        }

        /// <summary>
        /// 使用内存流方式插入图片到PowerPoint（避免临时文件）
        /// </summary>
        private PowerPoint.Shape InsertImageFromMemoryStream(byte[] imageData, float left, float top, float width, float height, string imageId)
        {
            string tempFilePath = null;
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 开始内存流图片插入: {imageId}");

                // 由于PowerPoint AddPicture只支持文件路径，我们需要创建一个最小化的临时文件处理
                // 但使用更安全的方式和更快的清理
                tempFilePath = CreateSecureTempImageFile(imageData, imageId);

                // 插入图片到PowerPoint
                var picture = _slide.Shapes.AddPicture(
                    tempFilePath,
                    Microsoft.Office.Core.MsoTriState.msoFalse,
                    Microsoft.Office.Core.MsoTriState.msoTrue,
                    left, top, width, height);

                System.Diagnostics.Debug.WriteLine($"✅ 内存流图片插入成功: {imageId}");
                return picture;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 内存流图片插入失败: {imageId}, {ex.Message}");
                throw;
            }
            finally
            {
                // 立即清理临时文件
                CleanupTempImageFile(tempFilePath);
            }
        }

        /// <summary>
        /// 创建安全的临时图片文件（最小化文件系统影响）
        /// </summary>
        private string CreateSecureTempImageFile(byte[] imageData, string imageId)
        {
            try
            {
                // 使用更安全的临时文件创建方式
                string tempDir = System.IO.Path.GetTempPath();
                string fileName = $"ppt_img_{imageId}_{DateTime.Now.Ticks}.jpg";
                string tempFilePath = System.IO.Path.Combine(tempDir, fileName);

                // 直接写入文件，减少中间步骤
                System.IO.File.WriteAllBytes(tempFilePath, imageData);

                System.Diagnostics.Debug.WriteLine($"📁 创建临时图片文件: {tempFilePath} ({imageData.Length} bytes)");
                return tempFilePath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 创建临时图片文件失败: {ex.Message}");
                throw new Exception($"无法创建临时图片文件: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 清理临时图片文件
        /// </summary>
        private void CleanupTempImageFile(string tempFilePath)
        {
            if (string.IsNullOrEmpty(tempFilePath)) return;

            try
            {
                if (System.IO.File.Exists(tempFilePath))
                {
                    // 尝试多次删除，处理文件锁定问题
                    for (int i = 0; i < 3; i++)
                    {
                        try
                        {
                            System.IO.File.Delete(tempFilePath);
                            System.Diagnostics.Debug.WriteLine($"🗑️ 临时图片文件已清理: {tempFilePath}");
                            break;
                        }
                        catch (Exception)
                        {
                            if (i < 2) // 不是最后一次尝试
                            {
                                System.Threading.Thread.Sleep(50); // 等待50ms后重试
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"⚠️ 临时图片文件清理失败: {tempFilePath}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ 清理临时图片文件异常: {ex.Message}");
                // 不抛出异常，避免影响主流程
            }
        }

        /// <summary>
        /// 检查EXIF旋转信息
        /// </summary>





        /// <summary>
        /// 检查检测结果是否包含有效的裁剪数据
        /// </summary>
        private bool HasValidCropData(BatteryDetectionApiResult detectionResult)
        {
            if (detectionResult == null) return false;

            // 检查是否有有效的裁剪区域（不是全零）
            bool hasValidCrop = detectionResult.CropLeft > 0 || detectionResult.CropTop > 0 ||
                               detectionResult.CropRight > 0 || detectionResult.CropBottom > 0;

            if (!hasValidCrop)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ 检测结果无有效裁剪区域: Left={detectionResult.CropLeft}, Top={detectionResult.CropTop}, Right={detectionResult.CropRight}, Bottom={detectionResult.CropBottom}");
            }

            return hasValidCrop;
        }























        /// <summary>
        /// 简化Cell Number显示，将长字符串转换为多行显示
        /// </summary>
        private string SimplifyCellNumber(string cellNumber)
        {
            if (string.IsNullOrEmpty(cellNumber))
                return "";

            try
            {
                // 将逗号分隔的Cell Number转换为换行分隔
                var parts = cellNumber.Split(',');
                if (parts.Length <= 1)
                    return cellNumber;

                // 进一步简化每个部分，只保留关键信息
                var simplifiedParts = new List<string>();
                foreach (var part in parts)
                {
                    var trimmed = part.Trim();
                    // 提取最后的编号部分，例如从 "1-202412170004-0001" 提取 "1-0001"
                    if (trimmed.Contains("-"))
                    {
                        var segments = trimmed.Split('-');
                        if (segments.Length >= 3)
                        {
                            // 保留第一段和最后一段
                            simplifiedParts.Add($"{segments[0]}-{segments[segments.Length - 1]}");
                        }
                        else
                        {
                            simplifiedParts.Add(trimmed);
                        }
                    }
                    else
                    {
                        simplifiedParts.Add(trimmed);
                    }
                }

                // 用换行符连接，最多显示3行
                var result = string.Join("\n", simplifiedParts.Take(3));
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"简化Cell Number失败: {ex.Message}");
                return cellNumber;
            }
        }

        /// <summary>
        /// 格式化Cell Number用于显示
        /// </summary>
        private string FormatCellNumberForDisplay(string cellNumber)
        {
            if (string.IsNullOrEmpty(cellNumber))
                return "";

            try
            {
                // 如果包含逗号，说明有多个cellNumber
                if (cellNumber.Contains(","))
                {
                    var parts = cellNumber.Split(',')
                        .Select(p => p.Trim())
                        .Where(p => !string.IsNullOrEmpty(p))
                        .ToList();

                    if (parts.Count <= 3)
                    {
                        // 3个或以下，用换行符连接
                        return string.Join("\n", parts);
                    }
                    else
                    {
                        // 超过3个，显示前2个加省略号
                        return string.Join("\n", parts.Take(2)) + "\n...";
                    }
                }
                else
                {
                    // 单个cellNumber，直接返回
                    return cellNumber;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"格式化Cell Number失败: {ex.Message}");
                return cellNumber;
            }
        }



        /// <summary>
        /// 模拟处理后的图片尺寸（用于列宽计算，使用与实际处理相同的API数据计算逻辑）
        /// </summary>
        private (float Width, float Height) SimulateProcessedImageSize(byte[] imageData, string imageId, BatteryDetectionApiResult detectionResult, float availableHeight)
        {
            try
            {
                var (originalWidth, originalHeight) = GetImageDimensionsFromData(imageData);

                // 使用与实际处理相同的计算逻辑
                int finalWidth = originalWidth;
                int finalHeight = originalHeight;

                // 基于API返回的尺寸和百分比数据计算裁剪后尺寸
                if (detectionResult != null && HasValidCropData(detectionResult))
                {
                    // 使用API返回的尺寸信息
                    int apiImageWidth = detectionResult.ImageWidth;
                    int apiImageHeight = detectionResult.ImageHeight;

                    float cropLeftPercent = detectionResult.CropLeftPercent;
                    float cropTopPercent = detectionResult.CropTopPercent;
                    float cropRightPercent = detectionResult.CropRightPercent;
                    float cropBottomPercent = detectionResult.CropBottomPercent;

                    float croppedWidthF = apiImageWidth * (1.0f - cropLeftPercent - cropRightPercent);
                    float croppedHeightF = apiImageHeight * (1.0f - cropTopPercent - cropBottomPercent);

                    finalWidth = Math.Max(1, (int)Math.Round(croppedWidthF));
                    finalHeight = Math.Max(1, (int)Math.Round(croppedHeightF));
                }

                // 使用与实际处理相同的旋转判断逻辑
                bool wouldRotate = CalculateRotationFromApiData(imageData, detectionResult, $"{imageId}_simulate", "模拟处理", "未知位置", "模拟阶段");
                if (wouldRotate)
                {
                    // 交换宽高
                    (finalWidth, finalHeight) = (finalHeight, finalWidth);
                }

                // 计算显示尺寸（优先高度策略，列宽计算时不受宽度限制）
                float targetHeight = 2.1f * 28.35f; // 2.1cm转换为点数

                float aspectRatio = (float)finalWidth / finalHeight;
                // 优先让高度达到2.1cm，列宽计算时不限制宽度
                float displayHeight = targetHeight;
                float displayWidth = displayHeight * aspectRatio;

                System.Diagnostics.Debug.WriteLine($"📐 模拟处理尺寸 {imageId}: 最终宽高比{aspectRatio:F2}, 显示尺寸{displayWidth:F1}x{displayHeight:F1}");

                string cropInfo = "";
                if (detectionResult != null && HasValidCropData(detectionResult))
                {
                    int apiImageWidth = detectionResult.ImageWidth;
                    int apiImageHeight = detectionResult.ImageHeight;

                    float cropLeftPercent = detectionResult.CropLeftPercent;
                    float cropRightPercent = detectionResult.CropRightPercent;
                    float cropTopPercent = detectionResult.CropTopPercent;
                    float cropBottomPercent = detectionResult.CropBottomPercent;

                    int beforeCropWidth = (int)Math.Round(apiImageWidth * (1.0f - cropLeftPercent - cropRightPercent));
                    int beforeCropHeight = (int)Math.Round(apiImageHeight * (1.0f - cropTopPercent - cropBottomPercent));

                    cropInfo = $"API尺寸{apiImageWidth}x{apiImageHeight} -> 裁剪后{beforeCropWidth}x{beforeCropHeight} -> ";
                }

                System.Diagnostics.Debug.WriteLine($"🔍 模拟处理: {imageId}, 原图{originalWidth}x{originalHeight} -> {cropInfo}旋转后{finalWidth}x{finalHeight} -> 显示{displayWidth:F1}x{displayHeight:F1}");

                return (displayWidth, displayHeight);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 模拟处理失败: {imageId}, {ex.Message}");
                return (100f, availableHeight * 0.85f);
            }
        }



        /// <summary>
        /// 基于API返回的width_percent和height_percent计算是否需要旋转
        /// </summary>
        private bool CalculateRotationFromApiData(byte[] originalImageData, BatteryDetectionApiResult detectionResult, string imageId, string cellNumber = "", string position = "", string stage = "")
        {
            try
            {
                // 构建上下文信息
                string contextInfo = "";
                if (!string.IsNullOrEmpty(cellNumber) && !string.IsNullOrEmpty(position) && !string.IsNullOrEmpty(stage))
                {
                    contextInfo = $" (电芯{cellNumber}-{position}-{stage})";
                }

                System.Diagnostics.Debug.WriteLine($"🔍 开始基于API数据计算旋转: {imageId}{contextInfo}");

                // 如果没有有效的检测结果，不进行旋转
                if (detectionResult == null)
                {
                    System.Diagnostics.Debug.WriteLine($"   无API检测结果，不进行旋转");
                    return false;
                }

                // 获取API返回的百分比信息
                float widthPercent = detectionResult.WidthPercent;
                float heightPercent = detectionResult.HeightPercent;
                int apiImageWidth = detectionResult.ImageWidth;
                int apiImageHeight = detectionResult.ImageHeight;

                // 检查API数据是否有效
                if (widthPercent <= 0 || heightPercent <= 0 || apiImageWidth <= 0 || apiImageHeight <= 0)
                {
                    System.Diagnostics.Debug.WriteLine($"   ⚠️ API数据无效，不进行旋转:");
                    System.Diagnostics.Debug.WriteLine($"   ⚠️ width_percent={widthPercent:F6}, height_percent={heightPercent:F6}");
                    System.Diagnostics.Debug.WriteLine($"   ⚠️ API图片尺寸: {apiImageWidth}x{apiImageHeight}");
                    return false;
                }

                // 🎯 第一步：基于电池区域的宽高比判断是否需要旋转
                // 目标：让电池区域变成横向（宽 > 高），更好利用表格行高
                float batteryPixelWidth = widthPercent * apiImageWidth;
                float batteryPixelHeight = heightPercent * apiImageHeight;
                float batteryAspectRatio = batteryPixelWidth / batteryPixelHeight;

                // 修复：如果电池区域是竖向的（高 > 宽，宽高比 < 1），就旋转90度让其变横向
                bool needsApiRotation = batteryAspectRatio < 1.0f;
                System.Diagnostics.Debug.WriteLine($"   🎯 检测策略: 直接基于API电池区域宽高比判断");

                // 简化策略：直接基于API判断，System.Drawing已自动处理EXIF旋转
                System.Diagnostics.Debug.WriteLine($"   🎯 基于电池区域宽高比判断旋转{contextInfo}:");
                System.Diagnostics.Debug.WriteLine($"   📊 API图片尺寸: {apiImageWidth}x{apiImageHeight}");
                System.Diagnostics.Debug.WriteLine($"   📊 电池区域像素尺寸: width={batteryPixelWidth:F1}, height={batteryPixelHeight:F1} (API检测: {(batteryAspectRatio >= 1.0f ? "横向" : "竖向")})");
                System.Diagnostics.Debug.WriteLine($"   📊 电池区域宽高比: {batteryAspectRatio:F2} (宽高比 < 1.0 时为竖向，需要旋转)");
                System.Diagnostics.Debug.WriteLine($"   🎯 旋转策略: 竖向电池区域旋转为横向，更好利用表格高度");
                System.Diagnostics.Debug.WriteLine($"   ✅ 最终旋转判断: {needsApiRotation} (基于电池区域宽高比)");

                return needsApiRotation;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 基于API数据计算旋转失败: {imageId}, {ex.Message}，不进行旋转");
                return false;
            }
        }



        /// <summary>
        /// 将图片居中到单元格中
        /// </summary>
        private void CenterImageInCell(PowerPoint.Shape picture, PowerPoint.Cell cell)
        {
            try
            {
                float cellLeft = cell.Shape.Left;
                float cellTop = cell.Shape.Top;
                float cellWidth = cell.Shape.Width;
                float cellHeight = cell.Shape.Height;

                float pictureWidth = picture.Width;
                float pictureHeight = picture.Height;

                // 计算居中位置
                float centeredLeft = cellLeft + (cellWidth - pictureWidth) / 2;
                float centeredTop = cellTop + (cellHeight - pictureHeight) / 2;

                // 设置图片位置
                picture.Left = centeredLeft;
                picture.Top = centeredTop;

                System.Diagnostics.Debug.WriteLine($"裁剪后图片位置调整: ({centeredLeft:F1}, {centeredTop:F1}), 尺寸: {pictureWidth:F1}x{pictureHeight:F1}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"图片居中失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 调整图片尺寸以适应单元格，确保图片高度固定为2.1cm且宽度大于高度
        /// </summary>
        private void AdjustImageSizeToFitCell(PowerPoint.Shape shape, float cellWidth, float cellHeight)
        {
            try
            {
                // 目标高度：固定为2.1cm，确保图片高度一致
                float targetHeight = 2.1f * 28.35f; // 2.1cm = 59.535点

                // 获取当前图片尺寸和旋转状态
                float currentWidth = shape.Width;
                float currentHeight = shape.Height;
                float rotation = shape.Rotation;

                // 检查图片是否旋转了90度或270度（实际显示时宽高交换）
                bool isRotated90or270 = (Math.Abs(rotation % 180) > 45);

                // 计算实际显示的宽高（考虑旋转）
                float displayWidth = isRotated90or270 ? currentHeight : currentWidth;
                float displayHeight = isRotated90or270 ? currentWidth : currentHeight;
                float aspectRatio = displayWidth / displayHeight;

                System.Diagnostics.Debug.WriteLine($"调整前图片尺寸: {currentWidth:F1}x{currentHeight:F1}, 旋转: {rotation:F1}°");
                System.Diagnostics.Debug.WriteLine($"实际显示尺寸: {displayWidth:F1}x{displayHeight:F1}, 宽高比: {aspectRatio:F2}");
                System.Diagnostics.Debug.WriteLine($"单元格尺寸: {cellWidth:F1}x{cellHeight:F1}");
                System.Diagnostics.Debug.WriteLine($"🎯 目标高度: {targetHeight:F1}点 = {targetHeight/28.35f:F2}cm");

                // 优先策略：确保显示高度精确为2.1cm，根据实际显示宽高比计算对应宽度
                float newDisplayHeight = targetHeight; // 2.1cm = 59.535点
                float newDisplayWidth = newDisplayHeight * aspectRatio;

                // 根据旋转状态计算PowerPoint属性值
                // 关键修复：旋转90/270时，PowerPoint的Width控制显示高度，Height控制显示宽度
                float newWidth = isRotated90or270 ? newDisplayHeight : newDisplayWidth;
                float newHeight = isRotated90or270 ? newDisplayWidth : newDisplayHeight;

                // 完全信任列宽计算的结果，不再进行宽度限制检查
                // 列宽已经根据图片实际需求进行了精确计算
                System.Diagnostics.Debug.WriteLine($"✅ 完全信任列宽计算，图片铺满可用高度: {newWidth:F1}x{newHeight:F1}");
                System.Diagnostics.Debug.WriteLine($"📊 实际显示尺寸: {newDisplayWidth:F1}x{newDisplayHeight:F1} (旋转{(isRotated90or270 ? "90/270" : "0/180")}度)");
                System.Diagnostics.Debug.WriteLine($"📊 单元格尺寸: {cellWidth:F1}x{cellHeight:F1}, 可用高度: {targetHeight:F1}");
                System.Diagnostics.Debug.WriteLine($"📊 高度利用率: {(newDisplayHeight/targetHeight)*100:F1}%");

                // 🎯 核心修复：无论如何都要保证2.1cm高度，不再限制放大倍数
                // 因为列宽已经根据2.1cm高度精确计算过，所需的放大是合理的
                float scaleX = newWidth / currentWidth;
                float scaleY = newHeight / currentHeight;
                float maxScale = Math.Max(scaleX, scaleY);

                if (newWidth > currentWidth || newHeight > currentHeight)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ 放大以达到精确2.1cm高度: {currentWidth:F1}x{currentHeight:F1} -> {newWidth:F1}x{newHeight:F1} (放大{maxScale:F2}倍)");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"✅ 缩小以达到精确2.1cm高度: {currentWidth:F1}x{currentHeight:F1} -> {newWidth:F1}x{newHeight:F1} (缩小{maxScale:F2}倍)");
                }

                // 应用新尺寸
                shape.Width = newWidth;
                shape.Height = newHeight;

                // 验证最终效果 - 修复旋转后的验证逻辑
                // 重新计算最终的实际显示尺寸（考虑可能的旋转变化）
                float finalRotation = shape.Rotation;
                bool isFinalRotated90or270 = (Math.Abs(finalRotation % 180 - 90) < 45);
                float finalDisplayWidth = isFinalRotated90or270 ? newHeight : newWidth;
                float finalDisplayHeight = isFinalRotated90or270 ? newWidth : newHeight;

                bool finalDisplayWidthGreaterThanHeight = finalDisplayWidth > finalDisplayHeight;
                bool heightExactly2_1cm = Math.Abs(finalDisplayHeight - targetHeight) < 0.1f;

                System.Diagnostics.Debug.WriteLine($"✅ 图片尺寸调整完成: {currentWidth:F1}x{currentHeight:F1} -> {newWidth:F1}x{newHeight:F1}");
                System.Diagnostics.Debug.WriteLine($"📐 实际显示效果: {displayWidth:F1}x{displayHeight:F1} -> {finalDisplayWidth:F1}x{finalDisplayHeight:F1}");
                System.Diagnostics.Debug.WriteLine($"🎯 显示高度: {finalDisplayHeight:F1}点 = {finalDisplayHeight/28.35f:F2}cm (目标2.1cm: {heightExactly2_1cm})");
                System.Diagnostics.Debug.WriteLine($"✅ 宽度>高度: {finalDisplayWidthGreaterThanHeight} (宽{finalDisplayWidth:F1} vs 高{finalDisplayHeight:F1})");

                if (!heightExactly2_1cm)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ 警告：显示高度偏离2.1cm目标 {Math.Abs(finalDisplayHeight - targetHeight):F1}点");
                }
                if (!finalDisplayWidthGreaterThanHeight)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ 警告：最终显示宽度不大于高度！可能需要检查旋转逻辑");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"调整图片尺寸失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 计算保持宽高比的图片尺寸，优先让高度达到2.1cm目标
        /// </summary>
        private (float width, float height) CalculateImageSizeWithAspectRatio(byte[] imageData, float maxWidth, float maxHeight)
        {
            try
            {
                // 使用修复的方法获取图片尺寸（已处理EXIF）
                var (originalWidth, originalHeight) = GetImageDimensionsFromData(imageData);
                float aspectRatio = (float)originalWidth / originalHeight;

                System.Diagnostics.Debug.WriteLine($"原始图片尺寸: {originalWidth}x{originalHeight}, 宽高比: {aspectRatio:F2}");

                // 优先策略：使用传入的最大高度（已考虑边距），铺满可用空间
                float preferredHeight = maxHeight; // 直接使用传入的最大高度

                float finalWidth = preferredHeight * aspectRatio;
                float finalHeight = preferredHeight;

                // 检查宽度是否超出限制（当maxWidth为MaxValue时表示无宽度限制）
                if (maxWidth != float.MaxValue && finalWidth > maxWidth)
                {
                    // 如果宽度超出，则以宽度为限制，等比例调整
                    finalWidth = maxWidth;
                    finalHeight = maxWidth / aspectRatio;
                    System.Diagnostics.Debug.WriteLine($"宽度受限调整: 以宽度{maxWidth:F1}为准，高度调整为{finalHeight:F1}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"优先高度策略: 高度{finalHeight:F1}，对应宽度{finalWidth:F1}");
                }

                System.Diagnostics.Debug.WriteLine($"计算后尺寸: {finalWidth:F1}x{finalHeight:F1} (最大: {maxWidth:F1}x{maxHeight:F1})");
                return (finalWidth, finalHeight);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"计算图片尺寸失败: {ex.Message}，使用默认尺寸");
                // 默认也优先使用2.1cm高度
                float defaultHeight = Math.Min(maxHeight, 2.1f * 28.35f);
                float defaultWidth = Math.Min(maxWidth, defaultHeight * 1.33f); // 假设4:3宽高比
                return (defaultWidth, defaultHeight);
            }
        }

        /// <summary>
        /// 压缩图片用于API处理（学习旧版本）
        /// </summary>
        private byte[] CompressImageForApi(byte[] originalImageData)
        {
            try
            {
                using (var ms = new MemoryStream(originalImageData))
                using (var image = System.Drawing.Image.FromStream(ms))
                {
                    int originalWidth = image.Width;
                    int originalHeight = image.Height;

                    // 如果图片尺寸或大小超过阈值，进行压缩
                    if (originalWidth > 1024 || originalHeight > 1024 || originalImageData.Length > 1024 * 1024)
                    {
                        // 计算压缩后的尺寸，保持宽高比
                        float scale = Math.Min(1024f / originalWidth, 1024f / originalHeight);
                        int newWidth = (int)(originalWidth * scale);
                        int newHeight = (int)(originalHeight * scale);

                        using (var compressedImage = new System.Drawing.Bitmap(newWidth, newHeight))
                        using (var graphics = System.Drawing.Graphics.FromImage(compressedImage))
                        {
                            graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                            graphics.DrawImage(image, 0, 0, newWidth, newHeight);

                            using (var compressedMs = new MemoryStream())
                            {
                                compressedImage.Save(compressedMs, System.Drawing.Imaging.ImageFormat.Jpeg);
                                return compressedMs.ToArray();
                            }
                        }
                    }
                    else
                    {
                        // 图片已经足够小，直接返回
                        return originalImageData;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"压缩图片失败: {ex.Message}，使用原图");
                return originalImageData;
            }
        }

        /// <summary>
        /// 计算所有列的最优宽度（使用简化旋转估算，避免重复API请求）
        /// </summary>
        private async Task<Dictionary<int, float>> CalculateOptimalColumnWidths(PptTableData tableData, float availableTableWidth, float fixedRowHeight)
        {
            var columnWidths = new Dictionary<int, float>();

            try
            {
                System.Diagnostics.Debug.WriteLine("开始计算列宽自适应");

                // 1. 固定列宽度
                columnWidths[1] = 80f; // Cell No列
                columnWidths[2] = 60f; // SOC列

                // 计算总列数
                int beforeCols = tableData.TestStages.Contains("before") ? tableData.PhotoPositions.Count : 0;
                int afterCols = tableData.TestStages.Contains("after") ? tableData.PhotoPositions.Count : 0;
                int totalCols = 2 + beforeCols + afterCols + 1; // Cell No. + SOC + 照片列 + Test Result

                columnWidths[totalCols] = 80f; // Test Result列

                // 2. 计算图片列的最优宽度（考虑旋转）
                var photoColumnWidths = await CalculatePhotoColumnWidths(tableData, fixedRowHeight);

                // 3. 智能分配剩余宽度
                float fixedColumnsWidth = 80f + 60f + 80f; // Cell No + SOC + Test Result
                float remainingWidth = availableTableWidth - fixedColumnsWidth;

                // 计算所有图片列的总需求宽度
                float totalRequiredWidth = 0f;
                var photoColumnRequirements = new Dictionary<int, float>();
                int colIndex = 3; // 从第3列开始是图片列

                // 测试前照片列
                if (tableData.TestStages.Contains("before"))
                {
                    foreach (var position in tableData.PhotoPositions)
                    {
                        string beforeKey = $"before_{position}";
                        float optimalWidth = photoColumnWidths.ContainsKey(beforeKey) ? photoColumnWidths[beforeKey] : 100f;
                        photoColumnRequirements[colIndex] = optimalWidth;
                        totalRequiredWidth += optimalWidth;
                        System.Diagnostics.Debug.WriteLine($"Before列{colIndex}({position}): 使用宽度{optimalWidth:F1}");
                        colIndex++;
                    }
                }

                // 测试后照片列
                if (tableData.TestStages.Contains("after"))
                {
                    foreach (var position in tableData.PhotoPositions)
                    {
                        string afterKey = $"after_{position}";
                        float optimalWidth = photoColumnWidths.ContainsKey(afterKey) ? photoColumnWidths[afterKey] : 100f;
                        photoColumnRequirements[colIndex] = optimalWidth;
                        totalRequiredWidth += optimalWidth;
                        System.Diagnostics.Debug.WriteLine($"After列{colIndex}({position}): 使用宽度{optimalWidth:F1}");
                        colIndex++;
                    }
                }

                // 4. 完全根据图片所需宽度设置列宽，不受表格总宽度限制
                System.Diagnostics.Debug.WriteLine("📏 采用完全基于图片需求的列宽设置策略");

                foreach (var kvp in photoColumnRequirements)
                {
                    columnWidths[kvp.Key] = kvp.Value; // 完全使用图片所需的宽度
                    System.Diagnostics.Debug.WriteLine($"列{kvp.Key}设置为图片所需宽度: {kvp.Value:F1}点");
                }

                // 计算实际表格宽度
                float actualTotalWidth = columnWidths.Values.Sum();
                float overwidthRatio = actualTotalWidth / availableTableWidth;

                if (overwidthRatio > 1.0f)
                {
                    System.Diagnostics.Debug.WriteLine($"📊 表格宽度: {actualTotalWidth:F1}点，超出可用宽度{availableTableWidth:F1}点 ({overwidthRatio:F1}倍)");
                    System.Diagnostics.Debug.WriteLine($"✅ 优先保证图片2.1cm高度，表格将自动调整宽度");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"📊 表格宽度: {actualTotalWidth:F1}点，在可用宽度{availableTableWidth:F1}点范围内");
                }

                // 5. 不再进行边界调整，完全信任图片所需宽度
                System.Diagnostics.Debug.WriteLine("📏 跳过边界调整，完全使用图片所需列宽");

                // 6. 验证和调试信息
                System.Diagnostics.Debug.WriteLine($"列宽计算完成:");
                System.Diagnostics.Debug.WriteLine($"  - 总列数: {totalCols}");
                System.Diagnostics.Debug.WriteLine($"  - 可用宽度: {availableTableWidth:F1}");
                System.Diagnostics.Debug.WriteLine($"  - 实际总宽度: {actualTotalWidth:F1}");
                System.Diagnostics.Debug.WriteLine($"  - 固定列宽度: {fixedColumnsWidth:F1}");
                System.Diagnostics.Debug.WriteLine($"  - 图片列总需求: {totalRequiredWidth:F1}");

                foreach (var kvp in columnWidths)
                {
                    System.Diagnostics.Debug.WriteLine($"  - 列{kvp.Key}: {kvp.Value:F1}");
                }

                return columnWidths;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"列宽计算失败: {ex.Message}");
                // 返回默认列宽
                return GetDefaultColumnWidths(tableData);
            }
        }

        /// <summary>
        /// 从电芯行数据中获取指定位置的图片ID列表
        /// </summary>
        private List<string> GetImageIdsForPosition(BatteryRowData batteryRow, string position)
        {
            var imageIds = new List<string>();
            
            try
            {
                // 从测试前照片中获取
                if (batteryRow.BeforeTestPhotos != null && batteryRow.BeforeTestPhotos.ContainsKey(position))
                {
                    var beforePhoto = batteryRow.BeforeTestPhotos[position];
                    if (!string.IsNullOrEmpty(beforePhoto))
                    {
                        imageIds.Add(beforePhoto);
                    }
                }
                
                // 从测试后照片中获取
                if (batteryRow.AfterTestPhotos != null && batteryRow.AfterTestPhotos.ContainsKey(position))
                {
                    var afterPhoto = batteryRow.AfterTestPhotos[position];
                    if (!string.IsNullOrEmpty(afterPhoto))
                    {
                        imageIds.Add(afterPhoto);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取位置 {position} 的图片ID失败: {ex.Message}");
            }
            
            return imageIds;
        }

        /// <summary>
        /// 预处理所有图片，获取准确的旋转判断和尺寸信息
        /// </summary>
        private async Task PreprocessAllImages(PptTableData tableData)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始预处理所有图片数据");

                var allImageIds = new HashSet<string>();

                // 收集所有图片ID
                foreach (var batteryRow in tableData.BatteryRows)
                {
                    foreach (var position in tableData.PhotoPositions)
                    {
                        var imageIds = GetImageIdsForPosition(batteryRow, position);
                        foreach (var imageId in imageIds)
                        {
                            if (!string.IsNullOrEmpty(imageId))
                            {
                                allImageIds.Add(imageId);
                            }
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"需要预处理的图片数量: {allImageIds.Count}");

                // 并行下载所有图片数据
                var downloadTasks = allImageIds.Select(async imageId =>
                {
                    try
                    {
                        if (!_imageDataCache.ContainsKey(imageId))
                        {
                            var imageData = await _imageApiClient.DownloadImageDataByFileIdAsync(imageId);
                            if (imageData != null && imageData.Length > 0)
                            {
                                _imageDataCache[imageId] = imageData;
                                System.Diagnostics.Debug.WriteLine($"📥 预处理下载: {imageId}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ 预处理下载失败: {imageId}, {ex.Message}");
                    }
                });

                await Task.WhenAll(downloadTasks);

                // 并行进行电池检测
                var detectionTasks = allImageIds.Where(id => _imageDataCache.ContainsKey(id)).Select(async imageId =>
                {
                    try
                    {
                        if (!_detectionResultCache.ContainsKey(imageId))
                        {
                            var imageData = _imageDataCache[imageId];
                            var detectionResult = await DetectBatteryInOriginalImage(imageData, imageId);
                            if (detectionResult != null)
                            {
                                _detectionResultCache[imageId] = detectionResult;
                                System.Diagnostics.Debug.WriteLine($"🔍 预处理检测: {imageId}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ 预处理检测失败: {imageId}, {ex.Message}");
                    }
                });

                await Task.WhenAll(detectionTasks);

                System.Diagnostics.Debug.WriteLine($"预处理完成: 图片数据{_imageDataCache.Count}项, 检测结果{_detectionResultCache.Count}项");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"预处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 基于实际图片处理结果计算精确列宽
        /// </summary>
        private Dictionary<int, float> CalculatePreciseColumnWidths(PowerPoint.Table table, PptTableData tableData, float availableTableWidth, float fixedRowHeight)
        {
            var columnWidths = new Dictionary<int, float>();

            try
            {
                System.Diagnostics.Debug.WriteLine("开始基于实际图片尺寸计算精确列宽");

                // 1. 固定列宽度
                columnWidths[1] = 80f; // Cell No列
                columnWidths[2] = 60f; // SOC列

                // 计算总列数
                int beforeCols = tableData.TestStages.Contains("before") ? tableData.PhotoPositions.Count : 0;
                int afterCols = tableData.TestStages.Contains("after") ? tableData.PhotoPositions.Count : 0;
                int totalCols = 2 + beforeCols + afterCols + 1; // Cell No. + SOC + 照片列 + Test Result

                columnWidths[totalCols] = 80f; // Test Result列

                // 2. 基于实际图片尺寸计算图片列宽度
                var photoColumnWidths = CalculateActualPhotoColumnWidths(table, tableData);

                // 3. 分配图片列宽度
                int colIndex = 3; // 从第3列开始是图片列

                // Before Test 列
                if (tableData.TestStages.Contains("before"))
                {
                    foreach (var position in tableData.PhotoPositions)
                    {
                        string key = $"before_{position}";
                        if (photoColumnWidths.ContainsKey(key))
                        {
                            columnWidths[colIndex] = photoColumnWidths[key];
                        }
                        else
                        {
                            columnWidths[colIndex] = 80f; // 默认宽度
                        }
                        colIndex++;
                    }
                }

                // After Test 列
                if (tableData.TestStages.Contains("after"))
                {
                    foreach (var position in tableData.PhotoPositions)
                    {
                        string key = $"after_{position}";
                        if (photoColumnWidths.ContainsKey(key))
                        {
                            columnWidths[colIndex] = photoColumnWidths[key];
                        }
                        else
                        {
                            columnWidths[colIndex] = 80f; // 默认宽度
                        }
                        colIndex++;
                    }
                }

                // 4. 验证列宽设置
                float totalRequiredWidth = columnWidths.Values.Sum();
                float fixedColumnsWidth = 80f + 60f + 80f; // Cell No + SOC + Test Result
                float photoColumnsWidth = totalRequiredWidth - fixedColumnsWidth;

                System.Diagnostics.Debug.WriteLine($"📏 精确列宽计算: 基于实际图片尺寸分析");
                System.Diagnostics.Debug.WriteLine($"📊 固定列宽度: {fixedColumnsWidth:F1}点");
                System.Diagnostics.Debug.WriteLine($"📊 图片列总宽度: {photoColumnsWidth:F1}点");
                System.Diagnostics.Debug.WriteLine($"📊 表格总宽度: {totalRequiredWidth:F1}点");
                System.Diagnostics.Debug.WriteLine($"📊 可用表格宽度: {availableTableWidth:F1}点");
                
                if (totalRequiredWidth > availableTableWidth)
                {
                    float overflowRatio = totalRequiredWidth / availableTableWidth;
                    System.Diagnostics.Debug.WriteLine($"⚠️ 表格宽度超出限制: {overflowRatio:P1}，可能需要调整图片尺寸或页面布局");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"✅ 表格宽度在合理范围内");
                }

                // 5. 输出每列详细信息
                System.Diagnostics.Debug.WriteLine($"详细列宽分配:");
                foreach (var kvp in columnWidths.OrderBy(x => x.Key))
                {
                    string columnName = GetColumnName(kvp.Key, tableData);
                    System.Diagnostics.Debug.WriteLine($"  列{kvp.Key}({columnName}): {kvp.Value:F1}点");
                }

                return columnWidths;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"精确列宽计算失败: {ex.Message}");
                // 返回当前列宽
                var currentWidths = new Dictionary<int, float>();
                for (int i = 1; i <= table.Columns.Count; i++)
                {
                    currentWidths[i] = table.Columns[i].Width;
                }
                return currentWidths;
            }
        }

        /// <summary>
        /// 基于预处理的图片数据计算各列所需宽度（使用2.1cm标准化高度）
        /// </summary>
        private Dictionary<string, float> CalculateActualPhotoColumnWidths(PowerPoint.Table table, PptTableData tableData)
        {
            var photoColumnWidths = new Dictionary<string, float>();

            try
            {
                System.Diagnostics.Debug.WriteLine("开始基于预处理图片数据计算标准化列宽");

                // 初始化各位置的最小宽度
                foreach (var stage in tableData.TestStages)
                {
                    foreach (var position in tableData.PhotoPositions)
                    {
                        string key = $"{stage}_{position}";
                        photoColumnWidths[key] = 60f; // 统一最小宽度
                    }
                }

                // 使用预处理的图片数据计算标准化列宽
                foreach (var batteryRow in tableData.BatteryRows)
                {
                    foreach (var position in tableData.PhotoPositions)
                    {
                        var imageIds = GetImageIdsForPosition(batteryRow, position);
                        
                        foreach (var imageId in imageIds)
                        {
                            if (!string.IsNullOrEmpty(imageId) && _imageDataCache.ContainsKey(imageId))
                            {
                                try
                                {
                                    var imageData = _imageDataCache[imageId];
                                    
                                    // 从byte[]数据中获取图片尺寸（System.Drawing已自动处理EXIF旋转）
                                    var imageSize = GetImageSizeFromBytes(imageData);
                                    if (imageSize.Width == 0 || imageSize.Height == 0)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"无法获取图片{imageId}的尺寸信息");
                                        continue;
                                    }
                                    
                                    // 使用显示尺寸计算2.1cm高度对应的标准化宽度
                                    float displayWidth = imageSize.Width;
                                    float displayHeight = imageSize.Height;
                                    
                                    // 标准高度2.1cm = 59.535点
                                    float targetHeight = 2.1f * 28.35f;
                                    float standardizedWidth = displayWidth * (targetHeight / displayHeight);
                                    
                                    // 添加必要边距
                                    standardizedWidth += 8f; // 左右边距
                                    
                                    // 更新对应测试阶段的列宽
                                    if (batteryRow.BeforeTestPhotos.ContainsValue(imageId))
                                    {
                                        string beforeKey = $"before_{position}";
                                        photoColumnWidths[beforeKey] = Math.Max(photoColumnWidths[beforeKey], standardizedWidth);
                                        System.Diagnostics.Debug.WriteLine($"Before {position}: 显示尺寸{displayWidth:F1}x{displayHeight:F1} -> 标准化宽度{standardizedWidth:F1}");
                                    }
                                    
                                    if (batteryRow.AfterTestPhotos.ContainsValue(imageId))
                                    {
                                        string afterKey = $"after_{position}";
                                        photoColumnWidths[afterKey] = Math.Max(photoColumnWidths[afterKey], standardizedWidth);
                                        System.Diagnostics.Debug.WriteLine($"After {position}: 显示尺寸{displayWidth:F1}x{displayHeight:F1} -> 标准化宽度{standardizedWidth:F1}");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Debug.WriteLine($"计算图片{imageId}标准化宽度失败: {ex.Message}");
                                }
                            }
                        }
                    }
                }

                // 输出最终结果
                System.Diagnostics.Debug.WriteLine("=== 标准化列宽计算结果 ===");
                foreach (var kvp in photoColumnWidths)
                {
                    System.Diagnostics.Debug.WriteLine($"标准化列宽 - {kvp.Key}: {kvp.Value:F1}点");
                }

                return photoColumnWidths;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"标准化列宽计算失败: {ex.Message}");

                // 尝试从可用的图片数据中计算平均比例作为默认值
                var defaultWidths = new Dictionary<string, float>();
                float averageAspectRatio = CalculateAverageAspectRatio(tableData);
                
                foreach (var stage in tableData.TestStages)
                {
                    foreach (var position in tableData.PhotoPositions)
                    {
                        string key = $"{stage}_{position}";
                        // 基于2.1cm高度和计算得出的平均宽高比
                        float targetHeight = 2.1f * 28.35f; // 59.535点
                        float defaultWidth = targetHeight * averageAspectRatio + 8f; // 实际比例 + 边距
                        defaultWidths[key] = defaultWidth;
                        System.Diagnostics.Debug.WriteLine($"使用智能默认宽度 {key}: {defaultWidth:F1}点 (宽高比: {averageAspectRatio:F2})");
                    }
                }
                return defaultWidths;
            }
        }

        /// <summary>
        /// 根据列索引获取对应的位置键
        /// </summary>
        private string GetColumnKeyByIndex(int columnIndex, PptTableData tableData)
        {
            try
            {
                int colIndex = 3; // 从第3列开始是图片列

                // Before Test 列
                if (tableData.TestStages.Contains("before"))
                {
                    foreach (var position in tableData.PhotoPositions)
                    {
                        if (colIndex == columnIndex)
                        {
                            return $"before_{position}";
                        }
                        colIndex++;
                    }
                }

                // After Test 列
                if (tableData.TestStages.Contains("after"))
                {
                    foreach (var position in tableData.PhotoPositions)
                    {
                        if (colIndex == columnIndex)
                        {
                            return $"after_{position}";
                        }
                        colIndex++;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取列键失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从可用的图片数据中计算平均宽高比
        /// </summary>
        private float CalculateAverageAspectRatio(PptTableData tableData)
        {
            var aspectRatios = new List<float>();
            
            try
            {
                // 遍历所有图片数据，计算宽高比
                foreach (var batteryRow in tableData.BatteryRows)
                {
                    foreach (var position in tableData.PhotoPositions)
                    {
                        var imageIds = GetImageIdsForPosition(batteryRow, position);
                        
                        foreach (var imageId in imageIds)
                        {
                            if (!string.IsNullOrEmpty(imageId) && _imageDataCache.ContainsKey(imageId))
                            {
                                try
                                {
                                    var imageData = _imageDataCache[imageId];
                                    var imageSize = GetImageSizeFromBytes(imageData);
                                    
                                    if (imageSize.Width > 0 && imageSize.Height > 0)
                                    {
                                        float aspectRatio = (float)imageSize.Width / imageSize.Height;
                                        aspectRatios.Add(aspectRatio);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Debug.WriteLine($"计算图片{imageId}宽高比失败: {ex.Message}");
                                }
                            }
                        }
                    }
                }
                
                if (aspectRatios.Count > 0)
                {
                    float averageRatio = aspectRatios.Average();
                    System.Diagnostics.Debug.WriteLine($"计算得出平均宽高比: {averageRatio:F2} (基于{aspectRatios.Count}张图片)");
                    return averageRatio;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"计算平均宽高比失败: {ex.Message}");
            }
            
            // 如果无法计算，使用合理的默认值（接近4:3但稍宽一些）
            System.Diagnostics.Debug.WriteLine("无法计算平均宽高比，使用默认值1.4");
            return 1.4f;
        }

        /// <summary>
        /// 从字节数组中获取图片尺寸
        /// </summary>
        private System.Drawing.Size GetImageSizeFromBytes(byte[] imageBytes)
        {
            try
            {
                if (imageBytes == null || imageBytes.Length == 0)
                {
                    return new System.Drawing.Size(0, 0);
                }

                using (var ms = new System.IO.MemoryStream(imageBytes))
                {
                    using (var image = System.Drawing.Image.FromStream(ms))
                    {
                        return new System.Drawing.Size(image.Width, image.Height);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析图片尺寸失败: {ex.Message}");
                return new System.Drawing.Size(0, 0);
            }
        }

        /// <summary>
        /// 根据列索引获取列名称（用于调试输出）
        /// </summary>
        private string GetColumnName(int columnIndex, PptTableData tableData)
        {
            try
            {
                if (columnIndex == 1) return "Cell No.";
                if (columnIndex == 2) return "SOC";
                
                int totalCols = 2 + 
                    (tableData.TestStages.Contains("before") ? tableData.PhotoPositions.Count : 0) +
                    (tableData.TestStages.Contains("after") ? tableData.PhotoPositions.Count : 0) + 1;
                
                if (columnIndex == totalCols) return "Test Result";

                // 获取图片列名称
                string key = GetColumnKeyByIndex(columnIndex, tableData);
                if (!string.IsNullOrEmpty(key))
                {
                    var parts = key.Split('_');
                    if (parts.Length == 2)
                    {
                        string stage = parts[0] == "before" ? "Before" : "After";
                        return $"{stage} {parts[1]}";
                    }
                }

                return $"Column{columnIndex}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取列名称失败: {ex.Message}");
                return $"Column{columnIndex}";
            }
        }

        /// <summary>
        /// 计算图片列的最优宽度（分别计算before和after阶段）
        /// </summary>
        private async Task<Dictionary<string, float>> CalculatePhotoColumnWidths(PptTableData tableData, float rowHeight)
        {
            var photoColumnWidths = new Dictionary<string, float>();

            try
            {
                // 分别计算before和after阶段的列宽
                foreach (var position in tableData.PhotoPositions)
                {
                    // 计算before阶段该位置的最大宽度
                    if (tableData.TestStages.Contains("before"))
                    {
                        float maxBeforeWidth = await CalculatePositionMaxWidth(tableData, position, "before", rowHeight);
                        photoColumnWidths[$"before_{position}"] = Math.Max(maxBeforeWidth, 50f);
                        System.Diagnostics.Debug.WriteLine($"位置 {position} Before阶段最优宽度: {photoColumnWidths[$"before_{position}"]:F1}");
                    }

                    // 计算after阶段该位置的最大宽度
                    if (tableData.TestStages.Contains("after"))
                    {
                        float maxAfterWidth = await CalculatePositionMaxWidth(tableData, position, "after", rowHeight);
                        photoColumnWidths[$"after_{position}"] = Math.Max(maxAfterWidth, 50f);
                        System.Diagnostics.Debug.WriteLine($"位置 {position} After阶段最优宽度: {photoColumnWidths[$"after_{position}"]:F1}");
                    }
                }

                return photoColumnWidths;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"图片列宽计算失败: {ex.Message}");
                return new Dictionary<string, float>();
            }
        }

        /// <summary>
        /// 计算指定位置和测试阶段的最大图片宽度
        /// </summary>
        private async Task<float> CalculatePositionMaxWidth(PptTableData tableData, string position, string stage, float rowHeight)
        {
            float maxRequiredWidth = 0f;

            try
            {
                foreach (var batteryRow in tableData.BatteryRows)
                {
                    string imageId = null;

                    // 根据测试阶段获取对应的图片ID
                    if (stage == "before" && batteryRow.BeforeTestPhotos != null && batteryRow.BeforeTestPhotos.ContainsKey(position))
                    {
                        imageId = batteryRow.BeforeTestPhotos[position];
                    }
                    else if (stage == "after" && batteryRow.AfterTestPhotos != null && batteryRow.AfterTestPhotos.ContainsKey(position))
                    {
                        imageId = batteryRow.AfterTestPhotos[position];
                    }

                    if (!string.IsNullOrEmpty(imageId))
                    {
                        // 使用考虑旋转的尺寸计算方法
                        var imageSize = await GetImageOptimalSizeWithRotation(imageId, rowHeight);
                        maxRequiredWidth = Math.Max(maxRequiredWidth, imageSize.Width);
                        System.Diagnostics.Debug.WriteLine($"位置 {position} {stage}阶段 图片{imageId}: 宽度需求{imageSize.Width:F1}");
                    }
                }

                return maxRequiredWidth;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"计算位置{position} {stage}阶段宽度失败: {ex.Message}");
                return 50f; // 返回默认最小宽度
            }
        }


        /// <summary>
        /// 获取图片的最优尺寸（使用简化旋转估算，避免重复API请求）
        /// </summary>
        private async Task<(float Width, float Height)> GetImageOptimalSizeWithRotation(string imageId, float availableHeight)
        {
            try
            {
                // 1. 检查缓存（列宽计算专用缓存）
                string cacheKey = $"{imageId}_columnWidth";
                if (_imageSizeCache.ContainsKey(cacheKey))
                {
                    return _imageSizeCache[cacheKey];
                }

                // 2. 获取图片原始数据（应该已经在预处理阶段缓存）
                byte[] imageData;
                if (_imageDataCache.ContainsKey(imageId))
                {
                    imageData = _imageDataCache[imageId];
                    System.Diagnostics.Debug.WriteLine($"📦 使用预处理缓存的图片数据: {imageId}");
                }
                else
                {
                    // 预处理阶段应该已经下载了，如果没有则补充下载
                    System.Diagnostics.Debug.WriteLine($"⚠️ 图片数据未在预处理阶段缓存，补充下载: {imageId}");
                    imageData = await _imageApiClient.DownloadImageDataByFileIdAsync(imageId);
                    if (imageData != null && imageData.Length > 0)
                    {
                        _imageDataCache[imageId] = imageData; // 缓存图片数据
                    }
                }

                if (imageData == null || imageData.Length == 0)
                {
                    var defaultSize = (80f, 59.5f); // 默认宽度80点，高度2.1cm
                    _imageSizeCache[cacheKey] = defaultSize;
                    return defaultSize;
                }

                // 3. 获取压缩后的图片尺寸（使用API返回的尺寸，与实际处理保持一致）
                var (originalWidth, originalHeight) = GetCompressedImageDimensions(imageData, imageId);

                // 4. 使用预处理阶段的检测结果进行旋转判断
                bool needsRotation = false;
                BatteryDetectionApiResult detectionResult = null;

                try
                {
                    // 检查预处理阶段的检测结果缓存
                    if (_detectionResultCache.ContainsKey(imageId))
                    {
                        detectionResult = _detectionResultCache[imageId];
                        System.Diagnostics.Debug.WriteLine($"📦 使用预处理的检测结果: {imageId}");
                    }
                    else
                    {
                        // 预处理阶段应该已经检测了，如果没有则补充检测
                        System.Diagnostics.Debug.WriteLine($"⚠️ 检测结果未在预处理阶段缓存，补充检测: {imageId}");
                        detectionResult = await DetectBatteryInOriginalImage(imageData, imageId);
                        // 缓存检测结果
                        if (detectionResult != null)
                        {
                            _detectionResultCache[imageId] = detectionResult;
                        }
                    }

                    // 🔧 修复：使用与实际处理完全相同的旋转判断逻辑，传入完整上下文信息
                    needsRotation = CalculateRotationFromApiData(imageData, detectionResult, $"{imageId}_columnWidth", "列宽计算", "未知位置", "列宽计算阶段");
                    System.Diagnostics.Debug.WriteLine($"📏 列宽计算 - 图片 {imageId}: 预处理旋转判断={needsRotation}");
                }
                catch (Exception ex)
                {
                    // 如果API调用失败，使用简化判断作为后备
                    needsRotation = originalHeight > originalWidth * 1.2f;
                    System.Diagnostics.Debug.WriteLine($"📏 列宽计算 - 图片 {imageId}: API调用失败，使用简化判断={needsRotation}, 错误: {ex.Message}");
                }

                // 5. 计算裁剪后的尺寸（考虑电池检测裁剪）
                int displayWidth, displayHeight;

                if (detectionResult != null && HasValidCropData(detectionResult))
                {
                    // 使用裁剪后的尺寸（与实际处理保持一致）
                    float cropLeftPercent = detectionResult.CropLeftPercent;
                    float cropTopPercent = detectionResult.CropTopPercent;
                    float cropRightPercent = detectionResult.CropRightPercent;
                    float cropBottomPercent = detectionResult.CropBottomPercent;

                    // 计算裁剪后的尺寸
                    float croppedWidth = originalWidth * (1.0f - cropLeftPercent - cropRightPercent);
                    float croppedHeight = originalHeight * (1.0f - cropTopPercent - cropBottomPercent);

                    // 应用旋转
                    displayWidth = (int)(needsRotation ? croppedHeight : croppedWidth);
                    displayHeight = (int)(needsRotation ? croppedWidth : croppedHeight);

                    System.Diagnostics.Debug.WriteLine($"📏 列宽计算 - 图片 {imageId}: 裁剪前{originalWidth}x{originalHeight} -> 裁剪后{croppedWidth:F1}x{croppedHeight:F1} -> 旋转后{displayWidth}x{displayHeight}");
                }
                else
                {
                    // 没有裁剪信息，使用完整图片尺寸
                    displayWidth = needsRotation ? originalHeight : originalWidth;
                    displayHeight = needsRotation ? originalWidth : originalHeight;

                    System.Diagnostics.Debug.WriteLine($"📏 列宽计算 - 图片 {imageId}: 无裁剪信息，使用完整尺寸 {originalWidth}x{originalHeight} -> 旋转后{displayWidth}x{displayHeight}");
                }

                // 6. 计算最终显示尺寸（优先高度策略）
                // 目标高度：使用实际可用高度（与图片尺寸调整逻辑保持一致）
                float cellHeight = 2.22f * 28.35f; // 行高2.22cm
                float targetHeight = cellHeight - 5f; // 减去边距，与AdjustImageSizeToFitCell保持一致

                // 按比例计算宽度，确保高度铺满可用空间
                float aspectRatio = (float)displayWidth / displayHeight;
                float finalHeight = targetHeight;
                float finalWidth = finalHeight * aspectRatio;

                System.Diagnostics.Debug.WriteLine($"📏 列宽计算 - 图片 {imageId}: 旋转后宽高比{aspectRatio:F2}, 可用高度{finalHeight:F1}点，图片宽度{finalWidth:F1}点");

                // 为列宽计算添加必要的边距（最小化边距，精确计算）
                float columnWidthWithMargin = finalWidth + 8f; // 只增加8点边距（左右各4点）
                finalWidth = columnWidthWithMargin;

                System.Diagnostics.Debug.WriteLine($"📏 列宽计算 - 图片 {imageId}: 可用高度{finalHeight:F1}点，所需列宽{finalWidth:F1}点（含8点边距）");

                // 7. 缓存结果
                var result = (finalWidth, finalHeight);
                _imageSizeCache[cacheKey] = result;

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 获取图片 {imageId} 列宽尺寸失败: {ex.Message}");
                var defaultSize = (80f, 59.5f); // 默认尺寸
                return defaultSize;
            }
        }

        /// <summary>
        /// 验证并调整列宽（边界情况处理）
        /// </summary>
        private void ValidateAndAdjustColumnWidths(Dictionary<int, float> columnWidths, float maxTableWidth)
        {
            const float MIN_PHOTO_COLUMN_WIDTH = 50f; // 最小图片列宽度

            try
            {
                // 1. 确保最小列宽
                var photoColumnIndices = columnWidths.Keys.Where(k => k > 2 && k < columnWidths.Keys.Max()).ToList();
                foreach (var colIndex in photoColumnIndices)
                {
                    if (columnWidths[colIndex] < MIN_PHOTO_COLUMN_WIDTH)
                    {
                        columnWidths[colIndex] = MIN_PHOTO_COLUMN_WIDTH;
                        System.Diagnostics.Debug.WriteLine($"列{colIndex}宽度调整到最小值: {MIN_PHOTO_COLUMN_WIDTH}");
                    }
                }

                // 2. 检查总宽度是否超限（宽松处理，优先保证图片高度）
                float totalWidth = columnWidths.Values.Sum();
                float overflowRatio = totalWidth / maxTableWidth;

                if (overflowRatio > 1.1f) // 只有超出10%以上才强制缩放
                {
                    // 计算固定列和图片列的宽度
                    float fixedColumnsWidth = columnWidths[1] + columnWidths[2] + columnWidths[columnWidths.Keys.Max()]; // Cell No + SOC + Test Result
                    float photoColumnsWidth = totalWidth - fixedColumnsWidth;

                    // 按比例缩放图片列
                    float availablePhotoWidth = maxTableWidth - fixedColumnsWidth;
                    if (availablePhotoWidth > 0 && photoColumnsWidth > 0)
                    {
                        float scaleFactor = availablePhotoWidth / photoColumnsWidth;
                        foreach (var colIndex in photoColumnIndices)
                        {
                            columnWidths[colIndex] = Math.Max(columnWidths[colIndex] * scaleFactor, MIN_PHOTO_COLUMN_WIDTH);
                        }
                        System.Diagnostics.Debug.WriteLine($"表格超宽{overflowRatio:P1}，图片列强制缩放，缩放因子: {scaleFactor:F3}");
                    }
                }
                else if (overflowRatio > 1.0f)
                {
                    System.Diagnostics.Debug.WriteLine($"表格轻微超宽{overflowRatio:P1}，优先保证图片高度，不进行缩放");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"列宽验证调整失败: {ex.Message}");
            }
        }



        /// <summary>
        /// 获取默认列宽配置
        /// </summary>
        private Dictionary<int, float> GetDefaultColumnWidths(PptTableData tableData)
        {
            var columnWidths = new Dictionary<int, float>();

            // 固定列宽度
            columnWidths[1] = 80f; // Cell No列
            columnWidths[2] = 60f; // SOC列

            // 计算总列数
            int beforeCols = tableData.TestStages.Contains("before") ? tableData.PhotoPositions.Count : 0;
            int afterCols = tableData.TestStages.Contains("after") ? tableData.PhotoPositions.Count : 0;
            int totalCols = 2 + beforeCols + afterCols + 1;

            columnWidths[totalCols] = 80f; // Test Result列

            // 图片列使用默认宽度
            for (int i = 3; i < totalCols; i++)
            {
                columnWidths[i] = 100f; // 默认图片列宽度
            }

            return columnWidths;
        }

        /// <summary>
        /// 设置表头行样式（第1-2行）
        /// </summary>
        private void SetTableHeaderStyle(PowerPoint.Table table, int headerRows)
        {
            var headerStyle = new TableCellStyle
            {
                BackgroundColor = ColorTranslator.FromHtml("#9fc8f0"),
                FontName = "Times New Roman",
                FontNameFarEast = "思源黑体",
                FontSize = 10,
                IsBold = true,
                FontColor = Color.Black,
                HorizontalAlignment = PowerPoint.PpParagraphAlignment.ppAlignCenter,
                VerticalAlignment = Microsoft.Office.Core.MsoVerticalAnchor.msoAnchorMiddle
            };

            for (int row = 1; row <= headerRows; row++)
            {
                ApplyStyleToRow(table.Rows[row], headerStyle);
            }
        }

        /// <summary>
        /// 设置内容行样式（第3行开始）
        /// </summary>
        private void SetTableContentStyle(PowerPoint.Table table, int startRow)
        {
            var contentStyle = new TableCellStyle
            {
                BackgroundColor = Color.White,
                FontName = "Times New Roman",
                FontNameFarEast = "思源黑体",
                FontSize = 6,
                IsBold = false,
                FontColor = Color.Black,
                HorizontalAlignment = PowerPoint.PpParagraphAlignment.ppAlignCenter,
                VerticalAlignment = Microsoft.Office.Core.MsoVerticalAnchor.msoAnchorMiddle
            };

            for (int row = startRow; row <= table.Rows.Count; row++)
            {
                ApplyStyleToRow(table.Rows[row], contentStyle);
            }
        }

        /// <summary>
        /// 将样式应用到整行
        /// </summary>
        private void ApplyStyleToRow(PowerPoint.Row row, TableCellStyle style)
        {
            try
            {
                for (int col = 1; col <= row.Cells.Count; col++)
                {
                    var cell = row.Cells[col];
                    ApplyStyleToCell(cell, style);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用行样式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 将样式应用到单个单元格
        /// </summary>
        private void ApplyStyleToCell(PowerPoint.Cell cell, TableCellStyle style)
        {
            try
            {
                // 设置背景色
                cell.Shape.Fill.ForeColor.RGB = ColorTranslator.ToOle(style.BackgroundColor);

                // 设置文本样式
                if (cell.Shape.HasTextFrame == Microsoft.Office.Core.MsoTriState.msoTrue)
                {
                    var textRange = cell.Shape.TextFrame.TextRange;
                    textRange.Font.Name = style.FontName;
                    textRange.Font.NameFarEast = style.FontNameFarEast;
                    textRange.Font.Color.RGB = ColorTranslator.ToOle(style.FontColor);
                    textRange.Font.Size = style.FontSize;
                    textRange.Font.Bold = style.IsBold ? Microsoft.Office.Core.MsoTriState.msoTrue : Microsoft.Office.Core.MsoTriState.msoFalse;

                    // 对齐方式
                    textRange.ParagraphFormat.Alignment = style.HorizontalAlignment;
                    cell.Shape.TextFrame.VerticalAnchor = style.VerticalAlignment;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用单元格样式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量处理图片
        /// </summary>
        private async Task ProcessImagesInBatch(List<ImageProcessingInfo> imageInfoList)
        {
            try
            {
                if (imageInfoList == null || imageInfoList.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ 没有图片需要处理");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"🔄 开始批量处理 {imageInfoList.Count} 张图片");

                // 第一步：批量获取所有图片（优先使用缓存）
                var imageDataDict = new Dictionary<string, byte[]>();
                var downloadTasks = imageInfoList.Select(async info =>
                {
                    try
                    {
                        byte[] imageData;

                        // 检查缓存
                        lock (_imageDataCache)
                        {
                            if (_imageDataCache.ContainsKey(info.FileId))
                            {
                                imageData = _imageDataCache[info.FileId];
                                lock (imageDataDict)
                                {
                                    imageDataDict[info.FileId] = imageData;
                                }
                                System.Diagnostics.Debug.WriteLine($"📦 使用缓存图片: {info.FileId}");
                                return;
                            }
                        }

                        // 如果缓存中没有，则下载
                        imageData = await _imageApiClient.DownloadImageDataByFileIdAsync(info.FileId);
                        if (imageData != null && imageData.Length > 0)
                        {
                            lock (_imageDataCache)
                            {
                                _imageDataCache[info.FileId] = imageData; // 更新缓存
                            }

                            lock (imageDataDict)
                            {
                                imageDataDict[info.FileId] = imageData;
                            }
                            System.Diagnostics.Debug.WriteLine($"📥 下载新图片: {info.FileId}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ 获取图片失败: {info.FileId}, {ex.Message}");
                    }
                });

                await Task.WhenAll(downloadTasks);
                System.Diagnostics.Debug.WriteLine($"📥 批量获取完成，成功获取 {imageDataDict.Count}/{imageInfoList.Count} 张图片");

                // 第二步：批量调用电池检测API
                var fileIds = imageDataDict.Keys.ToList();
                var imageDataList = imageDataDict.Select(kvp => new BatteryDetectionImageData
                {
                    ImageId = kvp.Key,
                    ImageData = kvp.Value
                }).ToList();

                var tableProcessor = new BatteryTableImageProcessor();
                var detectionResults = await tableProcessor.DetectBatteryInMultipleImages(imageDataList);



                // 第三步：串行插入图片到PPT（避免COM线程安全问题）
                System.Diagnostics.Debug.WriteLine($"🔄 开始串行插入 {imageInfoList.Count} 张图片到PPT");
                int insertedCount = 0;

                foreach (var info in imageInfoList)
                {
                    try
                    {
                        if (imageDataDict.ContainsKey(info.FileId))
                        {
                            var imageData = imageDataDict[info.FileId];
                            var detectionResult = detectionResults.FirstOrDefault(r => r.ImageId == info.FileId);

                            InsertImageToCellWithDetection(info.Cell, info.FileId, imageData, detectionResult, info.RowIndex, info.ColIndex, info.CellNumber, info.Position, info.Stage);
                            insertedCount++;
                            System.Diagnostics.Debug.WriteLine($"✅ 插入完成 ({insertedCount}/{imageInfoList.Count}): {info.FileId} -> 行{info.RowIndex}列{info.ColIndex}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ 跳过插入: {info.FileId} (下载失败)");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ 插入失败: {info.FileId}, {ex.Message}");
                    }
                }
                System.Diagnostics.Debug.WriteLine($"🎯 批量插入完成");

            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 批量处理失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 插入图片到单元格（批量处理版本：使用预下载的数据和检测结果）
        /// </summary>
        private void InsertImageToCellWithDetection(PowerPoint.Cell cell, string fileId, byte[] imageData, BatteryDetectionApiResult detectionResult, int rowIndex, int colIndex, string cellNumber, string position, string stage)
        {
            try
            {
                if (imageData == null || imageData.Length == 0)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ 图片数据为空: {fileId}");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"🔄 开始处理图片: {fileId} (批量模式) - 电芯{cellNumber}-{position}-{stage}");

                // 1. 获取原图尺寸
                var (originalWidth, originalHeight) = GetImageDimensionsFromData(imageData);
                System.Diagnostics.Debug.WriteLine($"原始图片尺寸: {originalWidth}x{originalHeight}, 宽高比: {(float)originalWidth / originalHeight:F2}");

                // 2. 计算目标尺寸（优先让高度达到2.1cm）
                float cellWidth = cell.Shape.Width;
                float cellHeight = cell.Shape.Height;
                float cellLeft = cell.Shape.Left;
                float cellTop = cell.Shape.Top;

                // 计算可用空间，为边距留出空间（信任列宽计算结果，不限制宽度）
                float availableHeight = cellHeight - 5f; // 上下各留2.5点边距

                // 使用新的优先高度策略计算图片尺寸（不限制宽度，铺满可用高度）
                var (targetWidth, targetHeight) = CalculateImageSizeWithAspectRatio(imageData, float.MaxValue, availableHeight);
                System.Diagnostics.Debug.WriteLine($"计算后尺寸: {targetWidth:F1}x{targetHeight:F1} (可用高度: {availableHeight:F1})");

                // 3. 使用内存流方式插入图片到PPT表格单元格
                // 清空单元格文本，为图片腾出空间
                try
                {
                    cell.Shape.TextFrame.TextRange.Text = "";
                }
                catch (Exception) { }

                // 计算单元格内的居中位置
                float centerX = cellLeft + (cellWidth - targetWidth) / 2;
                float centerY = cellTop + (cellHeight - targetHeight) / 2;

                // 使用内存流方式插入图片（避免临时文件）
                var picture = InsertImageFromMemoryStream(imageData, centerX, centerY, targetWidth, targetHeight, fileId);

                // 设置图片名称和标记，便于识别
                try
                {
                    picture.Name = $"BatteryImage_{rowIndex}_{colIndex}_{fileId}";
                    picture.AlternativeText = $"TableCell_{rowIndex}_{colIndex}";
                }
                catch (Exception) { }

                // 4. 应用电池检测裁剪
                if (detectionResult != null && HasValidCropData(detectionResult))
                {
                    System.Diagnostics.Debug.WriteLine($"✅ 检测成功: Left={detectionResult.CropLeft}, Top={detectionResult.CropTop}, Right={detectionResult.CropRight}, Bottom={detectionResult.CropBottom}, 置信度={detectionResult.Confidence:F7}");
                    var (imgWidth, imgHeight) = GetImageDimensionsFromApiData(detectionResult, imageData);
                    var tableProcessor = new BatteryTableImageProcessor();
                    tableProcessor.ApplyDetectionToPowerPointShape(picture, detectionResult, imgWidth, imgHeight);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ 无有效检测结果，跳过PPT裁剪: {fileId}");
                }

                // 5. 判断是否需要旋转
                bool needsRotation = CalculateRotationFromApiData(imageData, detectionResult, fileId, cellNumber, position, stage);
                if (needsRotation)
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"🔄 开始执行PPT图片旋转: {fileId}");
                        System.Diagnostics.Debug.WriteLine($"   旋转前图片属性: Width={picture.Width:F1}, Height={picture.Height:F1}, Rotation={picture.Rotation}");

                        // 修复旋转逻辑：确保图片最终以横向方式展示
                        float currentRotation = picture.Rotation;
                        float targetRotation = CalculateOptimalRotationForLandscape(currentRotation, picture.Width, picture.Height);
                        picture.Rotation = targetRotation;

                        System.Diagnostics.Debug.WriteLine($"   旋转角度: {currentRotation}° → {targetRotation}° (优化为横向展示)");
                        System.Diagnostics.Debug.WriteLine($"   旋转后图片属性: Width={picture.Width:F1}, Height={picture.Height:F1}, Rotation={picture.Rotation}");
                        System.Diagnostics.Debug.WriteLine($"✅ PPT图片旋转为横向展示成功: {fileId} (电芯{cellNumber}-{position}-{stage})");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ PPT图片旋转失败: {fileId}, 错误: {ex.Message}");
                        System.Diagnostics.Debug.WriteLine($"   异常类型: {ex.GetType().Name}");
                        System.Diagnostics.Debug.WriteLine($"   堆栈跟踪: {ex.StackTrace}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ PPT图片无需旋转: {fileId} (电芯{cellNumber}-{position}-{stage})");
                }

                // 6. 最终位置和尺寸调整
                // 旋转后需要重新调整图片尺寸以确保铺满可用高度
                AdjustImageSizeToFitCell(picture, cellWidth, cellHeight);

                // 重新计算居中位置（基于调整后的尺寸）
                float finalCenterX = cellLeft + (cellWidth - picture.Width) / 2;
                float finalCenterY = cellTop + (cellHeight - picture.Height) / 2;
                picture.Left = finalCenterX;
                picture.Top = finalCenterY;

                System.Diagnostics.Debug.WriteLine($"✅ 批量模式图片处理完成: {fileId} (电芯{cellNumber}-{position}-{stage})");
                System.Diagnostics.Debug.WriteLine($"   单元格位置: ({cellLeft:F1}, {cellTop:F1}), 尺寸: {cellWidth:F1}x{cellHeight:F1}");
                System.Diagnostics.Debug.WriteLine($"   图片位置: ({picture.Left:F1}, {picture.Top:F1}), 尺寸: {picture.Width:F1}x{picture.Height:F1}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 批量模式插入图片失败: {fileId}, {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 计算最优旋转角度以实现横向展示（修复版本）
        /// </summary>
        private float CalculateOptimalRotationForLandscape(float currentRotation, float currentWidth, float currentHeight)
        {
            try
            {
                // 标准化当前角度到0-360范围
                float normalizedCurrent = ((currentRotation % 360) + 360) % 360;

                System.Diagnostics.Debug.WriteLine($"🎯 横向旋转计算: 当前角度={normalizedCurrent}°, 当前尺寸={currentWidth:F1}x{currentHeight:F1}");

                // 检查当前旋转状态下的实际显示方向
                bool isRotated90or270 = (Math.Abs(normalizedCurrent % 180 - 90) < 45);
                float displayWidth = isRotated90or270 ? currentHeight : currentWidth;
                float displayHeight = isRotated90or270 ? currentWidth : currentHeight;

                System.Diagnostics.Debug.WriteLine($"🎯 实际显示尺寸: {displayWidth:F1}x{displayHeight:F1}, 当前是否横向: {displayWidth > displayHeight}");

                // 如果当前显示已经是横向（宽>高），保持不变
                if (displayWidth > displayHeight)
                {
                    System.Diagnostics.Debug.WriteLine($"🎯 目标角度: {normalizedCurrent}° (已经是横向展示，保持不变)");
                    return normalizedCurrent;
                }

                // 当前显示是竖向，需要旋转90度使其变为横向
                float targetRotation = (normalizedCurrent + 90) % 360;
                System.Diagnostics.Debug.WriteLine($"🎯 目标角度: {targetRotation}° (旋转90度实现横向展示)");

                return targetRotation;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 计算最优旋转角度失败: {ex.Message}");
                return currentRotation; // 出错时保持当前角度
            }
        }

        /// <summary>
        /// 计算目标旋转角度（横向优先逻辑：让图片放平以充分利用2.1cm高度）
        /// </summary>
        private float CalculateTargetRotation(float currentRotation, float currentWidth, float currentHeight)
        {
            try
            {
                // 标准化当前角度到0-360范围
                float normalizedCurrent = ((currentRotation % 360) + 360) % 360;

                // 判断当前图片的方向
                bool isCurrentlyPortrait = currentHeight > currentWidth;

                System.Diagnostics.Debug.WriteLine($"🎯 旋转角度计算: 当前角度={normalizedCurrent}°, 当前尺寸={currentWidth:F1}x{currentHeight:F1}, 当前方向={(isCurrentlyPortrait ? "竖向" : "横向")}");

                // 目标：让图片变成横向（宽>高），这样可以更好地利用2.1cm的表格高度
                if (isCurrentlyPortrait)
                {
                    // 当前是竖向，需要旋转为横向（放平）
                    if (Math.Abs(normalizedCurrent - 0) < Math.Abs(normalizedCurrent - 180))
                    {
                        System.Diagnostics.Debug.WriteLine($"🎯 目标角度: 90° (从0°竖向转为横向，放平图片)");
                        return 90f;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"🎯 目标角度: 270° (从180°竖向转为横向，放平图片)");
                        return 270f;
                    }
                }
                else
                {
                    // 已经是横向，检查是否需要微调到标准角度
                    if (Math.Abs(normalizedCurrent - 90) < Math.Abs(normalizedCurrent - 270))
                    {
                        System.Diagnostics.Debug.WriteLine($"🎯 目标角度: 90° (保持横向，标准化角度)");
                        return 90f;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"🎯 目标角度: 270° (保持横向，标准化角度)");
                        return 270f;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 计算目标旋转角度失败: {ex.Message}");
                return currentRotation; // 出错时保持当前角度
            }
        }

        /// <summary>
        /// 清理图片缓存
        /// </summary>
        private void ClearImageCache()
        {
            try
            {
                int sizeCount = _imageSizeCache.Count;
                int dataCount = _imageDataCache.Count;
                int detectionCount = _detectionResultCache.Count;

                _imageSizeCache.Clear();
                _imageDataCache.Clear();
                _detectionResultCache.Clear();

                System.Diagnostics.Debug.WriteLine($"🗑️ 缓存已清理: 尺寸缓存{sizeCount}项, 数据缓存{dataCount}项, 检测缓存{detectionCount}项");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ 清理缓存失败: {ex.Message}");
            }
        }
    }
}
