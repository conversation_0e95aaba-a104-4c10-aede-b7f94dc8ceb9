# PPT表格生成功能使用说明

## 概述

PPT表格生成功能已经进行了全面优化，现在提供两个版本：
- **优化版**：使用专用PPT接口，提供更灵活的数据选择和展示能力
- **旧版**：保持向后兼容，使用原有接口

## 新功能特性

### 1. 专用PPT数据接口
- 独立于现有业务接口，避免业务变更影响PPT功能
- 提供稳定的数据结构和接口契约
- 支持版本控制机制

### 2. 增强的用户选择能力
- **委托单号输入和验证**：实时验证委托单号有效性
- **测试项目选择**：根据委托单号动态加载可用测试项目
- **照片部位多选**：支持根据测试项目动态显示可用部位
- **测试阶段选择**：灵活选择测试前/测试后

### 3. 优化的表格布局
- 标准化的三段式表格结构
- 支持测试前后照片的对比展示
- 清晰的视觉分组和标识

## 使用方法

### 步骤1：启动功能
1. 在PowerPoint中打开演示文稿
2. 选择要插入表格的幻灯片
3. 点击"PBI-ppt"选项卡中的"生成PPT表格(优化版)"按钮

### 步骤2：登录验证
- 如果未登录，系统会提示登录
- 输入用户名和密码完成登录

### 步骤3：输入委托单号
1. 在"委托单号"输入框中输入委托单号
2. 点击"验证"按钮或按回车键
3. 系统会验证委托单号的有效性

### 步骤4：选择测试项目
1. 验证成功后，系统会自动加载该委托单号下的测试项目
2. 从下拉列表中选择需要的测试项目

### 步骤5：选择照片部位
1. 系统会根据选择的测试项目显示可用的照片部位
2. 勾选需要的照片部位（支持多选）
3. 可使用"全选"和"清空"按钮快速操作

### 步骤6：选择测试阶段
1. 选择需要的测试阶段：
   - 测试前：显示测试前的照片
   - 测试后：显示测试后的照片
2. 默认同时选择测试前和测试后

### 步骤7：生成表格
1. 确认所有选择无误后，点击"生成表格"按钮
2. 系统会显示进度条，显示表格生成进度
3. 生成完成后，表格会自动插入到当前幻灯片中

## 表格结构说明

生成的表格采用标准化的三段式结构：

```
┌─────────────┬─────┬──────────────────────────────────┬──────────────────────────────────┬─────────────┐
│   Cell No.  │ SOC │           Before Test            │         After Test (M.04)       │ Test Result │
├─────────────┼─────┼──────────┬──────────┬──────────┼──────────┬──────────┬──────────┼─────────────┤
│             │     │ Terminal │  Middle  │   Vent   │ Terminal │  Middle  │   Vent   │             │
├─────────────┼─────┼──────────┼──────────┼──────────┼──────────┼──────────┼──────────┼─────────────┤
│ 电芯编码1    │     │  [图片]   │  [图片]   │  [图片]   │  [图片]   │  [图片]   │  [图片]   │             │
├─────────────┼─────┼──────────┼──────────┼──────────┼──────────┼──────────┼──────────┼─────────────┤
│ 电芯编码2    │     │  [图片]   │  [图片]   │  [图片]   │  [图片]   │  [图片]   │  [图片]   │             │
└─────────────┴─────┴──────────┴──────────┴──────────┴──────────┴──────────┴──────────┴─────────────┘
```

### 表格特性
- **表头背景色**：使用 #9fc8f0 背景色
- **内容区域**：使用白色背景
- **默认值**：SOC和Test Result列默认为空
- **图片处理**：自动缩放、居中对齐、保持宽高比

## 照片部位说明

系统会根据不同的测试项目自动显示对应的照片部位：

### 过充测试/过放测试
- Terminal（端子）
- Middle（中部）
- Vent（通气孔）

### 短路测试/挤压测试
- Terminal（端子）
- Vent（通气孔）

### 跌落测试/冲击测试
- Terminal（端子）
- Middle（中部）
- Vent（通气孔）
- Corner（角部）

### 针刺测试
- Terminal（端子）
- Middle（中部）
- Vent（通气孔）
- Needle（针刺点）

## 错误处理

### 常见错误及解决方法

1. **委托单号不存在**
   - 检查输入的委托单号是否正确
   - 确认该委托单号在系统中存在

2. **登录已过期**
   - 重新登录系统
   - 检查网络连接

3. **没有找到测试项目**
   - 确认该委托单号下有相关的测试项目
   - 联系管理员检查数据

4. **图片加载失败**
   - 检查网络连接
   - 确认图片文件存在且可访问

## 性能优化

- **异步加载**：避免界面冻结
- **批量处理**：提高数据处理效率
- **进度显示**：实时显示处理进度
- **错误恢复**：自动重试机制

## 兼容性说明

- 支持PowerPoint 2016及以上版本
- 需要.NET Framework 4.7.2或更高版本
- 需要网络连接访问后端API

## 技术支持

如遇到问题，请联系开发团队或查看日志文件获取详细错误信息。

---

**版本**: v2.0  
**更新日期**: 2025-07-02  
**开发团队**: PPT插件开发组
