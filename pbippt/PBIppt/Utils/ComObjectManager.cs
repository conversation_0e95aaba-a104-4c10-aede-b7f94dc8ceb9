using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Diagnostics;

namespace PBIppt.Utils
{
    /// <summary>
    /// COM对象管理器，用于正确释放PowerPoint COM对象，防止内存泄漏
    /// </summary>
    public static class ComObjectManager
    {
        private static readonly List<object> _trackedObjects = new List<object>();
        private static readonly object _lock = new object();

        /// <summary>
        /// 注册需要跟踪的COM对象
        /// </summary>
        /// <param name="comObject">COM对象</param>
        /// <returns>返回原对象，方便链式调用</returns>
        public static T Track<T>(T comObject) where T : class
        {
            if (comObject == null) return null;

            lock (_lock)
            {
                _trackedObjects.Add(comObject);
                Debug.WriteLine($"COM对象已注册跟踪: {comObject.GetType().Name}, 当前跟踪数量: {_trackedObjects.Count}");
            }

            return comObject;
        }

        /// <summary>
        /// 安全释放单个COM对象
        /// </summary>
        /// <param name="comObject">要释放的COM对象</param>
        public static void SafeRelease(object comObject)
        {
            if (comObject == null) return;

            try
            {
                // 从跟踪列表中移除
                lock (_lock)
                {
                    _trackedObjects.Remove(comObject);
                }

                // 释放COM对象
                int refCount = Marshal.ReleaseComObject(comObject);
                Debug.WriteLine($"COM对象已释放: {comObject.GetType().Name}, 剩余引用计数: {refCount}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"释放COM对象失败: {ex.Message}");
                Logger.Warning($"释放COM对象失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放所有跟踪的COM对象
        /// </summary>
        public static void ReleaseAll()
        {
            lock (_lock)
            {
                Debug.WriteLine($"开始释放所有跟踪的COM对象，数量: {_trackedObjects.Count}");

                // 倒序释放，确保子对象先于父对象释放
                for (int i = _trackedObjects.Count - 1; i >= 0; i--)
                {
                    try
                    {
                        var comObject = _trackedObjects[i];
                        if (comObject != null)
                        {
                            Marshal.ReleaseComObject(comObject);
                            Debug.WriteLine($"已释放COM对象: {comObject.GetType().Name}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"释放COM对象时出错: {ex.Message}");
                    }
                }

                _trackedObjects.Clear();
                Debug.WriteLine("所有COM对象释放完成");
            }
        }

        /// <summary>
        /// 获取当前跟踪的COM对象数量
        /// </summary>
        public static int TrackedObjectCount
        {
            get
            {
                lock (_lock)
                {
                    return _trackedObjects.Count;
                }
            }
        }

        /// <summary>
        /// 强制垃圾回收，用于确保COM对象完全释放
        /// </summary>
        public static void ForceGarbageCollection()
        {
            try
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                Debug.WriteLine("强制垃圾回收完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"强制垃圾回收失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// COM对象包装器，实现IDisposable模式，确保COM对象正确释放
    /// </summary>
    /// <typeparam name="T">COM对象类型</typeparam>
    public class ComObjectWrapper<T> : IDisposable where T : class
    {
        private T _comObject;
        private bool _disposed = false;

        public ComObjectWrapper(T comObject)
        {
            _comObject = comObject;
            if (_comObject != null)
            {
                ComObjectManager.Track(_comObject);
            }
        }

        /// <summary>
        /// 获取包装的COM对象
        /// </summary>
        public T Object => _comObject;

        /// <summary>
        /// 隐式转换操作符，方便直接使用
        /// </summary>
        public static implicit operator T(ComObjectWrapper<T> wrapper)
        {
            return wrapper?._comObject;
        }

        /// <summary>
        /// 释放COM对象
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing && _comObject != null)
                {
                    ComObjectManager.SafeRelease(_comObject);
                    _comObject = null;
                }
                _disposed = true;
            }
        }

        ~ComObjectWrapper()
        {
            Dispose(false);
        }
    }
}
