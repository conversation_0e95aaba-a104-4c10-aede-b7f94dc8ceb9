using System;
using System.IO;
using System.Diagnostics;

namespace PBIppt.Utils
{
    /// <summary>
    /// Simple logging utility for the add-in
    /// </summary>
    public static class Logger
    {
        private static readonly string LogFilePath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "PBIppt",
            "log.txt");

        static Logger()
        {
            try
            {
                // Ensure log directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(LogFilePath));
            }
            catch
            {
                // Suppress initialization errors
            }
        }

        /// <summary>
        /// Log an informational message
        /// </summary>
        public static void Info(string message)
        {
            Log("INFO", message);
        }

        /// <summary>
        /// Log a warning message
        /// </summary>
        public static void Warning(string message)
        {
            Log("WARNING", message);
        }

        /// <summary>
        /// Log a warning message (alias for Warning)
        /// </summary>
        public static void LogWarning(string message)
        {
            Log("WARNING", message);
        }

        /// <summary>
        /// Log a warning message (alias for Warning)
        /// </summary>
        public static void Warn(string message)
        {
            Log("WARNING", message);
        }

        /// <summary>
        /// Log an error message
        /// </summary>
        public static void Error(string message)
        {
            Log("ERROR", message);
        }

        /// <summary>
        /// Log a debug message
        /// </summary>
        public static void Debug(string message)
        {
            Log("DEBUG", message);
        }

        /// <summary>
        /// Log an exception with full stack trace
        /// </summary>
        public static void Exception(Exception ex)
        {
            Log("EXCEPTION", $"{ex.Message}\n{ex.StackTrace}");
        }

        /// <summary>
        /// Log a message with specified level (public method)
        /// </summary>
        public static void Log(string level, string message)
        {
            WriteLog(level, message);
        }

        private static void WriteLog(string level, string message)
        {
            try
            {
                string logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} [{level}] {message}";

                System.Diagnostics.Debug.WriteLine(logEntry);

                File.AppendAllText(LogFilePath, logEntry + Environment.NewLine);
            }
            catch
            {
                // Suppress logging errors
            }
        }
    }
} 