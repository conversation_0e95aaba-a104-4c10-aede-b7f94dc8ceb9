using System;
using System.Diagnostics;
using PBIppt.Utils;
using PowerPoint = Microsoft.Office.Interop.PowerPoint;

namespace PBIppt.Utils
{
    /// <summary>
    /// COM对象管理器测试类
    /// </summary>
    public static class ComObjectManagerTest
    {
        /// <summary>
        /// 测试COM对象跟踪和释放功能
        /// </summary>
        public static void TestComObjectManagement()
        {
            try
            {
                Logger.Info("开始COM对象管理测试");
                
                // 测试1：基本跟踪和释放
                TestBasicTrackingAndRelease();
                
                // 测试2：包装器测试
                TestComObjectWrapper();
                
                // 测试3：批量释放测试
                TestBatchRelease();
                
                Logger.Info("COM对象管理测试完成");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex);
                Debug.WriteLine($"COM对象管理测试失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 测试基本的跟踪和释放功能
        /// </summary>
        private static void TestBasicTrackingAndRelease()
        {
            Debug.WriteLine("=== 测试基本跟踪和释放功能 ===");
            
            // 记录初始跟踪数量
            int initialCount = ComObjectManager.TrackedObjectCount;
            Debug.WriteLine($"初始跟踪对象数量: {initialCount}");
            
            // 模拟创建COM对象（这里用string代替，实际应该是COM对象）
            var mockComObject = new object();
            
            // 跟踪对象
            var trackedObject = ComObjectManager.Track(mockComObject);
            
            // 验证跟踪数量增加
            int afterTrackCount = ComObjectManager.TrackedObjectCount;
            Debug.WriteLine($"跟踪后对象数量: {afterTrackCount}");
            
            if (afterTrackCount != initialCount + 1)
            {
                throw new Exception("跟踪功能测试失败：对象数量未正确增加");
            }
            
            // 释放对象
            ComObjectManager.SafeRelease(trackedObject);
            
            // 验证跟踪数量减少
            int afterReleaseCount = ComObjectManager.TrackedObjectCount;
            Debug.WriteLine($"释放后对象数量: {afterReleaseCount}");
            
            if (afterReleaseCount != initialCount)
            {
                throw new Exception("释放功能测试失败：对象数量未正确减少");
            }
            
            Debug.WriteLine("✓ 基本跟踪和释放功能测试通过");
        }
        
        /// <summary>
        /// 测试COM对象包装器
        /// </summary>
        private static void TestComObjectWrapper()
        {
            Debug.WriteLine("=== 测试COM对象包装器 ===");
            
            int initialCount = ComObjectManager.TrackedObjectCount;
            Debug.WriteLine($"初始跟踪对象数量: {initialCount}");
            
            // 使用包装器
            using (var wrapper = new ComObjectWrapper<object>(new object()))
            {
                // 验证对象被跟踪
                int duringUseCount = ComObjectManager.TrackedObjectCount;
                Debug.WriteLine($"使用期间跟踪对象数量: {duringUseCount}");
                
                if (duringUseCount != initialCount + 1)
                {
                    throw new Exception("包装器跟踪测试失败：对象未被正确跟踪");
                }
                
                // 验证可以访问对象
                var obj = wrapper.Object;
                if (obj == null)
                {
                    throw new Exception("包装器访问测试失败：无法访问包装的对象");
                }
            }
            
            // 验证对象被自动释放
            int afterDisposeCount = ComObjectManager.TrackedObjectCount;
            Debug.WriteLine($"释放后跟踪对象数量: {afterDisposeCount}");
            
            if (afterDisposeCount != initialCount)
            {
                throw new Exception("包装器自动释放测试失败：对象未被正确释放");
            }
            
            Debug.WriteLine("✓ COM对象包装器测试通过");
        }
        
        /// <summary>
        /// 测试批量释放功能
        /// </summary>
        private static void TestBatchRelease()
        {
            Debug.WriteLine("=== 测试批量释放功能 ===");
            
            // 创建多个跟踪对象
            var obj1 = ComObjectManager.Track(new object());
            var obj2 = ComObjectManager.Track(new object());
            var obj3 = ComObjectManager.Track(new object());
            
            int beforeReleaseCount = ComObjectManager.TrackedObjectCount;
            Debug.WriteLine($"批量释放前跟踪对象数量: {beforeReleaseCount}");
            
            if (beforeReleaseCount < 3)
            {
                throw new Exception("批量释放测试准备失败：跟踪对象数量不足");
            }
            
            // 执行批量释放
            ComObjectManager.ReleaseAll();
            
            // 验证所有对象被释放
            int afterReleaseCount = ComObjectManager.TrackedObjectCount;
            Debug.WriteLine($"批量释放后跟踪对象数量: {afterReleaseCount}");
            
            if (afterReleaseCount != 0)
            {
                throw new Exception("批量释放测试失败：仍有对象未被释放");
            }
            
            Debug.WriteLine("✓ 批量释放功能测试通过");
        }
        
        /// <summary>
        /// 测试强制垃圾回收功能
        /// </summary>
        public static void TestForceGarbageCollection()
        {
            Debug.WriteLine("=== 测试强制垃圾回收功能 ===");
            
            try
            {
                // 记录GC前的内存使用
                long beforeGC = GC.GetTotalMemory(false);
                Debug.WriteLine($"GC前内存使用: {beforeGC} bytes");
                
                // 执行强制垃圾回收
                ComObjectManager.ForceGarbageCollection();
                
                // 记录GC后的内存使用
                long afterGC = GC.GetTotalMemory(false);
                Debug.WriteLine($"GC后内存使用: {afterGC} bytes");
                
                Debug.WriteLine("✓ 强制垃圾回收功能执行完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"强制垃圾回收测试失败: {ex.Message}");
                throw;
            }
        }
    }
}
