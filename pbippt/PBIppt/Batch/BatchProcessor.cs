using System;
using System.Collections.Generic;
using System.Drawing;
using System.Threading.Tasks;
using Microsoft.Office.Interop.PowerPoint;
using PBIppt.ApiClient;
using PBIppt.ImageProcessing;
using PBIppt.Models;
using PBIppt.Utils;
using PowerPoint = Microsoft.Office.Interop.PowerPoint;
using PBIppt.UI;

namespace PBIppt.Batch
{
    /// <summary>
    /// Handles batch processing of images in PowerPoint
    /// </summary>
    public class BatchProcessor
    {
        private readonly IBatteryApiClient _apiClient;
        private readonly ProgressManager _progressManager;

        /// <summary>
        /// 处理开始事件参数
        /// </summary>
        public class ProcessingStartedEventArgs : EventArgs
        {
            /// <summary>
            /// 总项目数
            /// </summary>
            public int TotalItems { get; set; }
            
            /// <summary>
            /// 描述信息
            /// </summary>
            public string Description { get; set; }
        }
        
        /// <summary>
        /// 项目处理事件参数
        /// </summary>
        public class ItemProcessedEventArgs : EventArgs
        {
            /// <summary>
            /// 已处理项目数
            /// </summary>
            public int ProcessedItems { get; set; }
            
            /// <summary>
            /// 总项目数
            /// </summary>
            public int TotalItems { get; set; }
        }
        
        /// <summary>
        /// 处理完成事件参数
        /// </summary>
        public class ProcessingCompletedEventArgs : EventArgs
        {
            /// <summary>
            /// 成功数量
            /// </summary>
            public int SuccessCount { get; set; }
            
            /// <summary>
            /// 失败数量
            /// </summary>
            public int FailureCount { get; set; }
            
            /// <summary>
            /// 总项目数
            /// </summary>
            public int TotalItems { get; set; }
        }
        
        /// <summary>
        /// 处理开始事件
        /// </summary>
        public event EventHandler<ProcessingStartedEventArgs> ProcessingStarted;
        
        /// <summary>
        /// 项目处理事件
        /// </summary>
        public event EventHandler<ItemProcessedEventArgs> ItemProcessed;
        
        /// <summary>
        /// 处理完成事件
        /// </summary>
        public event EventHandler<ProcessingCompletedEventArgs> ProcessingCompleted;

        /// <summary>
        /// Statistics for a batch operation
        /// </summary>
        public class BatchStats
        {
            /// <summary>
            /// Number of images processed
            /// </summary>
            public int ProcessedImages { get; set; }
            
            /// <summary>
            /// Number of batteries detected
            /// </summary>
            public int DetectedBatteries { get; set; }
            
            /// <summary>
            /// Number of errors encountered
            /// </summary>
            public int Errors { get; set; }
        }

        /// <summary>
        /// Create a new batch processor
        /// </summary>
        public BatchProcessor(IBatteryApiClient apiClient, ProgressManager progressManager)
        {
            _apiClient = apiClient;
            _progressManager = progressManager;
        }

        /// <summary>
        /// Process a single image shape
        /// </summary>
        public async Task<BatteryDetectionResult> ProcessSingleImageAsync(PowerPoint.Shape shape)
        {
            try
            {
                // Extract the image
                Image image = ImageExtractor.ExtractImageFromShape(shape);
                
                // Detect batteries
                BatteryDetectionResult result = await _apiClient.DetectBatteriesAsync(image);
                
                // Process the detection result
                if (result.Success && result.Batteries.Count > 0)
                {
                    // Sort batteries by confidence
                    result.Batteries.Sort((a, b) => b.Confidence.CompareTo(a.Confidence));
                    
                    // Crop the shape to focus on the battery with highest confidence
                    ImageCropper.CropShapeToBattery(shape, result.Batteries[0]);
                }
                
                // Clean up
                image.Dispose();
                
                return result;
            }
            catch (Exception ex)
            {
                Logger.Exception(ex);
                return new BatteryDetectionResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// Process all images on a slide
        /// </summary>
        public async Task<BatchStats> ProcessSlideAsync(Slide slide)
        {
            BatchStats stats = new BatchStats();
            List<PowerPoint.Shape> pictureShapes = new List<PowerPoint.Shape>();
            
            // Find all picture shapes on the slide
            foreach (PowerPoint.Shape shape in slide.Shapes)
            {
                if (shape.Type == Microsoft.Office.Core.MsoShapeType.msoPicture)
                {
                    pictureShapes.Add(shape);
                }
            }
            
            if (pictureShapes.Count == 0)
            {
                return stats;
            }
            
            // Start progress tracking
            _progressManager.StartProgress(pictureShapes.Count, $"正在处理幻灯片 {slide.SlideIndex} 上的 {pictureShapes.Count} 张图片...");
            
            // 触发处理开始事件
            ProcessingStarted?.Invoke(this, new ProcessingStartedEventArgs
            {
                TotalItems = pictureShapes.Count,
                Description = $"正在处理幻灯片 {slide.SlideIndex} 上的图片"
            });
            
            // Process each picture
            foreach (PowerPoint.Shape shape in pictureShapes)
            {
                try
                {
                    _progressManager.StatusMessage = $"处理图片 {stats.ProcessedImages + 1} / {pictureShapes.Count}...";
                    
                    BatteryDetectionResult result = await ProcessSingleImageAsync(shape);
                    
                    if (result.Success)
                    {
                        stats.DetectedBatteries += result.Batteries.Count;
                    }
                    else
                    {
                        stats.Errors++;
                    }
                    
                    stats.ProcessedImages++;
                    _progressManager.IncrementProgress();
                    
                    // 触发项目处理事件
                    ItemProcessed?.Invoke(this, new ItemProcessedEventArgs
                    {
                        ProcessedItems = stats.ProcessedImages,
                        TotalItems = pictureShapes.Count
                    });
                }
                catch (Exception ex)
                {
                    Logger.Exception(ex);
                    stats.Errors++;
                    stats.ProcessedImages++;
                    _progressManager.IncrementProgress();
                }
            }
            
            // Complete progress
            string message = $"已处理 {stats.ProcessedImages} 张图片，检测到 {stats.DetectedBatteries} 个电池";
            if (stats.Errors > 0)
            {
                message += $"，{stats.Errors} 张图片处理失败";
            }
            _progressManager.CompleteProgress(message);
            
            // 触发处理完成事件
            ProcessingCompleted?.Invoke(this, new ProcessingCompletedEventArgs
            {
                SuccessCount = stats.DetectedBatteries,
                FailureCount = stats.Errors,
                TotalItems = pictureShapes.Count
            });
            
            return stats;
        }

        /// <summary>
        /// Process all images in a presentation
        /// </summary>
        public async Task<BatchStats> ProcessPresentationAsync(Presentation presentation)
        {
            BatchStats stats = new BatchStats();
            
            // Count total picture shapes
            int totalShapes = 0;
            foreach (Slide slide in presentation.Slides)
            {
                foreach (PowerPoint.Shape shape in slide.Shapes)
                {
                    if (shape.Type == Microsoft.Office.Core.MsoShapeType.msoPicture)
                    {
                        totalShapes++;
                    }
                }
            }
            
            if (totalShapes == 0)
            {
                return stats;
            }
            
            // Start progress tracking
            _progressManager.StartProgress(totalShapes, $"正在处理演示文稿中的 {totalShapes} 张图片...");
            
            // 触发处理开始事件
            ProcessingStarted?.Invoke(this, new ProcessingStartedEventArgs
            {
                TotalItems = totalShapes,
                Description = "正在处理演示文稿中的图片"
            });
            
            // Process each slide
            int processedShapes = 0;
            foreach (Slide slide in presentation.Slides)
            {
                try
                {
                    _progressManager.StatusMessage = $"处理幻灯片 {slide.SlideIndex} / {presentation.Slides.Count}...";
                    
                    // Find picture shapes on this slide
                    List<PowerPoint.Shape> pictureShapes = new List<PowerPoint.Shape>();
                    foreach (PowerPoint.Shape shape in slide.Shapes)
                    {
                        if (shape.Type == Microsoft.Office.Core.MsoShapeType.msoPicture)
                        {
                            pictureShapes.Add(shape);
                        }
                    }
                    
                    // Process each picture on this slide
                    foreach (PowerPoint.Shape shape in pictureShapes)
                    {
                        try
                        {
                            BatteryDetectionResult result = await ProcessSingleImageAsync(shape);
                            
                            if (result.Success)
                            {
                                stats.DetectedBatteries += result.Batteries.Count;
                            }
                            else
                            {
                                stats.Errors++;
                            }
                            
                            stats.ProcessedImages++;
                            processedShapes++;
                            _progressManager.IncrementProgress();
                            
                            // 触发项目处理事件
                            ItemProcessed?.Invoke(this, new ItemProcessedEventArgs
                            {
                                ProcessedItems = processedShapes,
                                TotalItems = totalShapes
                            });
                        }
                        catch (Exception ex)
                        {
                            Logger.Exception(ex);
                            stats.Errors++;
                            stats.ProcessedImages++;
                            processedShapes++;
                            _progressManager.IncrementProgress();
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Exception(ex);
                }
            }
            
            // Complete progress
            string message = $"已处理 {stats.ProcessedImages} 张图片，检测到 {stats.DetectedBatteries} 个电池";
            if (stats.Errors > 0)
            {
                message += $"，{stats.Errors} 张图片处理失败";
            }
            _progressManager.CompleteProgress(message);
            
            // 触发处理完成事件
            ProcessingCompleted?.Invoke(this, new ProcessingCompletedEventArgs
            {
                SuccessCount = stats.DetectedBatteries,
                FailureCount = stats.Errors,
                TotalItems = processedShapes
            });
            
            return stats;
        }
    }
} 