using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using PBIppt.Models;
using System.Drawing;
using System.IO;

namespace PBIppt.ApiClient
{
    /// <summary>
    /// 电池API客户端实现
    /// </summary>
    public class BatteryApiClient : IBatteryApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly string _apiKey;
        
        /// <summary>
        /// 创建API客户端
        /// </summary>
        /// <param name="baseUrl">API基础URL</param>
        /// <param name="apiKey">API密钥（如果需要）</param>
        public BatteryApiClient(string baseUrl, string apiKey = null)
        {
            _baseUrl = baseUrl.TrimEnd('/');
            _apiKey = apiKey;
            
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            
            // 如果有API密钥，添加到请求头
            if (!string.IsNullOrEmpty(_apiKey))
            {
                _httpClient.DefaultRequestHeaders.Add("X-API-Key", _apiKey);
            }
        }
        
        /// <summary>
        /// 设置认证头
        /// </summary>
        private void SetAuthorizationHeader()
        {
            // 清除之前的认证头
            if (_httpClient.DefaultRequestHeaders.Contains("Authorization"))
            {
                _httpClient.DefaultRequestHeaders.Remove("Authorization");
            }
            
            // 获取并设置新的认证头
            string authHeader = AuthManager.GetAuthorizationHeader();
            if (!string.IsNullOrEmpty(authHeader))
            {
                _httpClient.DefaultRequestHeaders.Add("Authorization", authHeader);
            }
        }
        
        /// <summary>
        /// 检测图片中的电池
        /// </summary>
        public async Task<BatteryDetectionResult> DetectBatteriesAsync(Image image)
        {
            try
            {
                // 设置认证头
                SetAuthorizationHeader();
                
                // 将图片转换为字节数组
                byte[] imageData;
                using (MemoryStream ms = new MemoryStream())
                {
                    image.Save(ms, System.Drawing.Imaging.ImageFormat.Png);
                    imageData = ms.ToArray();
                }
                
                // 创建多部分表单内容
                using (var content = new MultipartFormDataContent())
                {
                    var imageContent = new ByteArrayContent(imageData);
                    imageContent.Headers.ContentType = MediaTypeHeaderValue.Parse("image/png");
                    content.Add(imageContent, "image", "image.png");
                    
                    // 发送POST请求
                    var response = await _httpClient.PostAsync($"{_baseUrl}/battery/detect", content);
                    response.EnsureSuccessStatusCode();
                    
                    // 解析响应
                    var jsonString = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<BatteryDetectionResult>(jsonString);
                }
            }
            catch (Exception ex)
            {
                return new BatteryDetectionResult
                {
                    Success = false,
                    ErrorMessage = $"API调用失败: {ex.Message}"
                };
            }
        }
        

        
        /// <summary>
        /// 获取所有可用的电池样品编号
        /// </summary>
        public async Task<List<string>> GetAllSampleNosAsync()
        {
            try
            {
                // 设置认证头
                SetAuthorizationHeader();
                
                var response = await _httpClient.GetAsync($"{_baseUrl}/battery/sample-nos");
                response.EnsureSuccessStatusCode();
                
                var jsonString = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<string>>(jsonString);
            }
            catch (Exception)
            {
                return new List<string>();
            }
        }
        
        /// <summary>
        /// 获取所有可用的存储天数
        /// </summary>
        public async Task<List<string>> GetAllStorageDaysAsync()
        {
            try
            {
                // 设置认证头
                SetAuthorizationHeader();
                
                var response = await _httpClient.GetAsync($"{_baseUrl}/battery/storage-days");
                response.EnsureSuccessStatusCode();
                
                var jsonString = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<string>>(jsonString);
            }
            catch (Exception)
            {
                return new List<string>();
            }
        }
        
        /// <summary>
        /// 获取所有可用的图片类型
        /// </summary>
        public async Task<List<string>> GetAllPictureTypesAsync()
        {
            try
            {
                // 设置认证头
                SetAuthorizationHeader();
                
                var response = await _httpClient.GetAsync($"{_baseUrl}/battery/picture-types");
                response.EnsureSuccessStatusCode();
                
                var jsonString = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<string>>(jsonString);
            }
            catch (Exception)
            {
                return new List<string>();
            }
        }
        
        /// <summary>
        /// 检查字符串是否为Base64编码的图片
        /// </summary>
        private bool IsBase64Image(string base64String)
        {
            // 检查是否以常见的Base64图片前缀开头
            if (base64String.StartsWith("data:image/"))
            {
                // 移除前缀部分
                int commaIndex = base64String.IndexOf(',');
                if (commaIndex > 0)
                {
                    base64String = base64String.Substring(commaIndex + 1);
                }
            }
            
            // 尝试解码Base64字符串
            try
            {
                Convert.FromBase64String(base64String);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
} 