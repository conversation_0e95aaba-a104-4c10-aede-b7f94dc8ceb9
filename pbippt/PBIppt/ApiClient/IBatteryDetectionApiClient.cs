using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PBIppt.ApiClient
{
    /// <summary>
    /// 电芯检测API客户端接口
    /// </summary>
    public interface IBatteryDetectionApiClient
    {
        /// <summary>
        /// 检测单张图片中的电芯
        /// </summary>
        /// <param name="imageData">图片二进制数据</param>
        /// <param name="fileName">图片文件名</param>
        /// <param name="imageId">图片ID（可选）</param>
        /// <returns>检测结果</returns>
        Task<BatteryDetectionApiResponse> DetectSingleImageAsync(byte[] imageData, string fileName, string imageId = null);

        /// <summary>
        /// 检测多张图片中的电芯
        /// </summary>
        /// <param name="images">图片数据列表</param>
        /// <param name="imageIds">图片ID列表（可选）</param>
        /// <returns>检测结果列表</returns>
        Task<BatteryDetectionApiResponse> DetectMultipleImagesAsync(List<BatteryDetectionImageData> images, List<string> imageIds = null);

        /// <summary>
        /// 设置认证令牌
        /// </summary>
        /// <param name="token">认证令牌</param>
        void SetAuthToken(string token);

        /// <summary>
        /// 测试API连接
        /// </summary>
        /// <returns>连接测试结果</returns>
        Task<bool> TestConnectionAsync();

        /// <summary>
        /// 释放资源
        /// </summary>
        void Dispose();
    }

    /// <summary>
    /// 电芯检测图片数据
    /// </summary>
    public class BatteryDetectionImageData
    {
        /// <summary>
        /// 图片二进制数据
        /// </summary>
        public byte[] ImageData { get; set; }

        /// <summary>
        /// 图片文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 图片ID（可选）
        /// </summary>
        public string ImageId { get; set; }
    }

    /// <summary>
    /// 电芯检测API响应
    /// </summary>
    public class BatteryDetectionApiResponse
    {
        /// <summary>
        /// 检测结果列表
        /// </summary>
        public List<BatteryDetectionApiResult> Results { get; set; } = new List<BatteryDetectionApiResult>();

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// 单个图片的电芯检测结果
    /// </summary>
    public class BatteryDetectionApiResult
    {
        /// <summary>
        /// 图片ID
        /// </summary>
        public string ImageId { get; set; }

        /// <summary>
        /// 裁剪区域左边界（像素）
        /// </summary>
        public int CropLeft { get; set; }

        /// <summary>
        /// 裁剪区域顶部边界（像素）
        /// </summary>
        public int CropTop { get; set; }

        /// <summary>
        /// 裁剪区域右边界（像素）
        /// </summary>
        public int CropRight { get; set; }

        /// <summary>
        /// 裁剪区域底部边界（像素）
        /// </summary>
        public int CropBottom { get; set; }

        /// <summary>
        /// 裁剪区域左边界（百分比坐标，0.0-1.0）
        /// </summary>
        public float CropLeftPercent { get; set; }

        /// <summary>
        /// 裁剪区域顶部边界（百分比坐标，0.0-1.0）
        /// </summary>
        public float CropTopPercent { get; set; }

        /// <summary>
        /// 裁剪区域右边界（百分比坐标，0.0-1.0）
        /// </summary>
        public float CropRightPercent { get; set; }

        /// <summary>
        /// 裁剪区域底部边界（百分比坐标，0.0-1.0）
        /// </summary>
        public float CropBottomPercent { get; set; }

        /// <summary>
        /// 检测区域中心点X坐标（百分比）
        /// </summary>
        public float CenterXPercent { get; set; }

        /// <summary>
        /// 检测区域中心点Y坐标（百分比）
        /// </summary>
        public float CenterYPercent { get; set; }

        /// <summary>
        /// 检测区域宽度（百分比）
        /// </summary>
        public float WidthPercent { get; set; }

        /// <summary>
        /// 检测区域高度（百分比）
        /// </summary>
        public float HeightPercent { get; set; }

        /// <summary>
        /// 检测置信度
        /// </summary>
        public float Confidence { get; set; }

        /// <summary>
        /// 图片宽度（像素）
        /// </summary>
        public int ImageWidth { get; set; }

        /// <summary>
        /// 图片高度（像素）
        /// </summary>
        public int ImageHeight { get; set; }

        /// <summary>
        /// 图片DPI信息（水平DPI, 垂直DPI）
        /// </summary>
        public float[] ImageDpi { get; set; } = new float[] { 96.0f, 96.0f };

        /// <summary>
        /// 是否检测成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息（如果检测失败）
        /// </summary>
        public string ErrorMessage { get; set; }
    }
}
