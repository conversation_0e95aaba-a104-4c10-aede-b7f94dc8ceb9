using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using System.Diagnostics;
using Newtonsoft.Json;
using PBIppt.Models;
using PBIppt.Utils;

namespace PBIppt.ApiClient
{
    /// <summary>
    /// API响应的JSON模型
    /// </summary>
    internal class DetectionApiJsonResponse
    {
        public List<DetectionResultJson> results { get; set; }
    }

    /// <summary>
    /// 检测结果的JSON模型
    /// </summary>
    internal class DetectionResultJson
    {
        public string image_id { get; set; }
        public int? CropLeft { get; set; }
        public int? CropTop { get; set; }
        public int? CropRight { get; set; }
        public int? CropBottom { get; set; }
        public float? CropLeftPercent { get; set; }
        public float? CropTopPercent { get; set; }
        public float? CropRightPercent { get; set; }
        public float? CropBottomPercent { get; set; }
        public float? center_x_percent { get; set; }
        public float? center_y_percent { get; set; }
        public float? width_percent { get; set; }
        public float? height_percent { get; set; }
        public float? confidence { get; set; }
        public ImageInfoJson image_info { get; set; }
    }

    /// <summary>
    /// 图片信息的JSON模型
    /// </summary>
    internal class ImageInfoJson
    {
        public int? width { get; set; }
        public int? height { get; set; }
        public List<float?> dpi { get; set; }
    }

    /// <summary>
    /// 电芯检测API客户端实现
    /// 调用电芯检测接口：http://10.100.1.101:8011/detect/
    /// </summary>
    public class BatteryDetectionApiClient : IBatteryDetectionApiClient, IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private string _authToken;

        /// <summary>
        /// 创建电芯检测API客户端
        /// </summary>
        public BatteryDetectionApiClient()
        {
            _baseUrl = "http://10.100.1.101:8011";
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(30); // 30秒超时

            Debug.WriteLine($"BatteryDetectionApiClient初始化，服务器地址: {_baseUrl}");
        }

        /// <summary>
        /// 检测单张图片中的电芯
        /// </summary>
        /// <param name="imageData">图片二进制数据</param>
        /// <param name="fileName">图片文件名</param>
        /// <param name="imageId">图片ID（可选）</param>
        /// <returns>检测结果</returns>
        public async Task<BatteryDetectionApiResponse> DetectSingleImageAsync(byte[] imageData, string fileName, string imageId = null)
        {
            try
            {
                Debug.WriteLine($"开始检测单张图片: {fileName}, 大小: {imageData?.Length ?? 0} bytes");

                if (imageData == null || imageData.Length == 0)
                {
                    return new BatteryDetectionApiResponse
                    {
                        Success = false,
                        ErrorMessage = "图片数据为空"
                    };
                }

                // 创建multipart/form-data请求
                using (var formData = new MultipartFormDataContent())
                {
                    // 添加图片文件
                    var imageContent = new ByteArrayContent(imageData);
                    imageContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("image/jpeg");
                    formData.Add(imageContent, "images", fileName ?? "image.jpg");

                    // 添加图片ID（如果提供）
                    if (!string.IsNullOrEmpty(imageId))
                    {
                        var imageIds = new List<string> { imageId };
                        var imageIdsJson = JsonConvert.SerializeObject(imageIds);
                        formData.Add(new StringContent(imageIdsJson), "image_ids_json");
                        Debug.WriteLine($"添加图片ID: {imageIdsJson}");
                    }

                    // 发送请求
                    var requestUrl = $"{_baseUrl}/detect/";
                    Debug.WriteLine($"发送检测请求到: {requestUrl}");

                    var response = await _httpClient.PostAsync(requestUrl, formData);
                    var responseContent = await response.Content.ReadAsStringAsync();

                    Debug.WriteLine($"服务器响应状态: {response.StatusCode}");
                    Debug.WriteLine($"响应内容: {responseContent}");

                    if (!response.IsSuccessStatusCode)
                    {
                        return new BatteryDetectionApiResponse
                        {
                            Success = false,
                            ErrorMessage = $"服务器返回错误: {response.StatusCode}, 内容: {responseContent}"
                        };
                    }

                    // 解析响应
                    var apiResponse = ParseDetectionResponse(responseContent);
                    Debug.WriteLine($"检测完成，成功: {apiResponse.Success}");

                    return apiResponse;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"检测图片时发生错误: {ex.Message}");
                Logger.Exception(ex);
                
                return new BatteryDetectionApiResponse
                {
                    Success = false,
                    ErrorMessage = $"检测过程中发生错误: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 检测多张图片中的电芯
        /// </summary>
        /// <param name="images">图片数据列表</param>
        /// <param name="imageIds">图片ID列表（可选）</param>
        /// <returns>检测结果列表</returns>
        public async Task<BatteryDetectionApiResponse> DetectMultipleImagesAsync(List<BatteryDetectionImageData> images, List<string> imageIds = null)
        {
            try
            {
                Debug.WriteLine($"开始检测多张图片，数量: {images?.Count ?? 0}");

                if (images == null || images.Count == 0)
                {
                    return new BatteryDetectionApiResponse
                    {
                        Success = false,
                        ErrorMessage = "图片列表为空"
                    };
                }

                // 创建multipart/form-data请求
                using (var formData = new MultipartFormDataContent())
                {
                    // 添加所有图片文件
                    for (int i = 0; i < images.Count; i++)
                    {
                        var image = images[i];
                        if (image.ImageData != null && image.ImageData.Length > 0)
                        {
                            var imageContent = new ByteArrayContent(image.ImageData);
                            imageContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("image/jpeg");
                            formData.Add(imageContent, "images", image.FileName ?? $"image_{i}.jpg");
                        }
                    }

                    // 从图片数据中提取图片ID列表
                    var extractedImageIds = new List<string>();
                    for (int i = 0; i < images.Count; i++)
                    {
                        var image = images[i];
                        // 优先使用传入的imageIds，然后使用图片数据中的ImageId，最后使用默认值
                        string imageId = null;

                        if (imageIds != null && i < imageIds.Count)
                        {
                            imageId = imageIds[i];
                        }
                        else if (!string.IsNullOrEmpty(image.ImageId))
                        {
                            imageId = image.ImageId;
                        }
                        else
                        {
                            imageId = $"image_{i}";
                        }

                        extractedImageIds.Add(imageId);
                    }

                    // 添加图片ID列表到请求
                    if (extractedImageIds.Count > 0)
                    {
                        var imageIdsJson = JsonConvert.SerializeObject(extractedImageIds);
                        formData.Add(new StringContent(imageIdsJson), "image_ids_json");
                        Debug.WriteLine($"添加图片ID列表: {imageIdsJson}");
                    }

                    // 发送请求
                    var requestUrl = $"{_baseUrl}/detect/";
                    Debug.WriteLine($"发送批量检测请求到: {requestUrl}");

                    var response = await _httpClient.PostAsync(requestUrl, formData);
                    var responseContent = await response.Content.ReadAsStringAsync();

                    Debug.WriteLine($"服务器响应状态: {response.StatusCode}");
                    Debug.WriteLine($"=== API原始响应内容 ===");
                    Debug.WriteLine(responseContent);
                    Debug.WriteLine($"=== API响应内容结束 ===");

                    if (!response.IsSuccessStatusCode)
                    {
                        return new BatteryDetectionApiResponse
                        {
                            Success = false,
                            ErrorMessage = $"服务器返回错误: {response.StatusCode}, 内容: {responseContent}"
                        };
                    }

                    // 解析响应
                    var apiResponse = ParseDetectionResponse(responseContent);
                    Debug.WriteLine($"批量检测完成，成功: {apiResponse.Success}, 结果数量: {apiResponse.Results?.Count ?? 0}");

                    return apiResponse;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"批量检测图片时发生错误: {ex.Message}");
                Logger.Exception(ex);
                
                return new BatteryDetectionApiResponse
                {
                    Success = false,
                    ErrorMessage = $"批量检测过程中发生错误: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 设置认证令牌
        /// </summary>
        /// <param name="token">认证令牌</param>
        public void SetAuthToken(string token)
        {
            _authToken = token;
            // 注意：电芯检测API可能不需要认证，这里预留接口
            Debug.WriteLine("设置认证令牌（电芯检测API可能不需要认证）");
        }

        /// <summary>
        /// 测试API连接
        /// </summary>
        /// <returns>连接测试结果</returns>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                Debug.WriteLine($"测试连接到电芯检测服务器: {_baseUrl}");
                
                // 尝试访问根路径
                var response = await _httpClient.GetAsync(_baseUrl);
                
                Debug.WriteLine($"连接测试响应状态: {response.StatusCode}");
                
                // 即使返回404也表示服务器可达
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"连接测试失败: {ex.Message}");
                Logger.Exception(ex);
                return false;
            }
        }

        /// <summary>
        /// 解析检测响应
        /// </summary>
        /// <param name="responseContent">响应内容</param>
        /// <returns>解析后的检测结果</returns>
        private BatteryDetectionApiResponse ParseDetectionResponse(string responseContent)
        {
            try
            {
                // 根据接口文档解析响应格式，使用强类型模型
                var jsonResponse = JsonConvert.DeserializeObject<DetectionApiJsonResponse>(responseContent);

                var response = new BatteryDetectionApiResponse
                {
                    Success = true,
                    Results = new List<BatteryDetectionApiResult>()
                };

                // 解析results数组
                if (jsonResponse?.results != null)
                {
                    foreach (var result in jsonResponse.results)
                    {
                        var detectionResult = new BatteryDetectionApiResult
                        {
                            ImageId = result.image_id,
                            CropLeft = result.CropLeft ?? 0,
                            CropTop = result.CropTop ?? 0,
                            CropRight = result.CropRight ?? 0,
                            CropBottom = result.CropBottom ?? 0,
                            CropLeftPercent = result.CropLeftPercent ?? 0.0f,
                            CropTopPercent = result.CropTopPercent ?? 0.0f,
                            CropRightPercent = result.CropRightPercent ?? 0.0f,
                            CropBottomPercent = result.CropBottomPercent ?? 0.0f,
                            CenterXPercent = result.center_x_percent ?? 0.0f,
                            CenterYPercent = result.center_y_percent ?? 0.0f,
                            WidthPercent = result.width_percent ?? 0.0f,
                            HeightPercent = result.height_percent ?? 0.0f,
                            Confidence = result.confidence ?? 0.0f,
                            ImageWidth = result.image_info?.width ?? 0,
                            ImageHeight = result.image_info?.height ?? 0,
                            Success = true
                        };

                        // 调试：验证image_info解析
                        Debug.WriteLine($"解析image_info - 图片ID: {detectionResult.ImageId}, 尺寸: {detectionResult.ImageWidth}x{detectionResult.ImageHeight}");

                        // 解析DPI信息
                        if (result.image_info?.dpi != null && result.image_info.dpi.Count >= 2)
                        {
                            try
                            {
                                detectionResult.ImageDpi = new float[]
                                {
                                    result.image_info.dpi[0] ?? 96.0f,
                                    result.image_info.dpi[1] ?? 96.0f
                                };
                                Debug.WriteLine($"解析到DPI信息: {detectionResult.ImageDpi[0]}x{detectionResult.ImageDpi[1]}");
                            }
                            catch (Exception dpiEx)
                            {
                                Debug.WriteLine($"解析DPI信息失败: {dpiEx.Message}，使用默认值96x96");
                                detectionResult.ImageDpi = new float[] { 96.0f, 96.0f };
                            }
                        }
                        else
                        {
                            // 如果没有DPI信息，使用默认值
                            detectionResult.ImageDpi = new float[] { 96.0f, 96.0f };
                        }

                        response.Results.Add(detectionResult);
                        Debug.WriteLine($"解析检测结果 - ImageId: {detectionResult.ImageId}, " +
                                      $"裁剪区域: ({detectionResult.CropLeft}, {detectionResult.CropTop}, " +
                                      $"{detectionResult.CropRight}, {detectionResult.CropBottom}), " +
                                      $"百分比: ({detectionResult.CropLeftPercent:F3}, {detectionResult.CropTopPercent:F3}, " +
                                      $"{detectionResult.CropRightPercent:F3}, {detectionResult.CropBottomPercent:F3}), " +
                                      $"DPI: {detectionResult.ImageDpi[0]}x{detectionResult.ImageDpi[1]}");
                    }
                }

                return response;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"解析检测响应失败: {ex.Message}");
                Logger.Exception(ex);
                
                return new BatteryDetectionApiResponse
                {
                    Success = false,
                    ErrorMessage = $"解析响应失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
