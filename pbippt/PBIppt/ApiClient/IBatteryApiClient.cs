using System;
using System.Collections.Generic;
using System.Drawing;
using System.Threading.Tasks;
using PBIppt.Models;

namespace PBIppt.ApiClient
{
    /// <summary>
    /// 电池API客户端接口
    /// </summary>
    public interface IBatteryApiClient
    {
        /// <summary>
        /// 检测图片中的电池
        /// </summary>
        /// <param name="image">要分析的图片</param>
        /// <returns>检测结果</returns>
        Task<BatteryDetectionResult> DetectBatteriesAsync(Image image);



        /// <summary>
        /// 获取所有可用的电池样品编号
        /// </summary>
        /// <returns>样品编号列表</returns>
        Task<List<string>> GetAllSampleNosAsync();

        /// <summary>
        /// 获取所有可用的存储天数
        /// </summary>
        /// <returns>存储天数列表</returns>
        Task<List<string>> GetAllStorageDaysAsync();

        /// <summary>
        /// 获取所有可用的图片类型
        /// </summary>
        /// <returns>图片类型列表</returns>
        Task<List<string>> GetAllPictureTypesAsync();
    }
}