using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Net.Http;
using Newtonsoft.Json.Linq;
using PBIppt.Models;

namespace PBIppt.ApiClient
{
    /// <summary>
    /// 电池图片API客户端接口（旧API）
    /// </summary>
    public interface IBatteryImageApiClient
    {
        /// <summary>
        /// 基础URL
        /// </summary>
        string BaseUrl { get; }
        // 登录功能已移至 AuthService，此接口专注于业务API

        /// <summary>
        /// 获取当前登录用户信息（包含角色信息）
        /// </summary>
        /// <returns>用户信息</returns>
        Task<UserInfo> GetCurrentUserAsync();
        

        
        /// <summary>
        /// 获取可用的存储天数列表
        /// </summary>
        /// <param name="progressId">测试进度ID</param>
        /// <returns>存储天数列表</returns>
        Task<List<string>> GetAvailableTotalDaysAsync(long progressId);
        
        /// <summary>
        /// 获取样品编号列表
        /// </summary>
        /// <param name="progressId">测试进度ID</param>
        /// <returns>样品编号列表</returns>
        Task<List<string>> GetSampleNoListAsync(long progressId);
        
        /// <summary>
        /// 获取图片类型列表
        /// </summary>
        /// <param name="sampleType">样品类型</param>
        /// <returns>图片类型列表</returns>
        Task<List<string>> GetPictureTypesBySampleTypeAsync(string sampleType);
        
        /// <summary>
        /// 获取Minio文件下载URL
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="fileName">文件名称</param>
        /// <returns>下载URL</returns>
        Task<string> GetMinioDownloadUrlAsync(string fileId, string fileName);
        
        /// <summary>
        /// 获取Minio文件预览URL
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <returns>预览URL</returns>
        Task<string> GetMinioPreviewUrlAsync(string fileId);
        
        /// <summary>
        /// 下载图片数据
        /// </summary>
        /// <param name="url">图片URL</param>
        /// <returns>图片二进制数据</returns>
        Task<byte[]> DownloadImageDataAsync(string url);

        /// <summary>
        /// 根据文件ID下载图片数据
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="mode">预览模式，默认为little</param>
        /// <returns>图片二进制数据</returns>
        Task<byte[]> DownloadImageDataByFileIdAsync(string fileId, string mode = "little");

        /// <summary>
        /// 获取Minio文件预览URL（参照Vue前端实现）
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <returns>预览URL</returns>
        Task<string> GetMinioFileUrlAsync(string fileId);

        /// <summary>
        /// 获取Minio文件下载URL（参照Vue前端实现）
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="fileName">文件名</param>
        /// <returns>下载URL</returns>
        Task<string> GetMinioFileDownloadUrlAsync(string fileId, string fileName = null);
        
        /// <summary>
        /// 设置认证令牌
        /// </summary>
        /// <param name="token">认证令牌</param>
        void SetAuthToken(string token);

        /// <summary>
        /// 测试token是否有效
        /// </summary>
        /// <returns>测试结果</returns>
        Task<bool> TestTokenValidityAsync();

        /// <summary>
        /// 检查AuthManager中的令牌状态
        /// </summary>
        /// <returns>令牌状态信息</returns>
        string CheckAuthManagerTokenStatus();
        
        /// <summary>
        /// 测试查询API
        /// </summary>
        /// <returns>测试结果信息</returns>
        Task<string> TestQueryApiAsync();
        
        /// <summary>
        /// 诊断登录API，尝试多种参数组合
        /// </summary>
        /// <param name="account">账号</param>
        /// <param name="password">密码</param>
        /// <returns>诊断结果</returns>
        Task<string> DiagnoseLoginApiAsync(string account, string password);

        /// <summary>
        /// 发送带Token认证的请求
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="url">请求URL</param>
        /// <param name="method">HTTP方法</param>
        /// <param name="data">请求数据</param>
        /// <returns>响应数据</returns>
        Task<T> SendWithTokenAuthAsync<T>(string url, HttpMethod method, object data = null);

        /// <summary>
        /// 下载图片
        /// </summary>
        /// <param name="url">图片URL</param>
        /// <returns>图片二进制数据</returns>
        Task<byte[]> DownloadImageAsync(string url);
    }
}