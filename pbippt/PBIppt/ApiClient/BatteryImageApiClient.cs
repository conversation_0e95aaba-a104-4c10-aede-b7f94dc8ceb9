using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PBIppt.Models;
using System.Diagnostics;

namespace PBIppt.ApiClient
{
    /// <summary>
    /// 电池图片API客户端实现
    /// </summary>
    public class BatteryImageApiClient : IBatteryImageApiClient
    {
        private HttpClient _httpClient;
        private readonly string _baseUrl;
        private string _token;

        /// <summary>
        /// 基础URL
        /// </summary>
        public string BaseUrl => _baseUrl;
        
        /// <summary>
        /// 创建电池图片API客户端，使用默认服务器地址
        /// </summary>
        public BatteryImageApiClient()
        {
            // 设置基础URL（不含/api路径）
            _baseUrl = "http://localhost:82";
            Debug.WriteLine($"BatteryImageApiClient初始化，使用默认地址: {_baseUrl}");
            InitializeHttpClient();
        }
 
        /// <summary>
        /// 创建电池图片API客户端，使用指定的服务器地址
        /// </summary>
        /// <param name="baseUrl">API服务器基础地址</param>
        public BatteryImageApiClient(string baseUrl)
        {
            // 设置基础URL（不含/api路径）
            _baseUrl = baseUrl;
            Debug.WriteLine($"BatteryImageApiClient初始化，使用指定地址: {_baseUrl}");
            InitializeHttpClient();
        }
        
        /// <summary>
        /// 初始化HTTP客户端
        /// </summary>
        private void InitializeHttpClient()
        {
            try
            {
                // 如果已有HttpClient实例，先释放
                if (_httpClient != null)
                {
                    _httpClient.Dispose();
                }

                _httpClient = new HttpClient();

                // 设置超时时间 - 与Vue前端一致 (10000000毫秒)
                _httpClient.Timeout = TimeSpan.FromMilliseconds(10000000);

                // 设置默认请求头
                _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                // 设置User-Agent
                _httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("PBIppt-Client/1.0");

                // 添加Vue前端使用的请求头
                _httpClient.DefaultRequestHeaders.Add("X-Requested-With", "XMLHttpRequest");

                // 确保没有Authorization头（登录时不应该有认证头）
                _httpClient.DefaultRequestHeaders.Authorization = null;

                Debug.WriteLine("HttpClient初始化成功，已清除认证头");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"HttpClient初始化失败: {ex.Message}");
                throw;
            }
        }

        // 登录功能已移至 AuthService，此类专注于业务API
        // 如需登录，请使用 PBIppt.Services.AuthService

        /// <summary>
        /// 设置认证令牌
        /// </summary>
        public void SetAuthToken(string token)
        {
            if (string.IsNullOrEmpty(token))
            {
                throw new ArgumentException("令牌不能为空", nameof(token));
            }

            _token = token;

            // 确保HttpClient实例可用
            if (_httpClient == null)
            {
                InitializeHttpClient();
            }

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _token);
            Debug.WriteLine("令牌已设置到BatteryImageApiClient");
        }
        
        /// <summary>
        /// 确保已设置令牌
        /// </summary>
        private void EnsureTokenSet()
        {
            if (string.IsNullOrEmpty(_token))
            {
                throw new UnauthorizedAccessException("未设置认证令牌，请先调用SetAuthToken方法或登录");
            }
            
            // 确保HttpClient实例可用
            if (_httpClient == null)
            {
                InitializeHttpClient();
            }
            
            // 清除所有现有的Authorization头
            if (_httpClient.DefaultRequestHeaders.Contains("Authorization"))
            {
                _httpClient.DefaultRequestHeaders.Remove("Authorization");
            }
            
            // 获取最新的token
            if (AuthManager.IsLoggedIn && !string.IsNullOrEmpty(AuthManager.CurrentAuth?.AccessToken))
            {
                _token = AuthManager.CurrentAuth.AccessToken;
                Debug.WriteLine($"使用AuthManager中的最新token: {_token.Substring(0, Math.Min(10, _token.Length))}...");
            }
            
            // 设置Authorization头 - 这个项目只需要JWT token，不需要JiraAuth
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _token);
            Debug.WriteLine($"设置Authorization头: Bearer {_token.Substring(0, Math.Min(10, _token.Length))}...");

            // 添加XMLHttpRequest头 - Vue前端也使用这个
            if (!_httpClient.DefaultRequestHeaders.Contains("X-Requested-With"))
            {
                _httpClient.DefaultRequestHeaders.Add("X-Requested-With", "XMLHttpRequest");
                Debug.WriteLine("添加X-Requested-With: XMLHttpRequest头");
            }
        }
        
        /// <summary>
        /// 使用安全测试API获取图片数据（已弃用，仅保留以维持兼容性）
        /// </summary>
        [Obsolete("此方法已弃用，请使用新的PPT表格API")]
        public async Task<object> GetPictureData(TestProgress testProgress, string totalDay)
        {
            try
            {
                // 确保已设置令牌
                EnsureTokenSet();
                
                // 确保参数不为null
                if (testProgress == null)
                {
                    return new { error = "测试进度参数不能为null" };
                }
                
                // 确保totalDay不为null，如果为null则使用默认值"all"
                string safeTotalDay = totalDay ?? "all";
                Debug.WriteLine($"存储天数参数: {safeTotalDay}");
                
                // 使用安全测试ID作为参数（从SampleNo获取）
                string safetyTestIds = testProgress.SampleNo ?? testProgress.Id.ToString();
                Debug.WriteLine($"使用安全测试ID: {safetyTestIds}");

                // 使用正确的安全测试API端点
                var url = $"{_baseUrl}/safetyTest/getPictureBySafetyTestIds";
                Debug.WriteLine($"获取图片数据URL: {url}");

                // 构造请求参数（参照Vue前端）
                var requestParam = new {
                    safetyTestIds = safetyTestIds,
                    cycleTime = safeTotalDay
                };

                // 使用安全测试API发送请求，先获取原始响应
                var rawResponse = await SendWithTokenAuthAsync<object>(url, HttpMethod.Post, requestParam);

                // 直接返回原始响应数据
                if (rawResponse != null)
                {
                    Debug.WriteLine("成功获取图片数据");
                    return rawResponse;
                }

                // 如果所有方法都失败，返回错误响应
                return new { error = "尝试了多种认证方式，均无法获取数据，请检查服务器配置" };
            }
            catch (UnauthorizedAccessException)
            {
                // 重新抛出未授权异常，以便上层处理登录
                throw;
            }
            catch (Exception ex)
            {
                // 处理其他异常
                return new { error = $"获取图片数据时发生错误: {ex.Message}" };
            }
        }





        
        public async Task<List<string>> GetAvailableTotalDaysAsync(long progressId)
        {
            try
            {
                // 确保已设置令牌
                EnsureTokenSet();

                // 使用正确的安全测试API端点
                var url = $"{_baseUrl}/safetyTest/getPictureBySafetyTestIds";

                // 构造安全测试请求参数（使用progressId作为safetyTestIds）
                var requestParam = new {
                    safetyTestIds = progressId.ToString(),
                    cycleTime = "all"
                };
                var jsonContent = JsonConvert.SerializeObject(requestParam);

                HttpResponseMessage response;
                using (var content = new StringContent(jsonContent, Encoding.UTF8, "application/json"))
                {
                    response = await _httpClient.PostAsync(url, content);
                }

                // 检查响应状态码
                if (!response.IsSuccessStatusCode)
                {
                    Debug.WriteLine($"API调用失败，状态码: {response.StatusCode}");
                    return new List<string>();
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonConvert.DeserializeObject<ResponseData>(responseContent);

                if (!apiResponse.Success)
                {
                    Debug.WriteLine($"API返回失败: {apiResponse.Message}");
                    return new List<string>();
                }

                // 从返回数据中提取allPicStageList（安全测试中对应的字段）
                if (apiResponse.Data != null)
                {
                    var dataObject = JObject.FromObject(apiResponse.Data);
                    var allPicStageList = dataObject["allPicStageList"]?.ToObject<List<string>>();

                    if (allPicStageList != null)
                    {
                        Debug.WriteLine($"成功获取可用循环时间: {string.Join(", ", allPicStageList)}");
                        return allPicStageList;
                    }
                }

                Debug.WriteLine("未找到allPicStageList数据");
                return new List<string>();
            }
            catch (UnauthorizedAccessException)
            {
                // 重新抛出未授权异常，以便上层处理登录
                throw;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取可用循环时间失败: {ex.Message}");
                return new List<string>();
            }
        }
        
        public async Task<List<string>> GetSampleNoListAsync(long progressId)
        {
            try
            {
                // 确保已设置令牌
                EnsureTokenSet();

                // 使用正确的安全测试API端点
                var url = $"{_baseUrl}/safetyTest/getPictureBySafetyTestIds";

                // 构造安全测试请求参数（使用progressId作为safetyTestIds）
                var requestParam = new {
                    safetyTestIds = progressId.ToString(),
                    cycleTime = "all"
                };
                var jsonContent = JsonConvert.SerializeObject(requestParam);

                HttpResponseMessage response;
                using (var content = new StringContent(jsonContent, Encoding.UTF8, "application/json"))
                {
                    response = await _httpClient.PostAsync(url, content);
                }

                // 检查响应状态码
                if (!response.IsSuccessStatusCode)
                {
                    Debug.WriteLine($"API调用失败，状态码: {response.StatusCode}");
                    return new List<string>();
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonConvert.DeserializeObject<ResponseData>(responseContent);

                if (!apiResponse.Success)
                {
                    Debug.WriteLine($"API返回失败: {apiResponse.Message}");
                    return new List<string>();
                }

                // 从返回数据中提取allSampleNoList
                if (apiResponse.Data != null)
                {
                    var dataObject = JObject.FromObject(apiResponse.Data);
                    var allSampleNoList = dataObject["allSampleNoList"]?.ToObject<List<string>>();

                    if (allSampleNoList != null)
                    {
                        Debug.WriteLine($"成功获取样品编号列表: {string.Join(", ", allSampleNoList)}");
                        return allSampleNoList;
                    }
                }

                Debug.WriteLine("未找到allSampleNoList数据");
                return new List<string>();
            }
            catch (UnauthorizedAccessException)
            {
                // 重新抛出未授权异常，以便上层处理登录
                throw;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取样品编号列表失败: {ex.Message}");
                return new List<string>();
            }
        }
        
        public async Task<List<string>> GetPictureTypesBySampleTypeAsync(string sampleType)
        {
            try
            {
                // 根据样品类型返回对应的图片类型列表（本地计算，与后端逻辑保持一致）
                var pictureTypes = GetPictureTypesBySampleTypeLocal(sampleType);
                Debug.WriteLine($"根据样品类型 '{sampleType}' 获取到 {pictureTypes.Count} 种图片类型");
                return await Task.FromResult(pictureTypes);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取图片类型时出错: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// 本地计算图片类型列表（与后端逻辑保持一致）
        /// </summary>
        private List<string> GetPictureTypesBySampleTypeLocal(string sampleType)
        {
            var pictureTypes = new List<string>();

            switch (sampleType)
            {
                case "方型":
                    pictureTypes.AddRange(new[] { "顶部", "底部", "大面1", "大面2", "侧面1", "侧面2", "工装装配顶部", "工装装配正面", "工装装配整体" });
                    break;
                case "软包":
                    pictureTypes.AddRange(new[] { "大面1", "大面2", "工装装配顶部", "工装装配正面", "工装装配整体" });
                    break;
                case "模组":
                    pictureTypes.AddRange(new[] { "顶部", "底部", "大面1", "大面2", "侧面1", "侧面2" });
                    break;
                default:
                    // G/C/V圆柱
                    pictureTypes.AddRange(new[] { "端子面", "vent面", "大面1", "大面2", "工装装配顶部", "工装装配正面", "工装装配整体" });
                    break;
            }

            return pictureTypes;
        }

        /// <summary>
        /// 测试安全测试ID是否存在
        /// </summary>
        /// <param name="safetyTestIds">安全测试ID列表（逗号分隔）</param>
        /// <returns>是否存在</returns>
        public async Task<bool> TestSafetyTestIdsExistAsync(string safetyTestIds)
        {
            try
            {
                // 确保已设置令牌
                EnsureTokenSet();

                var url = $"{_baseUrl}/safetyTest/getPictureBySafetyTestIds";

                // 构造请求参数
                var requestParam = new {
                    safetyTestIds = safetyTestIds,
                    cycleTime = "all"
                };
                var jsonContent = JsonConvert.SerializeObject(requestParam);

                HttpResponseMessage response;
                using (var content = new StringContent(jsonContent, Encoding.UTF8, "application/json"))
                {
                    response = await _httpClient.PostAsync(url, content);
                }

                if (!response.IsSuccessStatusCode)
                {
                    return false;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonConvert.DeserializeObject<ResponseData>(responseContent);

                // 如果返回成功，说明ID存在
                // 如果返回"查询安全测试结果失败"，说明ID不存在
                return apiResponse.Success;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取安全测试图片数据（新增方法，使用正确的API）
        /// </summary>
        /// <param name="safetyTestIds">安全测试ID列表（逗号分隔）</param>
        /// <param name="cycleTime">循环时间（"all" 或具体时间）</param>
        /// <returns>图片数据响应</returns>
        public async Task<ResponseData> GetPictureBySafetyTestIdsAsync(string safetyTestIds, string cycleTime = "all")
        {
            try
            {
                // 确保已设置令牌
                EnsureTokenSet();

                var url = $"{_baseUrl}/safetyTest/getPictureBySafetyTestIds";

                // 构造请求参数
                var requestParam = new {
                    safetyTestIds = safetyTestIds,
                    cycleTime = cycleTime
                };
                var jsonContent = JsonConvert.SerializeObject(requestParam);

                Debug.WriteLine($"调用安全测试图片API: {url}");
                Debug.WriteLine($"请求参数: {jsonContent}");

                HttpResponseMessage response;
                using (var content = new StringContent(jsonContent, Encoding.UTF8, "application/json"))
                {
                    response = await _httpClient.PostAsync(url, content);
                }

                // 检查响应状态码
                if (!response.IsSuccessStatusCode)
                {
                    Debug.WriteLine($"API调用失败，状态码: {response.StatusCode}");
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Debug.WriteLine($"错误内容: {errorContent}");
                    return new ResponseData { Success = false, Message = $"HTTP错误: {response.StatusCode}" };
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                Debug.WriteLine($"API响应: {responseContent.Substring(0, Math.Min(200, responseContent.Length))}...");

                var apiResponse = JsonConvert.DeserializeObject<ResponseData>(responseContent);

                if (!apiResponse.Success)
                {
                    Debug.WriteLine($"API返回失败: {apiResponse.Message}");
                }
                else
                {
                    Debug.WriteLine("API调用成功");
                }

                return apiResponse;
            }
            catch (UnauthorizedAccessException)
            {
                // 重新抛出未授权异常，以便上层处理登录
                throw;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取安全测试图片数据失败: {ex.Message}");
                return new ResponseData { Success = false, Message = ex.Message };
            }
        }
        
        /// <summary>
        /// 测试与服务器的连接
        /// </summary>
        /// <returns>连接测试结果</returns>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                Debug.WriteLine($"测试连接到服务器: {_baseUrl}");
                
                // 确保HttpClient实例可用
                if (_httpClient == null)
                {
                    Debug.WriteLine("HttpClient为null，重新初始化");
                    InitializeHttpClient();
                }
                
                // 尝试访问服务器根路径或健康检查端点
                var response = await _httpClient.GetAsync(_baseUrl);
                
                Debug.WriteLine($"服务器响应状态码: {response.StatusCode}");
                
                // 即使返回404也表示服务器可达，只要不是网络级别的错误
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"连接测试失败: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"内部异常: {ex.InnerException.Message}");
                }
                Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                return false;
            }
        }
        
        /// <summary>
        /// 获取服务器信息
        /// </summary>
        /// <returns>服务器信息</returns>
        public string GetServerInfo()
        {
            return $"服务器地址: {_baseUrl}\n" +
                   $"HttpClient已初始化: {_httpClient != null}\n" +
                   $"已设置认证令牌: {!string.IsNullOrEmpty(_token)}";
        }
        
        /// <summary>
        /// 测试登录API是否可用
        /// </summary>
        /// <returns>测试结果</returns>
        public async Task<string> TestLoginApiAsync()
        {
            try
            {
                Debug.WriteLine($"测试登录API: {_baseUrl}/login");
                
                // 确保HttpClient实例可用
                if (_httpClient == null)
                {
                    Debug.WriteLine("HttpClient为null，重新初始化");
                    InitializeHttpClient();
                }
                
                // 创建一个简单的OPTIONS请求，检查API是否可用
                var request = new HttpRequestMessage(HttpMethod.Options, $"{_baseUrl}/login");
                var response = await _httpClient.SendAsync(request);
                
                Debug.WriteLine($"登录API响应状态码: {response.StatusCode}");
                
                // 如果服务器不支持OPTIONS，尝试使用HEAD请求
                if ((int)response.StatusCode >= 400)
                {
                    Debug.WriteLine("OPTIONS请求失败，尝试使用HEAD请求");
                    request = new HttpRequestMessage(HttpMethod.Head, $"{_baseUrl}/login");
                    response = await _httpClient.SendAsync(request);
                    Debug.WriteLine($"HEAD请求响应状态码: {response.StatusCode}");
                }
                
                // 返回测试结果
                return $"登录API测试结果: {response.StatusCode}";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"登录API测试失败: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"内部异常: {ex.InnerException.Message}");
                }
                Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                return $"登录API测试失败: {ex.Message}";
            }
        }
        
        /// <summary>
        /// 检查AuthManager中的令牌状态
        /// </summary>
        /// <returns>令牌状态信息</returns>
        public string CheckAuthManagerTokenStatus()
        {
            bool isLoggedIn = AuthManager.IsLoggedIn;
            string token = AuthManager.CurrentAuth?.AccessToken ?? "null";
            string tokenShort = !string.IsNullOrEmpty(token) && token != "null" 
                ? token.Substring(0, Math.Min(10, token.Length)) + "..." 
                : "null";
            
            return $"AuthManager.IsLoggedIn: {isLoggedIn}, Token: {tokenShort}";
        }
        
        /// <summary>
        /// 检查TestProgress对象的完整性
        /// </summary>
        /// <param name="testProgress">测试进度对象</param>
        /// <returns>检查结果</returns>
        public string CheckTestProgressObject(TestProgress testProgress)
        {
            if (testProgress == null)
            {
                return "TestProgress对象为null";
            }
            
            StringBuilder sb = new StringBuilder();
            sb.AppendLine("TestProgress对象检查:");
            sb.AppendLine($"- ID: {testProgress.Id}");
            sb.AppendLine($"- SampleNo: {testProgress.SampleNo ?? "null"}");
            sb.AppendLine($"- SampleType: {testProgress.SampleType ?? "null"}");
            sb.AppendLine($"- 其他属性检查...");
            
            try
            {
                // 尝试序列化对象，检查是否有序列化问题
                var json = JsonConvert.SerializeObject(testProgress);
                sb.AppendLine($"- 序列化结果: {(json.Length > 100 ? json.Substring(0, 100) + "..." : json)}");
                sb.AppendLine("序列化正常，无明显问题");
            }
            catch (Exception ex)
            {
                sb.AppendLine($"- 序列化出错: {ex.Message}");
            }
            
            return sb.ToString();
        }
        
        /// <summary>
        /// 获取当前登录用户信息（包含角色信息）
        /// </summary>
        /// <returns>用户信息</returns>
        public async Task<UserInfo> GetCurrentUserAsync()
        {
            try
            {
                // 确保已设置令牌
                if (string.IsNullOrEmpty(_token))
                {
                    throw new InvalidOperationException("用户未登录，无法获取用户信息");
                }

                Debug.WriteLine("开始获取当前用户信息");

                // 构建请求URL
                var requestUrl = $"{_baseUrl}/getLoginUser";
                Debug.WriteLine($"用户信息请求URL: {requestUrl}");

                // 确保Authorization头已设置
                if (_httpClient.DefaultRequestHeaders.Authorization == null)
                {
                    _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _token);
                }

                // 发送GET请求
                var response = await _httpClient.GetAsync(requestUrl);
                var responseContent = await response.Content.ReadAsStringAsync();

                Debug.WriteLine($"用户信息响应状态码: {response.StatusCode}");
                Debug.WriteLine($"用户信息响应内容: {responseContent}");

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"获取用户信息失败，状态码: {response.StatusCode}");
                }

                // 解析响应
                var apiResponse = ResponseData.FromJson(responseContent);
                if (!apiResponse.Success)
                {
                    throw new InvalidOperationException($"获取用户信息失败: {apiResponse.Message}");
                }

                // 解析用户信息
                if (apiResponse.Data is JObject userData)
                {
                    var userInfo = new UserInfo
                    {
                        Id = userData["id"]?.Value<long>() ?? 0,
                        Account = userData["account"]?.ToString() ?? "",
                        Name = userData["name"]?.ToString() ?? "",
                        AdminType = userData["adminType"]?.Value<int>() ?? 0,
                        Roles = new List<UserRole>()
                    };

                    // 解析角色信息
                    if (userData["roles"] is JArray rolesArray)
                    {
                        foreach (var roleToken in rolesArray)
                        {
                            if (roleToken is JObject roleObj)
                            {
                                var role = new UserRole
                                {
                                    Id = roleObj["id"]?.Value<long>() ?? 0,
                                    Name = roleObj["name"]?.ToString() ?? "",
                                    Code = roleObj["code"]?.ToString() ?? ""
                                };
                                userInfo.Roles.Add(role);
                                Debug.WriteLine($"解析到用户角色: ID={role.Id}, Name={role.Name}, Code={role.Code}");
                            }
                        }
                    }

                    Debug.WriteLine($"成功获取用户信息: {userInfo.Account}, 角色数量: {userInfo.Roles.Count}");
                    return userInfo;
                }
                else
                {
                    throw new InvalidOperationException("无法解析用户信息数据");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取用户信息时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 测试token是否有效
        /// </summary>
        /// <returns>测试结果</returns>
        public async Task<bool> TestTokenValidityAsync()
        {
            try
            {
                // 确保已设置令牌
                if (string.IsNullOrEmpty(_token))
                {
                    Debug.WriteLine("Token为空，无法测试有效性");
                    return false;
                }
                
                // 确保HttpClient实例可用
                if (_httpClient == null)
                {
                    Debug.WriteLine("HttpClient为null，重新初始化");
                    InitializeHttpClient();
                }
                
                // 确保Authorization头部已设置
                if (_httpClient.DefaultRequestHeaders.Authorization == null || 
                    string.IsNullOrEmpty(_httpClient.DefaultRequestHeaders.Authorization.Parameter))
                {
                    _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _token);
                }
                
                // 尝试访问需要认证的API - 使用正确的安全测试API端点
                var url = $"{_baseUrl}/safetyTest/getPictureBySafetyTestIds";
                Debug.WriteLine($"测试Token有效性: {url}");

                // 构造安全测试请求参数（使用真实的安全测试ID）
                var requestParam = new {
                    safetyTestIds = "1907295047497265154",
                    cycleTime = "all"
                };
                var jsonContent = JsonConvert.SerializeObject(requestParam);

                HttpResponseMessage response;
                using (var content = new StringContent(jsonContent, Encoding.UTF8, "application/json"))
                {
                    response = await _httpClient.PostAsync(url, content);
                }
                
                // 如果返回401或403，表示token无效
                if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized || 
                    response.StatusCode == System.Net.HttpStatusCode.Forbidden)
                {
                    Debug.WriteLine($"Token无效，服务器返回: {response.StatusCode}");
                    return false;
                }
                
                Debug.WriteLine($"Token有效，服务器返回: {response.StatusCode}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"测试Token有效性时出错: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 测试查询API
        /// </summary>
        /// <returns>测试结果信息</returns>
        public async Task<string> TestQueryApiAsync()
        {
            try
            {
                Debug.WriteLine("开始测试查询API...");
                
                // 确保已设置令牌
                if (string.IsNullOrEmpty(_token))
                {
                    return "未设置认证令牌，请先登录";
                }
                
                // 确保HttpClient实例可用
                if (_httpClient == null)
                {
                    Debug.WriteLine("HttpClient为null，重新初始化");
                    InitializeHttpClient();
                }
                
                // 检查token有效性并刷新
                if (AuthManager.IsLoggedIn && !string.IsNullOrEmpty(AuthManager.CurrentAuth?.AccessToken))
                {
                    _token = AuthManager.CurrentAuth.AccessToken;
                    
                    // 清除所有现有的Authorization头
                    if (_httpClient.DefaultRequestHeaders.Contains("Authorization"))
                    {
                        _httpClient.DefaultRequestHeaders.Remove("Authorization");
                    }
                    
                    // 重新设置Authorization头部
                    _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _token);
                    Debug.WriteLine($"刷新Authorization头: Bearer {_token.Substring(0, Math.Min(10, _token.Length))}...");
                }
                
                // 测试API端点 - 使用正确的安全测试API
                var url = $"{_baseUrl}/safetyTest/getPictureBySafetyTestIds";
                Debug.WriteLine($"测试URL: {url}");

                // 构造安全测试请求参数（使用真实的安全测试ID）
                var requestParam = new {
                    safetyTestIds = "1907295047497265154",
                    cycleTime = "all"
                };
                var jsonContent = JsonConvert.SerializeObject(requestParam);

                using (var client = new HttpClient())
                {
                    // 设置基本请求头
                    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    client.DefaultRequestHeaders.Add("X-Requested-With", "XMLHttpRequest");

                    // 使用正确的认证方式 (仅Authorization头，不需要JiraAuth)
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _token);

                    HttpResponseMessage response;
                    using (var content = new StringContent(jsonContent, Encoding.UTF8, "application/json"))
                    {
                        response = await client.PostAsync(url, content);
                    }

                    if (response.IsSuccessStatusCode)
                    {
                        var responseContent = await response.Content.ReadAsStringAsync();
                        Debug.WriteLine($"API响应: {responseContent}");

                        var apiResponse = JsonConvert.DeserializeObject<ResponseData>(responseContent);
                        if (apiResponse.Success)
                        {
                            Debug.WriteLine("API调用成功!");
                            return "API测试成功";
                        }
                        else
                        {
                            Debug.WriteLine($"API返回错误: {apiResponse.Message}");
                            return $"API返回错误: {apiResponse.Message}";
                        }
                    }
                    else
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        Debug.WriteLine($"HTTP错误: {response.StatusCode}, 内容: {errorContent}");
                        return $"HTTP错误: {response.StatusCode}";
                    }

                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"测试API时出错: {ex.Message}");
                return $"测试API时出错: {ex.Message}";
            }
        }
        
        /// <summary>
        /// 使用正确的token验证方式发送请求
        /// </summary>
        public async Task<T> SendWithTokenAuthAsync<T>(string url, HttpMethod method, object data = null)
        {
            using (var client = new HttpClient())
            {
                // 设置基本请求头
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                client.DefaultRequestHeaders.Add("X-Requested-With", "XMLHttpRequest");
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _token);

                try
                {
                    HttpResponseMessage response;

                    if (method == HttpMethod.Get)
                    {
                        response = await client.GetAsync(url);
                    }
                    else
                    {
                        // 准备JSON数据并创建StringContent
                        string jsonData = data != null ? JsonConvert.SerializeObject(data) : "{}";
                        using (var content = new StringContent(jsonData, Encoding.UTF8, "application/json"))
                        {
                            response = await client.PostAsync(url, content);
                        }
                    }

                    var responseContent = await response.Content.ReadAsStringAsync();
                    Debug.WriteLine($"响应状态: {response.StatusCode}");

                    if (!response.IsSuccessStatusCode)
                    {
                        if (response.StatusCode == HttpStatusCode.Unauthorized)
                        {
                            throw new UnauthorizedAccessException("认证失败，请重新登录");
                        }
                        throw new HttpRequestException($"请求失败: {response.StatusCode}");
                    }

                    // 尝试解析为ResponseData格式
                    try
                    {
                        var apiResponse = JsonConvert.DeserializeObject<ResponseData>(responseContent);
                        if (apiResponse != null && apiResponse.Success)
                        {
                            if (typeof(T) == typeof(object))
                            {
                                return (T)(object)apiResponse.Data;
                            }
                            else if (apiResponse.Data != null)
                            {
                                return JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(apiResponse.Data));
                            }
                        }
                        else
                        {
                            throw new Exception( "API调用失败");
                        }
                    }
                    catch (JsonException)
                    {
                        // 如果不是ResponseData格式，直接解析为目标类型
                        return JsonConvert.DeserializeObject<T>(responseContent);
                    }

                    return default(T);
                }
                catch (UnauthorizedAccessException)
                {
                    // 重新抛出未授权异常
                    throw;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"请求失败: {ex.Message}");
                    throw;
                }
            }
        }

        /// <summary>
        /// 后端API统一响应格式
        /// </summary>
        public class ResponseData
        {
            [JsonProperty("code")]
            public int Code { get; set; }
            
            [JsonProperty("success")]
            public bool Success { get; set; }
            
            [JsonProperty("message")]
            public string Message { get; set; }
            
            [JsonProperty("data")]
            public object Data { get; set; }
            
            /// <summary>
            /// 辅助方法，从响应内容直接解析
            /// </summary>
            public static ResponseData FromJson(string json)
            {
                try
                {
                    return JsonConvert.DeserializeObject<ResponseData>(json);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"解析响应失败: {ex.Message}");
                    return new ResponseData
                    {
                        Success = false,
                        Message = $"解析响应失败: {ex.Message}",
                        Data = json
                    };
                }
            }
        }
        
        /// <summary>
        /// 诊断登录API，尝试多种参数组合
        /// </summary>
        /// <param name="account">账号</param>
        /// <param name="password">密码</param>
        /// <returns>诊断结果</returns>
        public async Task<string> DiagnoseLoginApiAsync(string account, string password)
        {
            Debug.WriteLine("开始诊断登录API...");
            
            // 确保HttpClient实例可用
            if (_httpClient == null)
            {
                InitializeHttpClient();
            }
            
            StringBuilder result = new StringBuilder();
            result.AppendLine("登录API诊断结果：");
            
            // 尝试的参数组合
            var paramCombinations = new List<Dictionary<string, string>>
            {
                new Dictionary<string, string> { { "account", account }, { "password", password } },
                new Dictionary<string, string> { { "username", account }, { "password", password } },
                new Dictionary<string, string> { { "loginName", account }, { "password", password } },
                new Dictionary<string, string> { { "loginId", account }, { "password", password } }
            };
            
            // 尝试的API路径
            var apiPaths = new[]
            {
                "/login",
                "/auth/login",
                "/user/login",
                "/sys/login",
                "/api/auth/login",
                "/api/login"
            };
            
            foreach (var apiPath in apiPaths)
            {
                result.AppendLine($"\n尝试API路径: {apiPath}");
                
                foreach (var paramDict in paramCombinations)
                {
                    var paramNames = string.Join(", ", paramDict.Keys);
                    result.AppendLine($"  参数组合: {paramNames}");
                    
                    try
                    {
                        var json = JsonConvert.SerializeObject(paramDict);

                        // 记录完整请求URL和数据
                        var requestUrl = $"{_baseUrl}{apiPath}";
                        Debug.WriteLine($"诊断请求URL: {requestUrl}, 数据: {json}");

                        // 发送请求
                        HttpResponseMessage response;
                        using (var content = new StringContent(json, Encoding.UTF8, "application/json"))
                        {
                            response = await _httpClient.PostAsync(requestUrl, content);
                        }
                        
                        // 处理响应
                        var responseContent = await response.Content.ReadAsStringAsync();
                        var statusCode = response.StatusCode;
                        
                        result.AppendLine($"    状态码: {statusCode}");
                        result.AppendLine($"    响应: {responseContent.Substring(0, Math.Min(100, responseContent.Length))}...");
                        
                        // 检查是否成功
                        if (response.IsSuccessStatusCode)
                        {
                            try
                            {
                                var apiResponse = ResponseData.FromJson(responseContent);
                                if (apiResponse.Success)
                                {
                                    result.AppendLine($"    成功! API路径: {apiPath}, 参数: {paramNames}");
                                    result.AppendLine($"    完整响应: {responseContent}");
                                }
                            }
                            catch { }
                        }
                    }
                    catch (Exception ex)
                    {
                        result.AppendLine($"    错误: {ex.Message}");
                    }
                }
            }
            
            return result.ToString();
        }

        /// <summary>
        /// 获取Minio文件下载URL
        /// </summary>
        public async Task<string> GetMinioDownloadUrlAsync(string fileId, string fileName)
        {
            try
            {
                // 确保已设置令牌
                EnsureTokenSet();
                
                if (string.IsNullOrEmpty(fileId))
                {
                    Debug.WriteLine("文件ID为空，无法获取下载URL");
                    return null;
                }
                
                // 构建API URL
                string encodedFileName = Uri.EscapeDataString(fileName);
                string url = $"{_baseUrl}/sysFileInfo/minioDownload/{fileId}?fileName={encodedFileName}";
                Debug.WriteLine($"请求下载URL: {url}");
                
                // 发送请求
                var response = await _httpClient.GetAsync(url);
                
                if (!response.IsSuccessStatusCode)
                {
                    Debug.WriteLine($"获取下载URL失败: {response.StatusCode}");
                    return null;
                }
                
                // 解析响应
                var content = await response.Content.ReadAsStringAsync();
                Debug.WriteLine($"下载URL响应: {(content.Length > 100 ? content.Substring(0, 100) + "..." : content)}");
                
                var responseData = JsonConvert.DeserializeObject<ResponseData>(content);
                
                if (responseData?.Success != true || responseData.Data == null)
                {
                    Debug.WriteLine($"API返回错误: {responseData?.Message}");
                    return null;
                }
                
                Debug.WriteLine($"获取到下载URL: {responseData.Data.ToString()}");
                return responseData.Data.ToString();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取文件下载URL时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取Minio文件预览URL
        /// </summary>
        public async Task<string> GetMinioPreviewUrlAsync(string fileId)
        {
            try
            {
                // 确保已设置令牌
                EnsureTokenSet();
                
                if (string.IsNullOrEmpty(fileId))
                {
                    Debug.WriteLine("文件ID为空，无法获取预览URL");
                    return null;
                }
                
                // 构建API URL
                string url = $"{_baseUrl}/sysFileInfo/minioPreview/{fileId}";
                Debug.WriteLine($"请求预览URL: {url}");
                
                // 发送请求
                var response = await _httpClient.GetAsync(url);
                
                if (!response.IsSuccessStatusCode)
                {
                    Debug.WriteLine($"获取预览URL失败: {response.StatusCode}");
                    return null;
                }
                
                // 解析响应
                var content = await response.Content.ReadAsStringAsync();
                Debug.WriteLine($"预览URL响应: {(content.Length > 100 ? content.Substring(0, 100) + "..." : content)}");
                
                var responseData = JsonConvert.DeserializeObject<ResponseData>(content);
                
                if (responseData?.Success != true || responseData.Data == null)
                {
                    Debug.WriteLine($"API返回错误: {responseData?.Message}");
                    return null;
                }
                
                string previewUrl = responseData.Data.ToString();
                if (previewUrl.Contains("10.100.1.99:9000"))
                {
                    // 替换为可访问的URL
                    previewUrl = previewUrl.Replace("http://10.100.1.99:9000/", "/minioDownload/");
                }
                
                Debug.WriteLine($"获取到预览URL: {previewUrl}");
                return previewUrl;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取文件预览URL时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 下载图片数据
        /// </summary>
        public async Task<byte[]> DownloadImageDataAsync(string url)
        {
            try
            {
                // 处理URL，但不转换为代理路径，直接使用原始Minio URL
                string processedUrl = ProcessImageUrlForDirectAccess(url);

                // 对于Minio URL，直接访问原始地址
                if (processedUrl.StartsWith("http://10.100.1.99:9000/"))
                {
                    // 直接访问Minio服务器，不需要认证
                    using (var client = new HttpClient())
                    {
                        var response = await client.GetAsync(processedUrl);

                        if (!response.IsSuccessStatusCode)
                        {
                            Debug.WriteLine($"Minio直接下载失败: {response.StatusCode}");
                            return null;
                        }

                        var imageData = await response.Content.ReadAsByteArrayAsync();
                        return imageData;
                    }
                }
                else if (processedUrl.StartsWith("/api/"))
                {
                    // API路径需要认证
                    EnsureTokenSet();

                    // 构建完整URL
                    string fullUrl = $"{_baseUrl}{processedUrl}";
                    Debug.WriteLine($"使用认证下载图片: {fullUrl}");

                    var response = await _httpClient.GetAsync(fullUrl);

                    if (!response.IsSuccessStatusCode)
                    {
                        Debug.WriteLine($"认证下载图片失败: {response.StatusCode}");
                        return null;
                    }

                    var imageData = await response.Content.ReadAsByteArrayAsync();
                    Debug.WriteLine($"成功下载图片数据，大小: {imageData.Length} 字节");
                    return imageData;
                }
                else
                {
                    // 其他外部URL，创建新的HttpClient
                    using (var client = new HttpClient())
                    {
                        Debug.WriteLine($"准备下载外部图片: {processedUrl}");
                        var response = await client.GetAsync(processedUrl);

                        if (!response.IsSuccessStatusCode)
                        {
                            Debug.WriteLine($"下载外部图片失败: {response.StatusCode}");
                            return null;
                        }

                        var imageData = await response.Content.ReadAsByteArrayAsync();
                        Debug.WriteLine($"成功下载外部图片数据，大小: {imageData.Length} 字节");
                        return imageData;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"下载图片数据时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 处理图片URL，模拟Vue前端的URL转换逻辑（用于代理访问）
        /// </summary>
        private string ProcessImageUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
            {
                return url;
            }

            Debug.WriteLine($"原始URL: {url}");

            // 1. 模拟Vue前端的URL转换：将Minio直接URL转换为代理URL
            if (url.Contains("http://10.100.1.99:9000/"))
            {
                string convertedUrl = url.Replace("http://10.100.1.99:9000/", "/minioDownload/");
                Debug.WriteLine($"Minio URL转换: {url} -> {convertedUrl}");
                return convertedUrl;
            }

            // 2. 处理包含Authorization参数的URL，移除该参数
            // 因为Vue前端对/minioDownload/路径不发送Authorization头
            if (url.Contains("Authorization=Bearer") || url.Contains("Authorization%3DBearer"))
            {
                try
                {
                    var uri = new Uri(url);
                    var query = System.Web.HttpUtility.ParseQueryString(uri.Query);

                    // 移除Authorization相关参数
                    query.Remove("Authorization");

                    var uriBuilder = new UriBuilder(uri)
                    {
                        Query = query.ToString()
                    };

                    string cleanUrl = uriBuilder.ToString();
                    Debug.WriteLine($"移除Authorization参数: {url} -> {cleanUrl}");

                    // 再次检查是否需要Minio URL转换
                    if (cleanUrl.Contains("http://10.100.1.99:9000/"))
                    {
                        cleanUrl = cleanUrl.Replace("http://10.100.1.99:9000/", "/minioDownload/");
                        Debug.WriteLine($"清理后再次转换: {cleanUrl}");
                    }

                    return cleanUrl;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"处理Authorization参数时出错: {ex.Message}");
                    // 如果URL解析失败，尝试简单的字符串替换
                    string fallbackUrl = url;
                    if (fallbackUrl.Contains("http://10.100.1.99:9000/"))
                    {
                        fallbackUrl = fallbackUrl.Replace("http://10.100.1.99:9000/", "/minioDownload/");
                    }
                    return fallbackUrl;
                }
            }

            Debug.WriteLine($"URL无需转换: {url}");
            return url;
        }

        /// <summary>
        /// 处理图片URL用于直接访问（不转换为代理路径）
        /// </summary>
        private string ProcessImageUrlForDirectAccess(string url)
        {
            if (string.IsNullOrEmpty(url))
            {
                return url;
            }

            // 对于Minio URL，保持原始地址不变，只移除Authorization参数（如果存在）
            if (url.Contains("http://10.100.1.99:9000/"))
            {
                // 如果URL包含Authorization参数，移除它
                if (url.Contains("Authorization=Bearer") || url.Contains("Authorization%3DBearer"))
                {
                    try
                    {
                        var uri = new Uri(url);
                        var query = System.Web.HttpUtility.ParseQueryString(uri.Query);

                        // 移除Authorization相关参数
                        query.Remove("Authorization");

                        var uriBuilder = new UriBuilder(uri)
                        {
                            Query = query.ToString()
                        };

                        string cleanUrl = uriBuilder.ToString();
                        return cleanUrl;
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"处理Authorization参数时出错: {ex.Message}");
                        return url; // 返回原始URL
                    }
                }

                return url; // 保持原始Minio URL
            }

            return url;
        }

        /// <summary>
        /// 使用Minio API获取文件预览URL（参照Vue前端实现）
        /// </summary>
        public async Task<string> GetMinioFileUrlAsync(string fileId)
        {
            try
            {
                // 确保已设置令牌
                EnsureTokenSet();

                if (string.IsNullOrEmpty(fileId))
                {
                    Debug.WriteLine("文件ID为空，无法获取预览URL");
                    return null;
                }

                // 构建API URL - 参照Vue前端的实现
                string url = $"{_baseUrl}/minioFile/getFileUrl?fileId={Uri.EscapeDataString(fileId)}";

                Debug.WriteLine($"请求Minio预览URL: {url}");

                // 发送POST请求
                var response = await _httpClient.PostAsync(url, null);

                if (!response.IsSuccessStatusCode)
                {
                    Debug.WriteLine($"获取Minio预览URL失败: {response.StatusCode}");
                    return null;
                }

                var content = await response.Content.ReadAsStringAsync();
                Debug.WriteLine($"Minio预览URL响应: {content}");

                var responseData = JsonConvert.DeserializeObject<ResponseData>(content);

                if (responseData?.Success != true || responseData.Data == null)
                {
                    Debug.WriteLine($"API返回错误: {responseData?.Message}");
                    return null;
                }

                string minioUrl = responseData.Data.ToString();

                // 应用URL处理逻辑（使用直接访问方式）
                string processedUrl = ProcessImageUrlForDirectAccess(minioUrl);

                return processedUrl;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取Minio文件预览URL时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 根据文件ID下载图片数据
        /// </summary>
        public async Task<byte[]> DownloadImageDataByFileIdAsync(string fileId, string mode = "little")
        {
            try
            {
                Debug.WriteLine($"开始下载图片，文件ID: {fileId}, 模式: {mode}");

                // 方法1: 使用Minio预览API（参照Vue前端实现）
                string previewUrl = await GetMinioFileUrlAsync(fileId);
                if (!string.IsNullOrEmpty(previewUrl))
                {
                    var imageData = await DownloadImageDataAsync(previewUrl);
                    if (imageData != null && imageData.Length > 1000) // 正常图片应该大于1KB
                    {
                        return imageData;
                    }
                }

                // 方法2: 使用Minio下载API
                string downloadUrl = await GetMinioFileDownloadUrlAsync(fileId, null);
                if (!string.IsNullOrEmpty(downloadUrl))
                {
                    var imageData = await DownloadImageDataAsync(downloadUrl);
                    if (imageData != null && imageData.Length > 1000)
                    {
                        return imageData;
                    }
                }

                // 方法3: 降级到原有的sysFileInfo预览方式
                Debug.WriteLine("Minio API方式都失败，使用原有的sysFileInfo预览方式");

                // 确保已设置令牌
                EnsureTokenSet();

                if (string.IsNullOrEmpty(fileId))
                {
                    Debug.WriteLine("文件ID为空，无法下载图片");
                    return null;
                }

                // 构建图片预览URL
                string url = $"{_baseUrl}/api/sysFileInfo/preview?id={Uri.EscapeDataString(fileId)}&mode={Uri.EscapeDataString(mode)}";
                Debug.WriteLine($"使用sysFileInfo预览下载图片: {url}");

                var response = await _httpClient.GetAsync(url);

                if (!response.IsSuccessStatusCode)
                {
                    Debug.WriteLine($"sysFileInfo预览下载失败: {response.StatusCode}");
                    return null;
                }

                var fallbackImageData = await response.Content.ReadAsByteArrayAsync();
                Debug.WriteLine($"sysFileInfo预览方式完成，图片大小: {fallbackImageData.Length} 字节");
                return fallbackImageData;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"根据文件ID下载图片数据时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取Minio文件下载URL（参照Vue前端实现）
        /// </summary>
        public async Task<string> GetMinioFileDownloadUrlAsync(string fileId, string fileName = null)
        {
            try
            {
                // 确保已设置令牌
                EnsureTokenSet();

                if (string.IsNullOrEmpty(fileId))
                {
                    Debug.WriteLine("文件ID为空，无法获取下载URL");
                    return null;
                }

                // 构建API URL - 参照Vue前端的实现
                string url = $"{_baseUrl}/minioFile/getDownloadUrl?fileId={Uri.EscapeDataString(fileId)}";
                if (!string.IsNullOrEmpty(fileName))
                {
                    url += $"&fileName={Uri.EscapeDataString(fileName)}";
                }

                Debug.WriteLine($"请求Minio下载URL: {url}");

                // 发送POST请求
                var response = await _httpClient.PostAsync(url, null);

                if (!response.IsSuccessStatusCode)
                {
                    Debug.WriteLine($"获取Minio下载URL失败: {response.StatusCode}");
                    return null;
                }

                // 解析响应
                var content = await response.Content.ReadAsStringAsync();
                Debug.WriteLine($"Minio下载URL响应: {(content.Length > 100 ? content.Substring(0, 100) + "..." : content)}");

                var responseData = JsonConvert.DeserializeObject<ResponseData>(content);

                if (responseData?.Success != true || responseData.Data == null)
                {
                    Debug.WriteLine($"API返回错误: {responseData?.Message}");
                    return null;
                }

                string downloadUrl = responseData.Data.ToString();
                Debug.WriteLine($"获取到Minio下载URL: {downloadUrl}");
                return downloadUrl;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取Minio文件下载URL时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 下载图片
        /// </summary>
        /// <param name="url">图片URL</param>
        /// <returns>图片二进制数据</returns>
        public async Task<byte[]> DownloadImageAsync(string url)
        {
            try
            {
                Debug.WriteLine($"开始下载图片: {url}");

                using (var client = new HttpClient())
                {
                    // 设置认证头
                    if (!string.IsNullOrEmpty(_token))
                    {
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _token);
                    }

                    var response = await client.GetAsync(url);
                    if (response.IsSuccessStatusCode)
                    {
                        var imageData = await response.Content.ReadAsByteArrayAsync();
                        Debug.WriteLine($"图片下载成功，大小: {imageData.Length} bytes");
                        return imageData;
                    }
                    else
                    {
                        Debug.WriteLine($"图片下载失败，状态码: {response.StatusCode}");
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"下载图片时出错: {ex.Message}");
                return null;
            }
        }
    }
}