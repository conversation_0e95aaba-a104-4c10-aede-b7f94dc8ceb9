using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Linq;
// 移除Microsoft.Office.Tools.PowerPoint引用，因为找不到对应的程序集
// using Microsoft.Office.Tools.PowerPoint;
using PBIppt.Utils;
using PBIppt.UI;
using Office = Microsoft.Office.Core;
using PowerPoint = Microsoft.Office.Interop.PowerPoint;
using System.Windows.Forms;
using System.Reflection;
using System.Runtime.InteropServices;
using Microsoft.Office.Tools;

namespace PBIppt
{
    public partial class ThisAddIn
    {
        private Office.IRibbonExtensibility _ribbon;

        private void ThisAddIn_Startup(object sender, System.EventArgs e)
        {
            try
            {
                Logger.Info("PowerPoint Battery Plugin started");
                
                // 预先创建功能区对象
                _ribbon = new Ribbon.BatteryRibbon();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"插件初始化失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ThisAddIn_Shutdown(object sender, System.EventArgs e)
        {
            try
            {
                Logger.Info("PowerPoint Battery Plugin shutdown");

                // 释放所有跟踪的COM对象
                ComObjectManager.ReleaseAll();

                // 强制垃圾回收
                ComObjectManager.ForceGarbageCollection();

                Logger.Info("COM对象清理完成");
            }
            catch (Exception ex)
            {
                Logger.Warning($"插件关闭时清理COM对象失败: {ex.Message}");
                // Suppress shutdown errors
            }
        }

        /// <summary>
        /// Returns the ribbon for the add-in
        /// </summary>
        protected override Microsoft.Office.Core.IRibbonExtensibility CreateRibbonExtensibilityObject()
        {
            try
            {
                if (_ribbon == null)
                {
                    _ribbon = new Ribbon.BatteryRibbon();
                }
                
                return _ribbon;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建功能区失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        #region VSTO 生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InternalStartup()
        {
            this.Startup += new System.EventHandler(ThisAddIn_Startup);
            this.Shutdown += new System.EventHandler(ThisAddIn_Shutdown);
        }
        
        #endregion
    }
}
