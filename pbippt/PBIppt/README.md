# PowerPoint电池图片表格插件

此项目是PowerPoint插件，用于生成电池图片表格和处理电池图片。

## 项目结构

```
PBIppt/
├── ApiClient/            # API客户端
│   ├── BatteryApiClient相关文件
│   └── BatteryImageApiClient相关文件
├── Batch/                # 批处理
│   └── BatchProcessor.cs
├── ImageProcessing/      # 图像处理
│   ├── ImageExtractor.cs
│   └── ImageCropper.cs
├── Models/               # 数据模型
│   ├── 基础模型
│   ├── 图片表格功能模型
│   └── 认证相关模型
├── Ribbon/               # Ribbon界面
│   ├── BatteryRibbon.xml
│   └── BatteryRibbon.cs
├── UI/                   # 用户界面
│   ├── LoginForm.cs
│   ├── QueryForm.cs
│   └── TableGenerator.cs
├── Utils/                # 工具类
│   ├── Logger.cs
│   ├── ProgressManager.cs     # 无弹窗进度管理器
│   └── BatteryImageHelper.cs  # 图片处理辅助类
├── Tests/                # 测试代码
│   └── BatteryImageHelperTests.cs
├── ThisAddIn.cs          # 插件入口点
└── PBIppt.csproj         # 项目文件
```

## 功能模块

1. **电池检测功能**
   - 处理单张图片
   - 处理当前幻灯片
   - 处理整个演示文稿

2. **图片表格生成功能**
   - 用户登录认证
   - 查询参数选择
   - 生成包含电池图片的表格

3. **日历寿命图片获取功能**（新增，参照Vue前端实现）
   - 调用日历寿命测试进度接口获取图片
   - **完全参照Vue前端的Minio API实现**
   - 支持多种图片下载方式（Minio预览URL、下载URL、直接文件ID等）
   - 智能降级处理，确保高成功率
   - 自动处理认证和错误重试
   - 支持按条件过滤图片（存储天数、样品编号、图片类型）
   - 提供图片摘要和统计信息

## PPT表格生成功能使用说明

### 基本用法

```csharp
// 1. 创建API客户端
var apiClient = new BatteryImageApiClient("http://your-backend-url");

// 2. 使用新版PPT表格生成功能
var pptTableQueryForm = new PptTableQueryForm(apiClient, currentSlide, progressManager);
pptTableQueryForm.ShowDialog();
```

### 功能特点

- 支持委托单号选择
- 支持测试项目选择
- 支持照片部位多选
- 支持样品编号多选
- 支持测试阶段选择

var filteredImages = BatteryImageHelper.FilterImagesByQuery(imageData, queryParams);
```

### 测试功能

```csharp
// 运行原有功能测试
var tester = new TestApiIntegration("http://your-backend-url");
var result = await tester.TestImageDownloadAsync("username", "password", 1, "all");

// 测试新的Minio API功能（参照Vue前端）
var minioResult = await tester.TestMinioApiImageDownloadAsync("username", "password", 1, "all");

// 运行专门的Minio API测试
var minioTests = await MinioApiTests.TestCompleteImageFlowAsync("http://your-backend-url", "username", "password", 1, "all");

// 对比新旧方式的性能和成功率
var comparison = await MinioApiTests.CompareDownloadMethodsAsync("http://your-backend-url", "username", "password", 1, "all");

// 运行BatteryImageHelper测试
var testResult = await BatteryImageHelperTests.RunAllTestsAsync();
```

## 开发说明

- 使用Visual Studio 2019或更高版本打开解决方案
- 需要安装VSTO (Visual Studio Tools for Office)
- 调试时会自动启动PowerPoint并加载插件 