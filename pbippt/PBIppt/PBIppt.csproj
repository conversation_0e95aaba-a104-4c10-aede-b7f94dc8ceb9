<Project ToolsVersion="17.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <ProjectTypeGuids>{BAA0C2D2-18E2-41B9-852F-F413020CAA33};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{817897C7-9F90-4F5C-93DC-BC1A0097D946}</ProjectGuid>
    <OutputType>Library</OutputType>
    <NoStandardLibraries>false</NoStandardLibraries>
    <RootNamespace>PBIppt</RootNamespace>
    <AssemblyName>PBIppt</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <DefineConstants>VSTO40;UseOfficeInterop</DefineConstants>
    <ResolveComReferenceSilent>true</ResolveComReferenceSilent>
    <IsWebBootstrapper>False</IsWebBootstrapper>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <PublishUrl>E:\</PublishUrl>
    <InstallUrl />
    <TargetCulture>zh-chs</TargetCulture>
    <ApplicationVersion>*******</ApplicationVersion>
    <AutoIncrementApplicationRevision>true</AutoIncrementApplicationRevision>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>days</UpdateIntervalUnits>
    <ProductName>PBIppt</ProductName>
    <PublisherName>PBIppt</PublisherName>
    <SupportUrl />
    <FriendlyName>PBIppt</FriendlyName>
    <OfficeApplicationDescription>PBI的ppt插件</OfficeApplicationDescription>
    <LoadBehavior>3</LoadBehavior>
  </PropertyGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.7.2">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.7.2 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.VSTORuntime.4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft Visual Studio 2010 Tools for Office Runtime %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <PropertyGroup>
    <OfficeApplication>PowerPoint</OfficeApplication>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <DefineConstants>$(DefineConstants);DEBUG;TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <DefineConstants>$(DefineConstants);TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Accessibility" />
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Office.Tools.v4.0.Framework, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.VisualStudio.Tools.Applications.Runtime, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Tools, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Tools.Common, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Office.Tools.Common.v4.0.Utilities, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <Choose>
    <When Condition="$([System.String]::Copy(&quot;;$(DefineConstants);&quot;).ToLower().Contains(';useofficeinterop;')) or $([System.String]::Copy(&quot;,$(DefineConstants),&quot;).ToLower().Contains(',useofficeinterop,'))">
      <ItemGroup>
        <Reference Include="Office, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c">
          <Private>False</Private>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </Reference>
        <Reference Include="Microsoft.Office.Interop.PowerPoint, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c">
          <Private>False</Private>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </Reference>
        <Reference Include="Microsoft.Office.Interop.Excel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c">
          <Private>False</Private>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </Reference>
      </ItemGroup>
    </When>
    <Otherwise>
      <ItemGroup>
        <COMReference Include="Microsoft.Office.Core">
          <Guid>{2DF8D04C-5BFA-101B-BDE5-00AA0044DE52}</Guid>
          <VersionMajor>2</VersionMajor>
          <VersionMinor>7</VersionMinor>
          <Lcid>0</Lcid>
          <WrapperTool>tlbimp</WrapperTool>
          <Isolated>False</Isolated>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </COMReference>
        <COMReference Include="Microsoft.Office.Interop.PowerPoint">
          <Guid>{91493440-5A91-11CF-8700-00AA0060263B}</Guid>
          <VersionMajor>2</VersionMajor>
          <VersionMinor>11</VersionMinor>
          <Lcid>0</Lcid>
          <WrapperTool>tlbimp</WrapperTool>
          <Isolated>False</Isolated>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </COMReference>
        <COMReference Include="Microsoft.Office.Interop.Excel">
          <Guid>{00020813-0000-0000-C000-000000000046}</Guid>
          <VersionMajor>1</VersionMajor>
          <VersionMinor>9</VersionMinor>
          <Lcid>0</Lcid>
          <WrapperTool>tlbimp</WrapperTool>
          <Isolated>False</Isolated>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </COMReference>
      </ItemGroup>
    </Otherwise>
  </Choose>
  <ItemGroup>
    <Reference Include="stdole, Version=7.0.3300.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ApiClient\IBatteryApiClient.cs" />
    <Compile Include="ApiClient\IBatteryImageApiClient.cs" />
    <Compile Include="ApiClient\BatteryImageApiClient.cs" />
    <Compile Include="ApiClient\IBatteryDetectionApiClient.cs" />
    <Compile Include="ApiClient\BatteryDetectionApiClient.cs" />
    <Compile Include="ApiClient\IAuthApiClient.cs" />

    <Compile Include="Batch\BatchProcessor.cs" />
    <Compile Include="ImageProcessing\ImageCropper.cs" />
    <Compile Include="ImageProcessing\ImageExtractor.cs" />
    <Compile Include="ImageProcessing\PowerPointCropHelper.cs" />
    <Compile Include="ImageProcessing\BatteryDetectionProcessor.cs" />
    <Compile Include="ImageProcessing\BatteryTableImageProcessor.cs" />
    <Compile Include="Models\Battery.cs" />
    <Compile Include="Models\BatteryDetectionResult.cs" />

    <Compile Include="Models\ImageData.cs" />
    <Compile Include="Models\TestProgress.cs" />
    <Compile Include="Models\AuthModels.cs" />
    <Compile Include="Models\RoleConstants.cs" />
    <Compile Include="Models\EnvironmentConfig.cs" />
    <Compile Include="Models\PptTableData.cs" />
    <Compile Include="Services\AuthService.cs" />
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Ribbon\BatteryRibbon.cs" />
    <Compile Include="ThisAddIn.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="UI\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>

    <Compile Include="UI\PptTableQueryForm.cs" />

    <Compile Include="UI\PptTableGenerator.cs" />
    <Compile Include="Utils\Logger.cs" />
    <Compile Include="Utils\ProgressManager.cs" />

    <Compile Include="Utils\ComObjectManager.cs" />
    <Compile Include="Utils\ComObjectManagerTest.cs" />
    <!-- 甘特图Models（v2.0共享） -->
    <Compile Include="GanttChart\Models\GanttEnums.cs" />
    <Compile Include="GanttChart\Models\GanttTask.cs" />
    <Compile Include="GanttChart\Models\GanttMilestone.cs" />
    <Compile Include="GanttChart\Models\TaskDependency.cs" />
    <Compile Include="GanttChart\Models\TimelineSettings.cs" />
    <Compile Include="GanttChart\Models\GanttProject.cs" />
    <Compile Include="GanttChart\Models\ProjectHealthReport.cs" />
    <!-- 甘特图Core（向后兼容） -->
    <Compile Include="GanttChart\Core\GanttEngine.cs" />
    <!-- 甘特图Utils（向后兼容） -->
    <Compile Include="GanttChart\Utils\GanttLayout.cs" />
    <Compile Include="GanttChart\Utils\LayoutHelper.cs" />
    <!-- 甘特图v2.0文件 -->
    <Compile Include="GanttChart\v2\Components\IGanttChartComponent.cs" />
    <Compile Include="GanttChart\v2\Components\GanttChartComponent.cs" />
    <Compile Include="GanttChart\v2\Events\EventBusSystem.cs" />
    <Compile Include="GanttChart\v2\Events\GanttEvents.cs" />
    <Compile Include="GanttChart\v2\State\StateManager.cs" />
    <Compile Include="GanttChart\v2\Interaction\AdvancedDragDrop\AdvancedDragDropManager.cs" />
    <Compile Include="GanttChart\v2\Interaction\AdvancedDragDrop\DragConstraints.cs" />
    <Compile Include="GanttChart\v2\Interaction\AdvancedDragDrop\DragPreviewRenderer.cs" />
    <Compile Include="GanttChart\v2\Interaction\ContextMenu\ContextMenuFramework.cs" />
    <Compile Include="GanttChart\v2\Interaction\ContextMenu\MenuProviders.cs" />
    <Compile Include="GanttChart\v2\Interaction\DirectEdit\InlineEditor.cs" />
    <Compile Include="GanttChart\v2\Interaction\DirectEdit\DoubleClickEditor.cs" />
    <Compile Include="GanttChart\v2\Interaction\Keyboard\KeyboardEventHandler.cs" />
    <Compile Include="GanttChart\v2\Interaction\Keyboard\KeyboardActions.cs" />
    <Compile Include="GanttChart\v2\Interaction\Keyboard\KeyboardNavigator.cs" />
    <Compile Include="GanttChart\v2\Interaction\Selection\AdvancedSelectionManager.cs" />
    <Compile Include="GanttChart\v2\Interaction\Selection\SelectionSupport.cs" />
    <Compile Include="GanttChart\v2\Visual\VisualEffectsSystem.cs" />
    <Compile Include="GanttChart\v2\Visual\VisualEffectsSupport.cs" />
    <Compile Include="GanttChart\v2\Visual\AnimationManager.cs" />
    <Compile Include="GanttChart\v2\Rendering\UnifiedGanttRenderer.cs" />
    <Compile Include="GanttChart\v2\Rendering\SmartLayout\SmartLayoutAlgorithm.cs" />
    <Compile Include="GanttChart\v2\Rendering\SmartLayout\SmartLayoutSupport.cs" />
    <Compile Include="GanttChart\v2\Rendering\SmartLayout\LayoutOptimizer.cs" />
    <Compile Include="GanttChart\v2\Rendering\SmartLayout\AdaptiveLayoutManager.cs" />
    <Compile Include="GanttChart\v2\Integration\PowerPointIntegration.cs" />
    <Compile Include="GanttChart\v2\Integration\PowerPointIntegrationSupport.cs" />
    <Compile Include="GanttChart\v2\Tests\GanttV2TestFramework.cs" />
    <Compile Include="GanttChart\v2\Tests\TestSupport.cs" />
    <Compile Include="GanttChart\v2\Tests\TestRunner.cs" />
    <Compile Include="GanttChart\v2\Examples\GanttV2Demo.cs" />
    <EmbeddedResource Include="Ribbon\BatteryRibbon.xml">
      <LogicalName>PBIppt.Ribbon.BatteryRibbon.xml</LogicalName>
    </EmbeddedResource>
    <None Include="app.config" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <None Include="PBIppt_TemporaryKey.pfx" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <None Include="ThisAddIn.Designer.xml">
      <DependentUpon>ThisAddIn.cs</DependentUpon>
    </None>
    <Compile Include="ThisAddIn.Designer.cs">
      <DependentUpon>ThisAddIn.Designer.xml</DependentUpon>
    </Compile>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>true</SignManifests>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>PBIppt_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>7E63F85B78490082428AD9B53A57692992ADC452</ManifestCertificateThumbprint>
  </PropertyGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\OfficeTools\Microsoft.VisualStudio.Tools.Office.targets" Condition="'$(VSToolsPath)' != ''" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{BAA0C2D2-18E2-41B9-852F-F413020CAA33}">
        <ProjectProperties HostName="PowerPoint" HostPackage="{29A7B9D7-A7F1-4328-8EF0-6B2D1A56B2C1}" OfficeVersion="15.0" VstxVersion="4.0" ApplicationType="PowerPoint" Language="cs" TemplatesPath="" DebugInfoExeName="#Software\Microsoft\Office\16.0\PowerPoint\InstallRoot\Path#powerpnt.exe" AddItemTemplatesGuid="{51063C3A-E220-4D12-8922-BDA915ACD783}" />
        <Host Name="PowerPoint" GeneratedCodeNamespace="PBIppt" PublishedHash="69C324AB27932AA2FBF2B7EA72250886FF164DE6" IconIndex="0">
          <HostItem Name="ThisAddIn" Code="ThisAddIn.cs" CanonicalName="AddIn" PublishedHash="9D56F417260DCAC4615F27E640B024EB158EF773" CanActivate="false" IconIndex="1" Blueprint="ThisAddIn.Designer.xml" GeneratedCode="ThisAddIn.Designer.cs" />
        </Host>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>