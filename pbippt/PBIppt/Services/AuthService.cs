using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PBIppt.Models;
using System.Diagnostics;
using System.Configuration;

namespace PBIppt.Services
{
    /// <summary>
    /// 认证服务接口
    /// </summary>
    public interface IAuthService
    {
        /// <summary>
        /// 登录并获取用户信息和角色
        /// </summary>
        Task<LoginResponse> LoginAsync(string account, string password);

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        Task<UserInfo> GetCurrentUserAsync();

        /// <summary>
        /// 验证Token有效性
        /// </summary>
        Task<bool> ValidateTokenAsync();
    }

    /// <summary>
    /// 认证服务实现
    /// </summary>
    public class AuthService : IAuthService
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;

        public AuthService()
        {
            _baseUrl = ConfigurationManager.AppSettings["ApiBaseUrl"] ?? "http://localhost:82";
            _baseUrl = _baseUrl.TrimEnd('/');

            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            Debug.WriteLine($"AuthService 初始化，API地址: {_baseUrl}");
        }

        /// <summary>
        /// 登录并获取用户信息和角色
        /// </summary>
        public async Task<LoginResponse> LoginAsync(string account, string password)
        {
            try
            {
                Debug.WriteLine($"开始登录流程，账号: {account}");

                // 1. 调用登录API获取Token
                var loginResponse = await CallLoginApiAsync(account, password);
                if (!loginResponse.Success)
                {
                    return loginResponse;
                }

                // 2. 设置认证头并获取用户信息和角色
                SetAuthorizationHeader(loginResponse.GetFullToken());
                var userInfo = await GetCurrentUserAsync();
                if (userInfo?.Roles != null)
                {
                    loginResponse.Roles = userInfo.Roles;
                    Debug.WriteLine($"成功获取用户角色信息，角色数量: {userInfo.Roles.Count}");
                }

                // 3. 保存到AuthManager
                AuthManager.SetCurrentAuth(loginResponse);
                Debug.WriteLine("认证信息已保存到AuthManager");

                return loginResponse;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"登录过程中发生错误: {ex.Message}");
                return new LoginResponse
                {
                    Success = false,
                    ErrorMessage = $"登录失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 调用登录API获取Token
        /// </summary>
        private async Task<LoginResponse> CallLoginApiAsync(string account, string password)
        {
            try
            {
                var loginRequest = new { account, password };
                var json = JsonConvert.SerializeObject(loginRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                Debug.WriteLine($"发送登录请求到: {_baseUrl}/login");
                var response = await _httpClient.PostAsync($"{_baseUrl}/login", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                Debug.WriteLine($"登录API响应状态: {response.StatusCode}");

                if (!response.IsSuccessStatusCode)
                {
                    return new LoginResponse
                    {
                        Success = false,
                        ErrorMessage = $"登录请求失败: {response.StatusCode}"
                    };
                }

                var apiResponse = JsonConvert.DeserializeObject<ResponseData>(responseContent);
                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    return ParseLoginResponse(apiResponse.Data, account);
                }

                return new LoginResponse
                {
                    Success = false,
                    ErrorMessage = apiResponse?.Message ?? "登录失败"
                };
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"调用登录API时发生错误: {ex.Message}");
                return new LoginResponse
                {
                    Success = false,
                    ErrorMessage = $"网络错误: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 解析登录响应数据
        /// </summary>
        private LoginResponse ParseLoginResponse(object data, string account)
        {
            try
            {
                var jObject = data as JObject;
                if (jObject == null)
                {
                    return new LoginResponse
                    {
                        Success = false,
                        ErrorMessage = "登录响应格式错误"
                    };
                }

                var jwtToken = jObject["jwtToken"]?.ToString();
                var username = jObject["username"]?.ToString();
                var expired = jObject["expired"]?.ToObject<int>() ?? 86400;

                Debug.WriteLine($"解析登录响应: jwtToken存在={!string.IsNullOrEmpty(jwtToken)}, username={username}");

                return new LoginResponse
                {
                    Success = !string.IsNullOrEmpty(jwtToken),
                    AccessToken = jwtToken,
                    TokenType = "Bearer",
                    ExpiresIn = expired,
                    Username = username ?? account
                };
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"解析登录响应时发生错误: {ex.Message}");
                return new LoginResponse
                {
                    Success = false,
                    ErrorMessage = $"解析登录响应失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        public async Task<UserInfo> GetCurrentUserAsync()
        {
            try
            {
                Debug.WriteLine("开始获取当前用户信息");

                var response = await _httpClient.GetAsync($"{_baseUrl}/getLoginUser");
                if (!response.IsSuccessStatusCode)
                {
                    Debug.WriteLine($"获取用户信息失败: {response.StatusCode}");
                    return null;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonConvert.DeserializeObject<ResponseData>(responseContent);

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    var userInfo = JsonConvert.DeserializeObject<UserInfo>(apiResponse.Data.ToString());
                    Debug.WriteLine($"成功获取用户信息: {userInfo?.Username}, 角色数量: {userInfo?.Roles?.Count ?? 0}");
                    return userInfo;
                }

                Debug.WriteLine("获取用户信息失败");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取用户信息时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 验证Token有效性 - 使用专门的用户信息接口验证
        /// </summary>
        public async Task<bool> ValidateTokenAsync()
        {
            try
            {
                if (!AuthManager.IsLoggedIn)
                {
                    Debug.WriteLine("Token为空，无法测试有效性");
                    return false;
                }

                SetAuthorizationHeader(AuthManager.GetAuthorizationHeader());
                Debug.WriteLine($"测试Token有效性: {_baseUrl}/getLoginUser");

                var response = await _httpClient.GetAsync($"{_baseUrl}/getLoginUser");
                bool isValid = response.IsSuccessStatusCode;

                if (!isValid)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Debug.WriteLine($"Token有效性验证结果: {isValid}, 服务器返回: {responseContent}");
                }

                return isValid;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"验证Token有效性时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置HTTP客户端的认证头
        /// </summary>
        private void SetAuthorizationHeader(string authorizationHeader)
        {
            if (!string.IsNullOrEmpty(authorizationHeader))
            {
                _httpClient.DefaultRequestHeaders.Authorization = AuthenticationHeaderValue.Parse(authorizationHeader);
                Debug.WriteLine($"设置认证头: {authorizationHeader}");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
