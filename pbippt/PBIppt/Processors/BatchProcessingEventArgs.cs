using System;

namespace PBIppt.Processors
{
    /// <summary>
    /// 批处理开始事件参数
    /// </summary>
    public class BatchProcessingStartedEventArgs : EventArgs
    {
        /// <summary>
        /// 总项目数
        /// </summary>
        public int TotalItems { get; set; }
        
        /// <summary>
        /// 创建新的事件参数
        /// </summary>
        public BatchProcessingStartedEventArgs(int totalItems)
        {
            TotalItems = totalItems;
        }
    }
    
    /// <summary>
    /// 批处理项目完成事件参数
    /// </summary>
    public class BatchItemProcessedEventArgs : EventArgs
    {
        /// <summary>
        /// 已处理项目数
        /// </summary>
        public int ProcessedItems { get; set; }
        
        /// <summary>
        /// 总项目数
        /// </summary>
        public int TotalItems { get; set; }
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 创建新的事件参数
        /// </summary>
        public BatchItemProcessedEventArgs(int processedItems, int totalItems, bool success, string errorMessage = null)
        {
            ProcessedItems = processedItems;
            TotalItems = totalItems;
            Success = success;
            ErrorMessage = errorMessage;
        }
    }
    
    /// <summary>
    /// 批处理完成事件参数
    /// </summary>
    public class BatchProcessingCompletedEventArgs : EventArgs
    {
        /// <summary>
        /// 成功数量
        /// </summary>
        public int SuccessCount { get; set; }
        
        /// <summary>
        /// 失败数量
        /// </summary>
        public int FailureCount { get; set; }
        
        /// <summary>
        /// 总项目数
        /// </summary>
        public int TotalItems { get; set; }
        
        /// <summary>
        /// 创建新的事件参数
        /// </summary>
        public BatchProcessingCompletedEventArgs(int successCount, int failureCount, int totalItems)
        {
            SuccessCount = successCount;
            FailureCount = failureCount;
            TotalItems = totalItems;
        }
    }
} 