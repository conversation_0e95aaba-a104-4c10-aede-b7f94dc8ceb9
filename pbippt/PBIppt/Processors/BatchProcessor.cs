using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PowerPoint = Microsoft.Office.Interop.PowerPoint;
using PBIppt.ApiClient;
using PBIppt.Models;
using PBIppt.Utils;

namespace PBIppt.Processors
{
    /// <summary>
    /// 批量处理器，用于处理多个幻灯片中的图像
    /// </summary>
    public class BatchProcessor
    {
        private readonly IBatteryImageApiClient _apiClient;
        
        // 事件
        public event EventHandler<BatchProcessingStartedEventArgs> ProcessingStarted;
        public event EventHandler<BatchItemProcessedEventArgs> ItemProcessed;
        public event EventHandler<BatchProcessingCompletedEventArgs> ProcessingCompleted;
        
        /// <summary>
        /// 创建批量处理器
        /// </summary>
        public BatchProcessor(IBatteryImageApiClient apiClient)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
        }
    }
} 