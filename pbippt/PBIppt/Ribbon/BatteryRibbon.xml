<?xml version="1.0" encoding="UTF-8"?>
<customUI xmlns="http://schemas.microsoft.com/office/2006/01/customui" onLoad="Ribbon_Load">
  <ribbon>
    <tabs>
      <tab id="tabBatteryDetection" label="PBI-ppt">
        <group id="BatteryDetectionGroup" label="电芯检测">
          <button id="btnDetectBattery"
                  label="检测电芯"
                  size="large"
                  imageMso="PictureCrop"
                  onAction="OnDetectBatteryClick"
                  screentip="电芯检测"
                  supertip="选择图片后点击此按钮，自动检测电芯位置并裁剪。检测完成后可手动调整裁剪区域。"/>

          <button id="btnResetCrop"
                  label="重置裁剪"
                  size="normal"
                  imageMso="PictureResetPicture"
                  onAction="OnResetCropClick"
                  screentip="重置图片裁剪"
                  supertip="清除当前图片的所有裁剪设置，恢复原始图片"/>
        </group>


        
        <group id="TableGroup" label="表格">
          <button id="btnGenerateTable" 
                  label="生成电池图片表格" 
                  size="large"
                  imageMso="TableInsert"
                  onAction="OnGenerateTableClick" 
                  screentip="生成电池图片表格" 
                  supertip="从后端获取电池图片数据，生成表格展示"/>
          
          <button id="btnLogin" 
                  label="登录/切换用户" 
                  size="normal"
                  imageMso="AddAccount"
                  onAction="OnLoginClick" 
                  screentip="用户登录" 
                  supertip="登录系统或切换当前用户"/>
        </group>

        <group id="GanttGroup" label="甘特图">
          <splitButton id="btnCreateGantt" size="large">
            <button id="btnCreateGanttMain"
                    label="创建甘特图"
                    imageMso="ChartInsert"
                    onAction="OnCreateGanttClick"
                    screentip="创建新的甘特图"
                    supertip="在当前幻灯片中插入一个新的甘特图"/>
            <menu id="menuCreateGantt">
              <button id="btnCreateFromTemplate"
                      label="从模板创建"
                      imageMso="TemplatesOnline"
                      onAction="OnCreateFromTemplateClick"
                      screentip="从模板创建甘特图"
                      supertip="选择预定义模板快速创建甘特图"/>
              <button id="btnCreateBlank"
                      label="创建空白图表"
                      imageMso="ChartInsert"
                      onAction="OnCreateBlankGanttClick"
                      screentip="创建空白甘特图"
                      supertip="创建一个空白的甘特图"/>
            </menu>
          </splitButton>

          <button id="btnEditGantt"
                  label="编辑甘特图"
                  size="normal"
                  imageMso="TablePropertiesDialog"
                  onAction="OnEditGanttClick"
                  screentip="编辑甘特图"
                  supertip="编辑选中的甘特图数据和属性"/>

          <button id="btnImportExcel"
                  label="从Excel导入"
                  size="normal"
                  imageMso="ImportExcel"
                  onAction="OnImportExcelClick"
                  screentip="从Excel导入数据"
                  supertip="从Excel文件导入任务和里程碑数据"/>

          <button id="btnExportExcel"
                  label="导出到Excel"
                  size="normal"
                  imageMso="ExportExcel"
                  onAction="OnExportExcelClick"
                  screentip="导出到Excel"
                  supertip="将甘特图数据导出到Excel文件"/>

          <button id="btnRefreshGantt"
                  label="刷新甘特图"
                  size="normal"
                  imageMso="Refresh"
                  onAction="OnRefreshGanttClick"
                  screentip="刷新甘特图"
                  supertip="重新计算关键路径和项目进度"/>

          <separator id="separator2"/>

          <button id="btnAnalyzeProject"
                  label="项目分析"
                  size="normal"
                  imageMso="ChartTrendLinesLinear"
                  onAction="OnAnalyzeProjectClick"
                  screentip="项目健康度分析"
                  supertip="分析项目状态、关键路径和性能"/>

          <button id="btnOptimizeGantt"
                  label="性能优化"
                  size="normal"
                  imageMso="Performance"
                  onAction="OnOptimizeGanttClick"
                  screentip="性能优化"
                  supertip="优化大型甘特图的显示性能"/>
        </group>

        <group id="CropTestGroup" label="裁剪测试">
          <button id="btnCropTestDpi"
                  label="DPI修复测试"
                  size="normal"
                  imageMso="PictureCrop"
                  onAction="OnCropTestDpiClick"
                  screentip="基于DPI的裁剪测试"
                  supertip="使用API返回的DPI信息进行精确的坐标转换测试"/>
        </group>

        <group id="HelpGroup" label="帮助">
          <button id="AboutButton"
                  label="关于"
                  size="normal"
                  onAction="OnAbout" />


        </group>
      </tab>
    </tabs>
  </ribbon>
</customUI> 