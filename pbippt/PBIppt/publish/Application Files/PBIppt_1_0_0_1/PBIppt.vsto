<?xml version="1.0" encoding="utf-8"?>
<asmv1:assembly xsi:schemaLocation="urn:schemas-microsoft-com:asm.v1 assembly.adaptive.xsd" manifestVersion="1.0" xmlns:asmv1="urn:schemas-microsoft-com:asm.v1" xmlns="urn:schemas-microsoft-com:asm.v2" xmlns:asmv2="urn:schemas-microsoft-com:asm.v2" xmlns:xrml="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:co.v1="urn:schemas-microsoft-com:clickonce.v1" xmlns:co.v2="urn:schemas-microsoft-com:clickonce.v2">
  <assemblyIdentity name="PBIppt.vsto" version="*******" publicKeyToken="d3af86de3c7fb742" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <description asmv2:publisher="PBIppt" asmv2:product="PowerPoint电池识别插件" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <deployment install="false" mapFileExtensions="true" />
  <compatibleFrameworks xmlns="urn:schemas-microsoft-com:clickonce.v2">
    <framework targetVersion="4.7.2" profile="Full" supportedRuntime="4.0.30319" />
  </compatibleFrameworks>
  <dependency>
    <dependentAssembly dependencyType="install" codebase="Application Files\PBIppt_1_0_0_1\PBIppt.dll.manifest" size="12797">
      <assemblyIdentity name="PBIppt.dll" version="*******" publicKeyToken="d3af86de3c7fb742" language="neutral" processorArchitecture="msil" type="win32" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>F+JSBes7EQ7n/vgTfn2lMOnTi7U5UgaU5zS4J0QGch0=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
<publisherIdentity name="CN=EVE\071716" issuerKeyHash="3151950d1a3f63514e5c3ad29f1869a719b42855" /><Signature Id="StrongNameSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>UV6bY7PKuBEwN0RY7wLCeYjXcnXlxN7RUNpujvBNFsY=</DigestValue></Reference></SignedInfo><SignatureValue>WyGjRPO1VZFOf6s7d5sX32uDeXXa59KXXSzZIXmMEjapjpNhGPy3CPCiUqHK8FnPIxLFuIySSRTHp5zTNMqkp5Si/QuX6PiWQSIR5XEfYYRdGj1dhZ0Utl5gqkydKRt6dNnS083wDIcI4s9L78n7SpocaXE6BhK6lohysYqBYqM=</SignatureValue><KeyInfo Id="StrongNameKeyInfo"><KeyValue><RSAKeyValue><Modulus>7OStg4ejnwgQFmDFixqWE4TV0jLfDP2Cwe4VFHb53QFCNcJmKmFEtdh1W20Y/NmVOWd/fhDQjPkDuu9XhdanmUCVBga0+d1Mn0kjWqmsf9estHhNYZD8U6dPiQuXtKCy/aDuG0EeSZJNK1hfMZlyQBOMY4oXwOUoMbKzz2PWaKU=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><msrel:RelData xmlns:msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:as="http://schemas.microsoft.com/windows/pki/2005/Authenticode"><r:grant><as:ManifestInformation Hash="c6164df08e6eda50d1dec4e57572d78879c202ef5844373011b8cab3639b5e51" Description="" Url=""><as:assemblyIdentity name="PBIppt.vsto" version="*******" publicKeyToken="d3af86de3c7fb742" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" /></as:ManifestInformation><as:SignedBy /><as:AuthenticodePublisher><as:X509SubjectName>CN=EVE\071716</as:X509SubjectName></as:AuthenticodePublisher></r:grant><r:issuer><Signature Id="AuthenticodeSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>BGhX5/g03BMVaHzljE7NqBOx+xbbMt4zGeVz6rfC/fo=</DigestValue></Reference></SignedInfo><SignatureValue>jYNTaWDUSllHDGdkGV7fUEy1VUhjpv+zMSjoCSwW8J7AECTG5bwuaKI3Ai6ZR4lhONNjgV8eQzfOtf9OEWeSrk1uK8+lWhVOUTH3nAhS54+4TzT6drJ9gOFg3nzX41mVBqo3++0Spx+S6OZnqK3h8KsE4Ey7ajjKHkT6bXOKSIg=</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>7OStg4ejnwgQFmDFixqWE4TV0jLfDP2Cwe4VFHb53QFCNcJmKmFEtdh1W20Y/NmVOWd/fhDQjPkDuu9XhdanmUCVBga0+d1Mn0kjWqmsf9estHhNYZD8U6dPiQuXtKCy/aDuG0EeSZJNK1hfMZlyQBOMY4oXwOUoMbKzz2PWaKU=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>MIIBwTCCASqgAwIBAgIQLZs7+UUk/ZlNyc59aBwysDANBgkqhkiG9w0BAQsFADAfMR0wGwYDVQQDHhQARQBWAEUAXAAwADcAMQA3ADEANjAeFw0yNTA2MTkwODIzMDJaFw0yNjA2MTkxNDIzMDJaMB8xHTAbBgNVBAMeFABFAFYARQBcADAANwAxADcAMQA2MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDs5K2Dh6OfCBAWYMWLGpYThNXSMt8M/YLB7hUUdvndAUI1wmYqYUS12HVbbRj82ZU5Z39+ENCM+QO671eF1qeZQJUGBrT53UyfSSNaqax/16y0eE1hkPxTp0+JC5e0oLL9oO4bQR5Jkk0rWF8xmXJAE4xjihfA5SgxsrPPY9ZopQIDAQABMA0GCSqGSIb3DQEBCwUAA4GBAEgTbXl96lf8pdN7EIxDF3AWI1FphAidTPCJ1GAQLfWSaf7rfRWX7na/r5oNQ+Cjc/VZFmtKES95SFYI02dcFfbkINRuBnd8lDbqGYBN5KW2TAFxJfWQ1sKRBWHBvxa6gyS35ztbYTrngHjfJHqdHHGCDzcBXgRTQk/+qcDm1jil</X509Certificate></X509Data></KeyInfo></Signature></r:issuer></r:license></msrel:RelData></KeyInfo></Signature></asmv1:assembly>