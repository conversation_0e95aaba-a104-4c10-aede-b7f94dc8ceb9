# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a PowerPoint VSTO add-in project written in C# that provides battery image processing and table generation functionality for PowerPoint presentations. The solution consists of two main projects:

- **PBIppt**: Main PowerPoint add-in project (.NET Framework 4.7.2)
- **PBIppt.Tests**: Test project (.NET 6.0)
- **CropTest**: Utility console application for image processing

## Build Commands

### Main Project (PBIppt)
```bash
# Build the solution
dotnet build PBIppt.sln

# Build in Debug mode
dotnet build PBIppt.sln --configuration Debug

# Build in Release mode
dotnet build PBIppt.sln --configuration Release

# Clean build artifacts
dotnet clean PBIppt.sln
```

### Tests
```bash
# Run all tests
dotnet test PBIppt.Tests

# Run tests with verbose output
dotnet test PBIppt.Tests --verbosity normal

# Run specific test class
dotnet test PBIppt.Tests --filter "FullyQualifiedName~BatteryImageApiClientTests"
```

### VSTO Deployment
The project uses ClickOnce deployment for VSTO add-ins. Build artifacts are published to the `publish/` directory.

## Architecture Overview

### Core Components

1. **Add-in Entry Point** (`ThisAddIn.cs`): PowerPoint VSTO add-in startup and shutdown logic
2. **Ribbon Interface** (`Ribbon/BatteryRibbon.cs`): PowerPoint ribbon UI integration
3. **API Clients** (`ApiClient/`): HTTP clients for backend communication
4. **Image Processing** (`ImageProcessing/`): Battery detection and image manipulation
5. **UI Components** (`UI/`): Windows Forms for user interaction
6. **Models** (`Models/`): Data transfer objects and business entities
7. **Utilities** (`Utils/`): Logging, progress management, and COM object handling

### Key Architectural Patterns

- **Interface-based design**: All API clients implement interfaces for testability
- **COM object management**: Strict COM object lifecycle management for Office interop
- **Event-driven UI**: Progress reporting through event handlers
- **Modular structure**: Clear separation between UI, business logic, and API communication

### Dependencies

- **.NET Framework 4.7.2** (main project)
- **Microsoft Office Interop** (PowerPoint, Excel)
- **VSTO Runtime** (Visual Studio Tools for Office)
- **Newtonsoft.Json** for JSON serialization
- **System.Net.Http** for HTTP communication

## Development Workflow

### Local Development
1. Open `PBIppt.sln` in Visual Studio
2. Ensure PowerPoint is installed for debugging
3. Build the solution (F6)
4. Press F5 to start debugging - PowerPoint will launch with the add-in loaded

### Configuration
- API endpoints configured in `app.config` under `ApiBaseUrl`
- Role-based permissions configured via role IDs in app settings
- Default base URL: `http://localhost:82`

### COM Object Management
This project heavily uses Office interop which requires careful COM object management:
- Use `ComObjectManager.Track()` for all Office objects
- Always call `ComObjectManager.ReleaseAll()` on shutdown
- See `Utils/COM对象管理指南.md` for detailed guidelines

### Testing Strategy
- **Unit Tests**: API client behavior and data processing logic
- **Integration Tests**: End-to-end workflow testing
- **UI Tests**: Form validation and user interaction testing
- Tests use MSTest framework with Moq for mocking

## Special Features

### Gantt Chart Component (v2.0)
The project includes an advanced Gantt chart system with:
- Component-based architecture (`GanttChart/v2/Components/`)
- Event bus system for component communication
- Advanced drag-drop with constraints
- Keyboard navigation and context menus
- Smart layout algorithms with optimization

### Battery Image Processing
- Automated battery detection in images
- Image cropping and extraction
- Integration with PowerPoint slide manipulation
- Support for batch processing workflows

### Multi-Environment Support
- Development, staging, and production API endpoints
- Role-based feature access control
- Configurable authentication mechanisms

## Common Development Tasks

### Adding New API Endpoints
1. Define interface in appropriate `ApiClient/I*.cs` file
2. Implement in corresponding `ApiClient/*.cs` file
3. Add models to `Models/` if needed
4. Write tests in `PBIppt.Tests/ApiClientTests/`

### Extending UI Functionality
1. Create Windows Form in `UI/` directory
2. Implement progress reporting using `ProgressManager`
3. Handle COM objects properly with `ComObjectManager`
4. Add ribbon button in `Ribbon/BatteryRibbon.xml` if needed

### Image Processing Features
1. Extend `ImageProcessing/` classes
2. Use existing patterns for PowerPoint integration
3. Implement proper error handling and logging
4. Consider batch processing requirements

## Important Notes

- Always use `ComObjectManager` for Office interop objects to prevent memory leaks
- Log all major operations using the `Logger` utility
- Follow existing patterns for progress reporting in long-running operations
- API authentication is handled through the `AuthService` class
- The project supports both Chinese and English localization